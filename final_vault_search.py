#!/usr/bin/env python3
"""
Final Targeted Vault Search
Searches for the exact MetaMask vault format as used by the official decryptor
"""

import os
import re
import json
import base64
from typing import List, Dict, Any

def search_exact_vault_format(filepath: str) -> List[Dict[str, Any]]:
    """Search for the exact vault format used by MetaMask based on official guide"""
    print(f"Searching {filepath} for exact vault format...")

    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []

    # Convert to text
    try:
        text = data.decode('utf-8', errors='ignore')
    except:
        return []

    vault_candidates = []

    # 1. Look for the EXACT format from the official guide: {"data":"...","iv":"...","salt":"..."}
    # This is the most specific pattern based on the example you provided
    exact_pattern = r'\{"data":"([A-Za-z0-9+/=]{100,})","iv":"([A-Za-z0-9+/=]{20,})","salt":"([A-Za-z0-9+/=]{40,})"\}'
    matches = re.findall(exact_pattern, text)

    for match in matches:
        data_val, iv_val, salt_val = match
        vault_candidates.append({
            'type': 'OFFICIAL_FORMAT',
            'data': data_val,
            'iv': iv_val,
            'salt': salt_val,
            'source': filepath
        })
        print(f"✅ Found OFFICIAL format vault in {filepath}")

    # 2. Look for the format with any whitespace variations
    whitespace_pattern = r'\{\s*"data"\s*:\s*"([A-Za-z0-9+/=]{100,})"\s*,\s*"iv"\s*:\s*"([A-Za-z0-9+/=]{20,})"\s*,\s*"salt"\s*:\s*"([A-Za-z0-9+/=]{40,})"\s*\}'
    whitespace_matches = re.findall(whitespace_pattern, text)

    for match in whitespace_matches:
        data_val, iv_val, salt_val = match
        vault_candidates.append({
            'type': 'WHITESPACE_VARIANT',
            'data': data_val,
            'iv': iv_val,
            'salt': salt_val,
            'source': filepath
        })
        print(f"✅ Found whitespace variant vault in {filepath}")

    # 3. Look for variations with different field orders but same structure
    variations = [
        r'\{"iv":"([A-Za-z0-9+/=]{20,})","data":"([A-Za-z0-9+/=]{100,})","salt":"([A-Za-z0-9+/=]{40,})"\}',
        r'\{"salt":"([A-Za-z0-9+/=]{40,})","data":"([A-Za-z0-9+/=]{100,})","iv":"([A-Za-z0-9+/=]{20,})"\}',
        r'\{"salt":"([A-Za-z0-9+/=]{40,})","iv":"([A-Za-z0-9+/=]{20,})","data":"([A-Za-z0-9+/=]{100,})"\}',
        r'\{"data":"([A-Za-z0-9+/=]{100,})","salt":"([A-Za-z0-9+/=]{40,})","iv":"([A-Za-z0-9+/=]{20,})"\}',
        r'\{"iv":"([A-Za-z0-9+/=]{20,})","salt":"([A-Za-z0-9+/=]{40,})","data":"([A-Za-z0-9+/=]{100,})"\}',
    ]

    field_orders = [
        ['iv', 'data', 'salt'],
        ['salt', 'data', 'iv'],
        ['salt', 'iv', 'data'],
        ['data', 'salt', 'iv'],
        ['iv', 'salt', 'data']
    ]

    for i, pattern in enumerate(variations):
        matches = re.findall(pattern, text)
        for match in matches:
            order = field_orders[i]
            vault_data = {}
            for j, field in enumerate(order):
                vault_data[field] = match[j]

            vault_candidates.append({
                'type': f'FIELD_ORDER_VARIANT_{i+1}',
                'data': vault_data['data'],
                'iv': vault_data['iv'],
                'salt': vault_data['salt'],
                'source': filepath
            })
            print(f"✅ Found field order variant {i+1} vault in {filepath}")

    # 4. Look for vault data with escaped quotes (common in stored JSON)
    escaped_patterns = [
        r'\{\\"data\\":\\"([A-Za-z0-9+/=]{100,})\\",\\"iv\\":\\"([A-Za-z0-9+/=]{20,})\\",\\"salt\\":\\"([A-Za-z0-9+/=]{40,})\\"\}',
        r'\\{"data\\":\\"([A-Za-z0-9+/=]{100,})\\",\\"iv\\":\\"([A-Za-z0-9+/=]{20,})\\",\\"salt\\":\\"([A-Za-z0-9+/=]{40,})\\"\\}',
    ]

    for i, pattern in enumerate(escaped_patterns):
        matches = re.findall(pattern, text)
        for match in matches:
            data_val, iv_val, salt_val = match
            vault_candidates.append({
                'type': f'ESCAPED_FORMAT_{i+1}',
                'data': data_val,
                'iv': iv_val,
                'salt': salt_val,
                'source': filepath
            })
            print(f"✅ Found escaped format {i+1} vault in {filepath}")

    # 5. Look for vault data within larger JSON structures (KeyringController, etc.)
    nested_patterns = [
        r'"vault"\s*:\s*\{"data":"([A-Za-z0-9+/=]{100,})","iv":"([A-Za-z0-9+/=]{20,})","salt":"([A-Za-z0-9+/=]{40,})"\}',
        r'"KeyringController"\s*:\s*\{[^}]*"vault"\s*:\s*\{"data":"([A-Za-z0-9+/=]{100,})","iv":"([A-Za-z0-9+/=]{20,})","salt":"([A-Za-z0-9+/=]{40,})"\}',
        r'"data"\s*:\s*\{[^}]*"vault"\s*:\s*\{"data":"([A-Za-z0-9+/=]{100,})","iv":"([A-Za-z0-9+/=]{20,})","salt":"([A-Za-z0-9+/=]{40,})"\}',
    ]

    for i, pattern in enumerate(nested_patterns):
        matches = re.findall(pattern, text, re.DOTALL)
        for match in matches:
            data_val, iv_val, salt_val = match
            vault_candidates.append({
                'type': f'NESTED_FORMAT_{i+1}',
                'data': data_val,
                'iv': iv_val,
                'salt': salt_val,
                'source': filepath
            })
            print(f"✅ Found nested format {i+1} vault in {filepath}")

    # 6. Look for partial matches - sometimes the JSON might be split across lines
    # Look for the three components separately and try to match them
    data_pattern = r'"data"\s*:\s*"([A-Za-z0-9+/=]{100,})"'
    iv_pattern = r'"iv"\s*:\s*"([A-Za-z0-9+/=]{20,})"'
    salt_pattern = r'"salt"\s*:\s*"([A-Za-z0-9+/=]{40,})"'

    data_matches = re.findall(data_pattern, text)
    iv_matches = re.findall(iv_pattern, text)
    salt_matches = re.findall(salt_pattern, text)

    # If we found all three components, try to pair them
    if data_matches and iv_matches and salt_matches:
        for data_val in data_matches:
            for iv_val in iv_matches:
                for salt_val in salt_matches:
                    vault_candidates.append({
                        'type': 'COMPONENT_MATCH',
                        'data': data_val,
                        'iv': iv_val,
                        'salt': salt_val,
                        'source': filepath,
                        'note': 'Found components separately - may need manual verification'
                    })
                    print(f"✅ Found vault components separately in {filepath}")
                    break  # Only take first combination
                break
            break

    return vault_candidates

def validate_vault_data(vault_data: Dict[str, str]) -> bool:
    """Validate that vault data has the correct format based on official example"""
    required_fields = ['data', 'iv', 'salt']

    # Check all required fields are present
    if not all(field in vault_data for field in required_fields):
        return False

    # Check that all fields are base64-encoded strings
    for field in required_fields:
        try:
            decoded = base64.b64decode(vault_data[field])
            # Ensure it's not empty after decoding
            if len(decoded) == 0:
                return False
        except:
            return False

    # Check lengths based on the official example:
    # data: "wwpXXtFCqZkYsWfeEwItZjJ0Cc7mRVjG47Dqh+ztL1PiCG6Izhg+zG0mM+H2ykyjz3X0RNhAE6IVsWFZamcZ47B4sVi4SvUxrMhARm5L3yHPxr3UsyGrOXmthyVMgEGmjwlmnFCNd2nMZ2o8/sRMra8FupurqevnBv57FiYpEEs7gPpFHv6587aL44MmKD8Snv4JLFqiqmlK82Waq5F+Iv9mw2sFVAL9mgZBSgFgbWdB3TsKVB2k" (length: 344)
    # iv: "rkUQlNcGTxBE0My7a/bCXw==" (length: 24)
    # salt: "HcKyNfGzaRALRQ0DlKgcIe5Uk30iI/M//oG6w8vX8Nk=" (length: 44)

    data_len = len(vault_data['data'])
    iv_len = len(vault_data['iv'])
    salt_len = len(vault_data['salt'])

    # Data should be substantial (encrypted seed phrase) - based on example: ~344 chars
    # Allow range from 200 to 500 characters
    if data_len < 200 or data_len > 500:
        return False

    # IV should be around 24 characters (16 bytes base64 encoded)
    # Allow range from 20 to 30 characters
    if iv_len < 20 or iv_len > 30:
        return False

    # Salt should be around 44 characters (32 bytes base64 encoded)
    # Allow range from 40 to 50 characters
    if salt_len < 40 or salt_len > 50:
        return False

    return True

def main():
    """Main search function"""
    print("Final Targeted MetaMask Vault Search")
    print("=" * 50)
    
    # Get all .ldb files
    ldb_files = [f for f in os.listdir('.') if f.endswith('.ldb')]
    
    if not ldb_files:
        print("No .ldb files found")
        return
    
    print(f"Searching {len(ldb_files)} .ldb files for exact vault format...")
    
    all_vault_candidates = []
    
    for ldb_file in ldb_files:
        candidates = search_exact_vault_format(ldb_file)
        all_vault_candidates.extend(candidates)
    
    print(f"\n{'='*60}")
    print("FINAL SEARCH RESULTS")
    print(f"{'='*60}")
    
    print(f"Total vault candidates found: {len(all_vault_candidates)}")
    
    if all_vault_candidates:
        valid_vaults = []
        
        for i, candidate in enumerate(all_vault_candidates):
            print(f"\nCandidate {i+1}:")
            print(f"  Type: {candidate['type']}")
            print(f"  Source: {candidate['source']}")
            
            if candidate['type'] != 'SINGLE_BASE64':
                # Validate the vault data
                vault_data = {
                    'data': candidate['data'],
                    'iv': candidate['iv'],
                    'salt': candidate['salt']
                }
                
                is_valid = validate_vault_data(vault_data)
                print(f"  Valid format: {is_valid}")
                
                if is_valid:
                    valid_vaults.append((i+1, vault_data))
                    print(f"  Data length: {len(candidate['data'])}")
                    print(f"  IV length: {len(candidate['iv'])}")
                    print(f"  Salt length: {len(candidate['salt'])}")
                    print(f"  Data preview: {candidate['data'][:50]}...")
                else:
                    print(f"  Data length: {len(candidate['data'])}")
                    print(f"  IV length: {len(candidate['iv'])}")
                    print(f"  Salt length: {len(candidate['salt'])}")
            else:
                print(f"  Decoded length: {candidate['decoded_length']}")
                print(f"  Data preview: {candidate['data'][:50]}...")
        
        # Save valid vaults
        if valid_vaults:
            print(f"\n🎉 FOUND {len(valid_vaults)} VALID VAULT(S)!")
            
            for i, (candidate_num, vault_data) in enumerate(valid_vaults):
                # Save as JSON
                vault_filename = f"valid_vault_{i+1}.json"
                with open(vault_filename, 'w') as f:
                    json.dump(vault_data, f, indent=2)
                
                # Save as text for copy-paste
                vault_text_filename = f"valid_vault_{i+1}.txt"
                with open(vault_text_filename, 'w') as f:
                    f.write(json.dumps(vault_data))
                
                print(f"\nValid Vault {i+1} (from candidate {candidate_num}):")
                print(f"  ✅ Saved to: {vault_filename}")
                print(f"  ✅ Text format: {vault_text_filename}")
            
            # Show instructions
            print(f"\n🎯 INSTRUCTIONS:")
            print(f"1. The MetaMask vault decryptor is already open in your browser")
            print(f"2. Copy the content from valid_vault_1.txt:")
            
            # Show the first valid vault for copy-paste
            first_vault = valid_vaults[0][1]
            print(f"\n📋 COPY THIS TEXT:")
            print(f"{'='*60}")
            print(json.dumps(first_vault))
            print(f"{'='*60}")
            
            print(f"\n3. Paste it into the 'Vault Data' field in the decryptor")
            print(f"4. Enter your MetaMask password")
            print(f"5. Click 'Decrypt' to recover your seed phrase")
            
        else:
            print(f"\n⚠️  Found vault candidates but none passed validation")
            print(f"This might mean:")
            print(f"  - The data is corrupted")
            print(f"  - The format is slightly different")
            print(f"  - The data needs manual cleaning")
            
            # Save all candidates anyway for manual inspection
            with open('all_vault_candidates.json', 'w') as f:
                json.dump(all_vault_candidates, f, indent=2, default=str)
            
            print(f"  - All candidates saved to: all_vault_candidates.json")
    
    else:
        print(f"\n❌ No vault data found in the expected format")
        print(f"\nThis could mean:")
        print(f"  1. The .ldb files are not from MetaMask's local storage")
        print(f"  2. The vault data is stored in a different format")
        print(f"  3. The files are corrupted or incomplete")
        print(f"  4. MetaMask was not set up on this browser profile")
        
        print(f"\n💡 Alternative approaches:")
        print(f"  1. Check if you have other .ldb files from different browser profiles")
        print(f"  2. Look for files with different numbers (000001.ldb, 000002.ldb, etc.)")
        print(f"  3. Check the browser's extension storage directly")

if __name__ == "__main__":
    main()
