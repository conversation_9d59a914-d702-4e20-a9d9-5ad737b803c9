#!/usr/bin/env python3
"""
Final Targeted Vault Search
Searches for the exact MetaMask vault format as used by the official decryptor
"""

import os
import re
import json
import base64
from typing import List, Dict, Any

def search_exact_vault_format(filepath: str) -> List[Dict[str, Any]]:
    """Search for the exact vault format used by MetaMask"""
    print(f"Searching {filepath} for exact vault format...")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    # Convert to text
    try:
        text = data.decode('utf-8', errors='ignore')
    except:
        return []
    
    vault_candidates = []
    
    # 1. Look for the exact format: {"data":"...","iv":"...","salt":"..."}
    exact_pattern = r'\{"data":"([A-Za-z0-9+/=]+)","iv":"([A-Za-z0-9+/=]+)","salt":"([A-Za-z0-9+/=]+)"\}'
    matches = re.findall(exact_pattern, text)
    
    for match in matches:
        data_val, iv_val, salt_val = match
        vault_candidates.append({
            'type': 'EXACT_FORMAT',
            'data': data_val,
            'iv': iv_val,
            'salt': salt_val,
            'source': filepath
        })
        print(f"✅ Found exact format vault in {filepath}")
    
    # 2. Look for variations with different field orders
    variations = [
        r'\{"iv":"([A-Za-z0-9+/=]+)","data":"([A-Za-z0-9+/=]+)","salt":"([A-Za-z0-9+/=]+)"\}',
        r'\{"salt":"([A-Za-z0-9+/=]+)","data":"([A-Za-z0-9+/=]+)","iv":"([A-Za-z0-9+/=]+)"\}',
        r'\{"salt":"([A-Za-z0-9+/=]+)","iv":"([A-Za-z0-9+/=]+)","data":"([A-Za-z0-9+/=]+)"\}',
        r'\{"data":"([A-Za-z0-9+/=]+)","salt":"([A-Za-z0-9+/=]+)","iv":"([A-Za-z0-9+/=]+)"\}',
        r'\{"iv":"([A-Za-z0-9+/=]+)","salt":"([A-Za-z0-9+/=]+)","data":"([A-Za-z0-9+/=]+)"\}',
    ]
    
    field_orders = [
        ['iv', 'data', 'salt'],
        ['salt', 'data', 'iv'],
        ['salt', 'iv', 'data'],
        ['data', 'salt', 'iv'],
        ['iv', 'salt', 'data']
    ]
    
    for i, pattern in enumerate(variations):
        matches = re.findall(pattern, text)
        for match in matches:
            order = field_orders[i]
            vault_data = {}
            for j, field in enumerate(order):
                vault_data[field] = match[j]
            
            vault_candidates.append({
                'type': f'VARIATION_{i+1}',
                'data': vault_data['data'],
                'iv': vault_data['iv'],
                'salt': vault_data['salt'],
                'source': filepath
            })
            print(f"✅ Found variation {i+1} vault in {filepath}")
    
    # 3. Look for vault data with escaped quotes
    escaped_pattern = r'\{\\"data\\":\\"([A-Za-z0-9+/=]+)\\",\\"iv\\":\\"([A-Za-z0-9+/=]+)\\",\\"salt\\":\\"([A-Za-z0-9+/=]+)\\"\}'
    escaped_matches = re.findall(escaped_pattern, text)
    
    for match in escaped_matches:
        data_val, iv_val, salt_val = match
        vault_candidates.append({
            'type': 'ESCAPED_FORMAT',
            'data': data_val,
            'iv': iv_val,
            'salt': salt_val,
            'source': filepath
        })
        print(f"✅ Found escaped format vault in {filepath}")
    
    # 4. Look for vault data within larger JSON structures
    # This searches for vault data that might be nested within other objects
    nested_patterns = [
        r'"vault"\s*:\s*\{"data":"([A-Za-z0-9+/=]+)","iv":"([A-Za-z0-9+/=]+)","salt":"([A-Za-z0-9+/=]+)"\}',
        r'"KeyringController"\s*:\s*\{[^}]*"vault"\s*:\s*\{"data":"([A-Za-z0-9+/=]+)","iv":"([A-Za-z0-9+/=]+)","salt":"([A-Za-z0-9+/=]+)"\}',
    ]
    
    for pattern in nested_patterns:
        matches = re.findall(pattern, text, re.DOTALL)
        for match in matches:
            data_val, iv_val, salt_val = match
            vault_candidates.append({
                'type': 'NESTED_FORMAT',
                'data': data_val,
                'iv': iv_val,
                'salt': salt_val,
                'source': filepath
            })
            print(f"✅ Found nested format vault in {filepath}")
    
    # 5. Look for any base64 strings that might be vault data (even if not in JSON format)
    # Sometimes the vault might be stored as a single base64 string
    single_vault_pattern = r'"([A-Za-z0-9+/]{200,}={0,2})"'
    single_matches = re.findall(single_vault_pattern, text)
    
    for match in single_matches:
        try:
            # Try to decode and see if it looks like encrypted data
            decoded = base64.b64decode(match)
            if len(decoded) > 100:  # Substantial encrypted data
                vault_candidates.append({
                    'type': 'SINGLE_BASE64',
                    'data': match,
                    'decoded_length': len(decoded),
                    'source': filepath
                })
                print(f"✅ Found potential single base64 vault in {filepath}")
        except:
            continue
    
    return vault_candidates

def validate_vault_data(vault_data: Dict[str, str]) -> bool:
    """Validate that vault data has the correct format"""
    required_fields = ['data', 'iv', 'salt']
    
    # Check all required fields are present
    if not all(field in vault_data for field in required_fields):
        return False
    
    # Check that all fields are base64-encoded strings
    for field in required_fields:
        try:
            base64.b64decode(vault_data[field])
        except:
            return False
    
    # Check reasonable lengths
    data_len = len(vault_data['data'])
    iv_len = len(vault_data['iv'])
    salt_len = len(vault_data['salt'])
    
    # Data should be substantial (encrypted seed phrase)
    if data_len < 100:
        return False
    
    # IV should be reasonable length (typically 16 bytes = 24 base64 chars)
    if iv_len < 16 or iv_len > 50:
        return False
    
    # Salt should be reasonable length (typically 32 bytes = 44 base64 chars)
    if salt_len < 20 or salt_len > 100:
        return False
    
    return True

def main():
    """Main search function"""
    print("Final Targeted MetaMask Vault Search")
    print("=" * 50)
    
    # Get all .ldb files
    ldb_files = [f for f in os.listdir('.') if f.endswith('.ldb')]
    
    if not ldb_files:
        print("No .ldb files found")
        return
    
    print(f"Searching {len(ldb_files)} .ldb files for exact vault format...")
    
    all_vault_candidates = []
    
    for ldb_file in ldb_files:
        candidates = search_exact_vault_format(ldb_file)
        all_vault_candidates.extend(candidates)
    
    print(f"\n{'='*60}")
    print("FINAL SEARCH RESULTS")
    print(f"{'='*60}")
    
    print(f"Total vault candidates found: {len(all_vault_candidates)}")
    
    if all_vault_candidates:
        valid_vaults = []
        
        for i, candidate in enumerate(all_vault_candidates):
            print(f"\nCandidate {i+1}:")
            print(f"  Type: {candidate['type']}")
            print(f"  Source: {candidate['source']}")
            
            if candidate['type'] != 'SINGLE_BASE64':
                # Validate the vault data
                vault_data = {
                    'data': candidate['data'],
                    'iv': candidate['iv'],
                    'salt': candidate['salt']
                }
                
                is_valid = validate_vault_data(vault_data)
                print(f"  Valid format: {is_valid}")
                
                if is_valid:
                    valid_vaults.append((i+1, vault_data))
                    print(f"  Data length: {len(candidate['data'])}")
                    print(f"  IV length: {len(candidate['iv'])}")
                    print(f"  Salt length: {len(candidate['salt'])}")
                    print(f"  Data preview: {candidate['data'][:50]}...")
                else:
                    print(f"  Data length: {len(candidate['data'])}")
                    print(f"  IV length: {len(candidate['iv'])}")
                    print(f"  Salt length: {len(candidate['salt'])}")
            else:
                print(f"  Decoded length: {candidate['decoded_length']}")
                print(f"  Data preview: {candidate['data'][:50]}...")
        
        # Save valid vaults
        if valid_vaults:
            print(f"\n🎉 FOUND {len(valid_vaults)} VALID VAULT(S)!")
            
            for i, (candidate_num, vault_data) in enumerate(valid_vaults):
                # Save as JSON
                vault_filename = f"valid_vault_{i+1}.json"
                with open(vault_filename, 'w') as f:
                    json.dump(vault_data, f, indent=2)
                
                # Save as text for copy-paste
                vault_text_filename = f"valid_vault_{i+1}.txt"
                with open(vault_text_filename, 'w') as f:
                    f.write(json.dumps(vault_data))
                
                print(f"\nValid Vault {i+1} (from candidate {candidate_num}):")
                print(f"  ✅ Saved to: {vault_filename}")
                print(f"  ✅ Text format: {vault_text_filename}")
            
            # Show instructions
            print(f"\n🎯 INSTRUCTIONS:")
            print(f"1. The MetaMask vault decryptor is already open in your browser")
            print(f"2. Copy the content from valid_vault_1.txt:")
            
            # Show the first valid vault for copy-paste
            first_vault = valid_vaults[0][1]
            print(f"\n📋 COPY THIS TEXT:")
            print(f"{'='*60}")
            print(json.dumps(first_vault))
            print(f"{'='*60}")
            
            print(f"\n3. Paste it into the 'Vault Data' field in the decryptor")
            print(f"4. Enter your MetaMask password")
            print(f"5. Click 'Decrypt' to recover your seed phrase")
            
        else:
            print(f"\n⚠️  Found vault candidates but none passed validation")
            print(f"This might mean:")
            print(f"  - The data is corrupted")
            print(f"  - The format is slightly different")
            print(f"  - The data needs manual cleaning")
            
            # Save all candidates anyway for manual inspection
            with open('all_vault_candidates.json', 'w') as f:
                json.dump(all_vault_candidates, f, indent=2, default=str)
            
            print(f"  - All candidates saved to: all_vault_candidates.json")
    
    else:
        print(f"\n❌ No vault data found in the expected format")
        print(f"\nThis could mean:")
        print(f"  1. The .ldb files are not from MetaMask's local storage")
        print(f"  2. The vault data is stored in a different format")
        print(f"  3. The files are corrupted or incomplete")
        print(f"  4. MetaMask was not set up on this browser profile")
        
        print(f"\n💡 Alternative approaches:")
        print(f"  1. Check if you have other .ldb files from different browser profiles")
        print(f"  2. Look for files with different numbers (000001.ldb, 000002.ldb, etc.)")
        print(f"  3. Check the browser's extension storage directly")

if __name__ == "__main__":
    main()
