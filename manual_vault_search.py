#!/usr/bin/env python3
"""
Manual Vault Search
Manually search for vault data patterns in the binary data
"""

import os
import re
import json
import base64

def search_raw_binary_for_vault(filepath: str):
    """Search raw binary data for vault patterns"""
    print(f"Searching raw binary data in {filepath}")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    vault_candidates = []
    
    # Search for base64-like patterns in raw bytes
    # Look for sequences that could be base64 encoded vault data
    
    # Pattern 1: Look for "data" followed by base64
    data_pattern = rb'data["\']?\s*[:=]\s*["\']?([A-Za-z0-9+/]{100,}={0,2})["\']?'
    matches = re.finditer(data_pattern, data, re.IGNORECASE)
    
    for match in matches:
        try:
            potential_data = match.group(1).decode('ascii')
            # Verify it's valid base64
            decoded = base64.b64decode(potential_data)
            if len(decoded) > 50:
                vault_candidates.append({
                    'type': 'DATA_FIELD',
                    'position': match.start(),
                    'data': potential_data,
                    'decoded_length': len(decoded)
                })
                print(f"✅ Found data field at position {match.start()}")
        except:
            continue
    
    # Pattern 2: Look for "salt" followed by base64
    salt_pattern = rb'salt["\']?\s*[:=]\s*["\']?([A-Za-z0-9+/]{20,}={0,2})["\']?'
    matches = re.finditer(salt_pattern, data, re.IGNORECASE)
    
    for match in matches:
        try:
            potential_salt = match.group(1).decode('ascii')
            decoded = base64.b64decode(potential_salt)
            if len(decoded) > 10:
                vault_candidates.append({
                    'type': 'SALT_FIELD',
                    'position': match.start(),
                    'salt': potential_salt,
                    'decoded_length': len(decoded)
                })
                print(f"✅ Found salt field at position {match.start()}")
        except:
            continue
    
    # Pattern 3: Look for "iv" followed by base64
    iv_pattern = rb'iv["\']?\s*[:=]\s*["\']?([A-Za-z0-9+/]{16,}={0,2})["\']?'
    matches = re.finditer(iv_pattern, data, re.IGNORECASE)
    
    for match in matches:
        try:
            potential_iv = match.group(1).decode('ascii')
            decoded = base64.b64decode(potential_iv)
            if len(decoded) > 8:
                vault_candidates.append({
                    'type': 'IV_FIELD',
                    'position': match.start(),
                    'iv': potential_iv,
                    'decoded_length': len(decoded)
                })
                print(f"✅ Found iv field at position {match.start()}")
        except:
            continue
    
    # Pattern 4: Look for complete JSON-like structures in binary
    json_pattern = rb'\{[^}]*["\']data["\'][^}]*["\']iv["\'][^}]*["\']salt["\'][^}]*\}'
    matches = re.finditer(json_pattern, data, re.IGNORECASE | re.DOTALL)
    
    for match in matches:
        try:
            potential_json = match.group().decode('utf-8', errors='ignore')
            vault_candidates.append({
                'type': 'JSON_STRUCTURE',
                'position': match.start(),
                'raw_json': potential_json
            })
            print(f"✅ Found JSON structure at position {match.start()}")
        except:
            continue
    
    # Pattern 5: Look for very long base64 strings that could be vault data
    long_base64_pattern = rb'[A-Za-z0-9+/]{200,}={0,2}'
    matches = re.finditer(long_base64_pattern, data)
    
    for match in matches:
        try:
            potential_vault = match.group().decode('ascii')
            decoded = base64.b64decode(potential_vault)
            if len(decoded) > 100:
                vault_candidates.append({
                    'type': 'LONG_BASE64',
                    'position': match.start(),
                    'data': potential_vault,
                    'decoded_length': len(decoded)
                })
                print(f"✅ Found long base64 at position {match.start()}")
        except:
            continue
    
    return vault_candidates

def search_around_keyring_positions(filepath: str, keyring_positions: list):
    """Search specifically around keyring positions"""
    print(f"Searching around keyring positions in {filepath}")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    vault_candidates = []
    
    for i, pos in enumerate(keyring_positions):
        print(f"\nExamining keyring position {i+1} at {pos}")
        
        # Look in a 5KB window around the keyring
        start = max(0, pos - 2000)
        end = min(len(data), pos + 3000)
        window = data[start:end]
        
        # Look for base64 patterns in this window
        base64_patterns = [
            rb'[A-Za-z0-9+/]{100,}={0,2}',  # Long base64
            rb'[A-Za-z0-9+/]{50,}={0,2}',   # Medium base64
        ]
        
        for pattern in base64_patterns:
            matches = re.finditer(pattern, window)
            for match in matches:
                try:
                    potential_b64 = match.group().decode('ascii')
                    decoded = base64.b64decode(potential_b64)
                    
                    if len(decoded) > 30:  # Substantial data
                        absolute_pos = start + match.start()
                        vault_candidates.append({
                            'type': f'BASE64_NEAR_KEYRING_{i+1}',
                            'keyring_position': pos,
                            'absolute_position': absolute_pos,
                            'relative_to_keyring': absolute_pos - pos,
                            'data': potential_b64,
                            'decoded_length': len(decoded)
                        })
                        print(f"  Found base64 at offset {absolute_pos - pos} from keyring")
                except:
                    continue
    
    return vault_candidates

def extract_readable_strings_around_keyring(filepath: str, keyring_positions: list):
    """Extract readable strings around keyring positions"""
    print(f"Extracting readable strings around keyring positions")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    findings = []
    
    for i, pos in enumerate(keyring_positions):
        print(f"\nExtracting strings near keyring {i+1} at position {pos}")
        
        # Look in a window around keyring
        start = max(0, pos - 1000)
        end = min(len(data), pos + 2000)
        window = data[start:end]
        
        # Try to decode as UTF-8 and extract meaningful strings
        try:
            text = window.decode('utf-8', errors='ignore')
            
            # Look for strings that contain vault-related keywords
            vault_keywords = ['data', 'salt', 'iv', 'vault', 'keyring', 'encrypted']
            
            for keyword in vault_keywords:
                # Find keyword and extract surrounding context
                keyword_positions = [m.start() for m in re.finditer(keyword, text, re.IGNORECASE)]
                
                for kw_pos in keyword_positions:
                    context_start = max(0, kw_pos - 200)
                    context_end = min(len(text), kw_pos + 200)
                    context = text[context_start:context_end]
                    
                    findings.append({
                        'keyring_position': pos,
                        'keyword': keyword,
                        'context': context,
                        'absolute_position': start + kw_pos
                    })
                    print(f"  Found '{keyword}' at offset {start + kw_pos - pos} from keyring")
        except:
            continue
    
    return findings

def main():
    """Main manual search function"""
    print("Manual Vault Search in Binary Data")
    print("=" * 50)
    
    target_file = "000005 1.ldb"
    keyring_positions = [797764, 922418, 1735360, 1860005]
    
    if not os.path.exists(target_file):
        print(f"Target file {target_file} not found!")
        return
    
    print(f"Searching {target_file} with keyring positions: {keyring_positions}")
    
    # Search 1: General vault patterns in entire file
    print(f"\n1. Searching entire file for vault patterns...")
    general_candidates = search_raw_binary_for_vault(target_file)
    
    # Search 2: Specific search around keyring positions
    print(f"\n2. Searching around keyring positions...")
    keyring_candidates = search_around_keyring_positions(target_file, keyring_positions)
    
    # Search 3: Extract readable strings around keyring
    print(f"\n3. Extracting readable strings around keyring...")
    string_findings = extract_readable_strings_around_keyring(target_file, keyring_positions)
    
    # Summary
    print(f"\n{'='*60}")
    print("MANUAL SEARCH RESULTS")
    print(f"{'='*60}")
    
    print(f"General vault candidates: {len(general_candidates)}")
    print(f"Keyring area candidates: {len(keyring_candidates)}")
    print(f"String findings: {len(string_findings)}")
    
    all_candidates = general_candidates + keyring_candidates
    
    if all_candidates:
        print(f"\n🎯 VAULT CANDIDATES FOUND:")
        
        for i, candidate in enumerate(all_candidates):
            print(f"\nCandidate {i+1}:")
            print(f"  Type: {candidate['type']}")
            print(f"  Position: {candidate.get('position', candidate.get('absolute_position', 'N/A'))}")
            
            if 'data' in candidate:
                print(f"  Data length: {len(candidate['data'])}")
                print(f"  Data preview: {candidate['data'][:50]}...")
            
            if 'decoded_length' in candidate:
                print(f"  Decoded length: {candidate['decoded_length']} bytes")
            
            if 'keyring_position' in candidate:
                print(f"  Near keyring at: {candidate['keyring_position']}")
                print(f"  Offset from keyring: {candidate.get('relative_to_keyring', 'N/A')}")
        
        # Save all candidates
        with open('manual_search_candidates.json', 'w') as f:
            json.dump({
                'general_candidates': general_candidates,
                'keyring_candidates': keyring_candidates,
                'string_findings': string_findings[:20]  # Limit string findings
            }, f, indent=2, default=str)
        
        print(f"\nCandidates saved to: manual_search_candidates.json")
        
        # Try to construct vault data from candidates
        print(f"\n🔧 ATTEMPTING TO CONSTRUCT VAULT DATA:")
        
        data_candidates = [c for c in all_candidates if 'data' in c and len(c.get('data', '')) > 100]
        
        if data_candidates:
            print(f"Found {len(data_candidates)} potential data fields")
            
            # For each data candidate, try to find nearby salt and iv
            for i, data_candidate in enumerate(data_candidates[:3]):  # Check first 3
                print(f"\nTrying to construct vault from candidate {i+1}:")
                print(f"  Data: {data_candidate['data'][:50]}...")
                
                # This would be the manual construction part
                # For now, just save the data candidate
                vault_attempt = {
                    "data": data_candidate['data'],
                    "iv": "NEED_TO_FIND_IV",
                    "salt": "NEED_TO_FIND_SALT"
                }
                
                attempt_filename = f"vault_attempt_{i+1}.json"
                with open(attempt_filename, 'w') as f:
                    json.dump(vault_attempt, f, indent=2)
                
                print(f"  Saved attempt to: {attempt_filename}")
    
    else:
        print(f"\n❌ No vault candidates found in manual search")
    
    if string_findings:
        print(f"\n📝 STRING FINDINGS AROUND KEYRING:")
        for finding in string_findings[:10]:  # Show first 10
            print(f"\nKeyword '{finding['keyword']}' near keyring {finding['keyring_position']}:")
            print(f"  Context: {finding['context'][:100]}...")
    
    print(f"\n💡 NEXT STEPS:")
    print(f"1. Review manual_search_candidates.json for any vault data")
    print(f"2. If you found data fields, manually look for corresponding salt and iv")
    print(f"3. Try opening the .ldb file in a hex editor")
    print(f"4. Consider that the vault might be in a different .ldb file")

if __name__ == "__main__":
    main()
