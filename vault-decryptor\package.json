{"name": "@metamask/vault-decryptor", "version": "1.0.0", "private": true, "description": "Utility for decrypting MetaMask vault data", "main": "index.js", "scripts": {"start": "beefy index.js:bundle.js --live --open", "test": "jest && jest-it-up", "build": "browserify index.js -o bundle.js"}, "browserify": {"transform": [["babe<PERSON>", {"presets": [["@babel/preset-env", {"useBuiltIns": "entry", "corejs": "3.22"}]]}], "brfs"]}, "author": "<PERSON>", "license": "ISC", "resolutions": {"elliptic": "^6.6.1"}, "dependencies": {"@metamask/browser-passworder": "^4.3.0", "ethereumjs-util": "^7.1.4", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hyperscript": "^3.2.0", "react-redux": "^8.0.1", "redux": "^4.2.0", "redux-logger": "^3.0.6", "redux-thunk": "^2.4.1", "xtend": "^4.0.2"}, "keywords": ["metamask", "vault-decryptor", "ethereum wallet"], "devDependencies": {"@babel/preset-env": "^7.20.2", "babelify": "^10.0.0", "brfs": "^1.4.3", "browserify": "^17.0.0", "jest": "^29.4.1", "jest-it-up": "^2.1.0"}, "directories": {"lib": "lib"}, "repository": {"type": "git", "url": "https://github.com/MetaMask/vault-decryptor.git"}, "bugs": {"url": "https://github.com/MetaMask/vault-decryptor/issues"}, "homepage": "https://github.com/MetaMask/vault-decryptor#readme", "engines": {"node": ">=18.0.0"}, "packageManager": "yarn@3.4.1"}