#!/usr/bin/env python3
"""
Focused Vault Extractor
Extracts the most promising vault data from the identified file
"""

import os
import re
import json
import base64
from typing import List, Dict, Any, Optional

def extract_promising_data(filepath: str = "000005 1.ldb") -> Dict[str, Any]:
    """Extract the most promising vault data from the target file"""
    print(f"Extracting promising vault data from: {filepath}")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return {}
    
    # Convert to text for analysis
    try:
        text = data.decode('utf-8', errors='ignore')
    except:
        return {}
    
    results = {
        'file': filepath,
        'vault_candidates': [],
        'seed_candidates': [],
        'encrypted_data': [],
        'json_structures': []
    }
    
    # 1. Look for vault-like structures
    vault_patterns = [
        r'"vault"\s*:\s*"([A-Za-z0-9+/=]{100,})"',  # Direct vault with long encrypted data
        r'"data"\s*:\s*"([A-Za-z0-9+/=]{100,})"',   # Data field with encrypted content
        r'"encryptedData"\s*:\s*"([A-Za-z0-9+/=]{100,})"',
    ]
    
    for pattern in vault_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            if len(match) > 100:  # Substantial encrypted data
                results['vault_candidates'].append({
                    'type': 'VAULT_DATA',
                    'data': match,
                    'length': len(match),
                    'pattern': pattern
                })
    
    # 2. Look for seed-related data
    seed_patterns = [
        r'["\']seed["\']\s*:\s*["\']([^"\']{50,})["\']',
        r'["\']mnemonic["\']\s*:\s*["\']([^"\']{50,})["\']',
        r'seed["\']?\s*[:=]\s*["\']?([A-Za-z0-9+/=]{50,})["\']?',
    ]
    
    for pattern in seed_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            results['seed_candidates'].append({
                'type': 'SEED_DATA',
                'data': match,
                'length': len(match),
                'pattern': pattern
            })
    
    # 3. Look for complete JSON structures that might contain vault data
    json_patterns = [
        r'\{[^{}]*"vault"[^{}]*\}',
        r'\{[^{}]*"KeyringController"[^{}]*\}',
        r'\{[^{}]*"data"[^{}]*"salt"[^{}]*"iv"[^{}]*\}',
    ]
    
    for pattern in json_patterns:
        matches = re.findall(pattern, text, re.DOTALL)
        for match in matches:
            if len(match) > 50:
                try:
                    # Try to parse as JSON
                    parsed = json.loads(match)
                    results['json_structures'].append({
                        'type': 'JSON_VAULT',
                        'raw': match,
                        'parsed': parsed,
                        'length': len(match)
                    })
                except:
                    # Keep as raw data
                    results['json_structures'].append({
                        'type': 'RAW_JSON_LIKE',
                        'raw': match,
                        'length': len(match)
                    })
    
    # 4. Look for any long base64-encoded strings that could be encrypted data
    base64_pattern = r'[A-Za-z0-9+/]{200,}={0,2}'
    base64_matches = re.findall(base64_pattern, text)
    
    for match in base64_matches:
        try:
            # Verify it's valid base64
            decoded = base64.b64decode(match)
            if len(decoded) > 100:  # Substantial data
                results['encrypted_data'].append({
                    'type': 'BASE64_ENCRYPTED',
                    'data': match,
                    'decoded_length': len(decoded),
                    'encoded_length': len(match)
                })
        except:
            continue
    
    return results

def find_metamask_vault_structure(filepath: str = "000005 1.ldb") -> List[Dict[str, Any]]:
    """Look for complete MetaMask vault structures"""
    print(f"Searching for complete MetaMask vault structures in: {filepath}")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    # Convert to text
    try:
        text = data.decode('utf-8', errors='ignore')
    except:
        return []
    
    vault_structures = []
    
    # Look for the typical MetaMask vault structure
    # MetaMask stores encrypted vault as: {"data": "...", "iv": "...", "salt": "..."}
    vault_structure_pattern = r'\{[^{}]*"data"\s*:\s*"([A-Za-z0-9+/=]{50,})"[^{}]*"iv"\s*:\s*"([A-Za-z0-9+/=]{20,})"[^{}]*"salt"\s*:\s*"([A-Za-z0-9+/=]{20,})"[^{}]*\}'
    
    matches = re.findall(vault_structure_pattern, text, re.DOTALL | re.IGNORECASE)
    
    for match in matches:
        data_field, iv_field, salt_field = match
        vault_structures.append({
            'type': 'COMPLETE_VAULT_STRUCTURE',
            'data': data_field,
            'iv': iv_field,
            'salt': salt_field,
            'data_length': len(data_field),
            'iv_length': len(iv_field),
            'salt_length': len(salt_field)
        })
    
    # Also look for variations
    variations = [
        r'\{[^{}]*"salt"\s*:\s*"([A-Za-z0-9+/=]{20,})"[^{}]*"iv"\s*:\s*"([A-Za-z0-9+/=]{20,})"[^{}]*"data"\s*:\s*"([A-Za-z0-9+/=]{50,})"[^{}]*\}',
        r'\{[^{}]*"iv"\s*:\s*"([A-Za-z0-9+/=]{20,})"[^{}]*"data"\s*:\s*"([A-Za-z0-9+/=]{50,})"[^{}]*"salt"\s*:\s*"([A-Za-z0-9+/=]{20,})"[^{}]*\}',
    ]
    
    for i, pattern in enumerate(variations):
        matches = re.findall(pattern, text, re.DOTALL | re.IGNORECASE)
        for match in matches:
            if i == 0:  # salt, iv, data
                salt_field, iv_field, data_field = match
            else:  # iv, data, salt
                iv_field, data_field, salt_field = match
            
            vault_structures.append({
                'type': f'VAULT_STRUCTURE_VARIANT_{i+1}',
                'data': data_field,
                'iv': iv_field,
                'salt': salt_field,
                'data_length': len(data_field),
                'iv_length': len(iv_field),
                'salt_length': len(salt_field)
            })
    
    return vault_structures

def main():
    """Main extraction function"""
    print("Focused MetaMask Vault Data Extractor")
    print("=" * 50)
    
    target_file = "000005 1.ldb"
    
    if not os.path.exists(target_file):
        print(f"Target file {target_file} not found!")
        return
    
    # Extract promising data
    promising_data = extract_promising_data(target_file)
    
    # Find complete vault structures
    vault_structures = find_metamask_vault_structure(target_file)
    
    # Summary
    print(f"\n{'='*60}")
    print("EXTRACTION RESULTS")
    print(f"{'='*60}")
    
    print(f"Vault candidates: {len(promising_data.get('vault_candidates', []))}")
    print(f"Seed candidates: {len(promising_data.get('seed_candidates', []))}")
    print(f"Encrypted data: {len(promising_data.get('encrypted_data', []))}")
    print(f"JSON structures: {len(promising_data.get('json_structures', []))}")
    print(f"Complete vault structures: {len(vault_structures)}")
    
    # Show the most promising findings
    if vault_structures:
        print(f"\n🎯 FOUND COMPLETE VAULT STRUCTURES:")
        for i, structure in enumerate(vault_structures):
            print(f"\nStructure {i+1} ({structure['type']}):")
            print(f"  - Data length: {structure['data_length']}")
            print(f"  - IV length: {structure['iv_length']}")
            print(f"  - Salt length: {structure['salt_length']}")
            print(f"  - Data preview: {structure['data'][:50]}...")
            print(f"  - IV: {structure['iv']}")
            print(f"  - Salt: {structure['salt']}")
    
    if promising_data.get('vault_candidates'):
        print(f"\n🔍 VAULT CANDIDATES:")
        for i, candidate in enumerate(promising_data['vault_candidates'][:3]):
            print(f"\nCandidate {i+1}:")
            print(f"  - Type: {candidate['type']}")
            print(f"  - Length: {candidate['length']}")
            print(f"  - Data preview: {candidate['data'][:50]}...")
    
    if promising_data.get('encrypted_data'):
        print(f"\n🔐 ENCRYPTED DATA:")
        for i, data in enumerate(promising_data['encrypted_data'][:3]):
            print(f"\nData {i+1}:")
            print(f"  - Type: {data['type']}")
            print(f"  - Encoded length: {data['encoded_length']}")
            print(f"  - Decoded length: {data['decoded_length']}")
            print(f"  - Data preview: {data['data'][:50]}...")
    
    # Save results
    all_results = {
        'promising_data': promising_data,
        'vault_structures': vault_structures,
        'summary': {
            'total_vault_candidates': len(promising_data.get('vault_candidates', [])),
            'total_vault_structures': len(vault_structures),
            'total_encrypted_data': len(promising_data.get('encrypted_data', [])),
            'has_complete_vault': len(vault_structures) > 0
        }
    }
    
    output_file = 'focused_vault_extraction.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\nDetailed results saved to: {output_file}")
    
    # Next steps
    if vault_structures:
        print(f"\n✅ SUCCESS: Found {len(vault_structures)} complete vault structure(s)!")
        print(f"Next step: Create decryptor to decrypt with your password")
    elif promising_data.get('vault_candidates') or promising_data.get('encrypted_data'):
        print(f"\n⚠️  Found potential vault data but no complete structures")
        print(f"Next step: Manual analysis of candidates")
    else:
        print(f"\n❌ No obvious vault structures found")
        print(f"The data might be in a different format or corrupted")

if __name__ == "__main__":
    main()
