#!/usr/bin/env python3
"""
Raw Data Scanner for MetaMask Recovery
Scans for any encrypted-looking data that could be MetaMask vault data
"""

import os
import re
import base64
import json
from typing import List, Dict, Any

def is_base64(s: str) -> bool:
    """Check if string looks like base64"""
    try:
        if len(s) < 20:  # Too short to be meaningful encrypted data
            return False
        # Check if it's valid base64
        base64.b64decode(s)
        # Check if it has base64 characteristics
        return re.match(r'^[A-Za-z0-9+/]*={0,2}$', s) is not None
    except:
        return False

def find_encrypted_looking_data(data: bytes) -> List[Dict[str, Any]]:
    """Find data that looks like it could be encrypted"""
    results = []
    
    # Convert to text
    try:
        text = data.decode('utf-8', errors='ignore')
    except:
        return results
    
    # Look for long base64-like strings
    base64_pattern = r'[A-Za-z0-9+/]{40,}={0,2}'
    matches = re.findall(base64_pattern, text)
    
    for match in matches:
        if is_base64(match):
            results.append({
                'type': 'BASE64_DATA',
                'data': match,
                'length': len(match),
                'context': 'Found in raw scan'
            })
    
    # Look for hex-encoded data
    hex_pattern = r'[0-9a-fA-F]{40,}'
    hex_matches = re.findall(hex_pattern, text)
    
    for match in hex_matches:
        if len(match) >= 40:  # Substantial hex data
            results.append({
                'type': 'HEX_DATA',
                'data': match,
                'length': len(match),
                'context': 'Found in raw scan'
            })
    
    # Look for JSON-like structures with encrypted data
    json_patterns = [
        r'\{[^{}]*"[^"]*":\s*"[A-Za-z0-9+/]{40,}={0,2}"[^{}]*\}',
        r'"[^"]*":\s*"[A-Za-z0-9+/]{40,}={0,2}"',
    ]
    
    for pattern in json_patterns:
        matches = re.findall(pattern, text, re.DOTALL)
        for match in matches:
            results.append({
                'type': 'JSON_WITH_ENCRYPTED',
                'data': match,
                'length': len(match),
                'context': 'JSON structure with encrypted-looking data'
            })
    
    return results

def scan_for_metamask_strings(data: bytes) -> List[Dict[str, Any]]:
    """Scan for any strings that might be related to MetaMask"""
    results = []
    
    try:
        text = data.decode('utf-8', errors='ignore')
    except:
        return results
    
    # MetaMask-related keywords to search around
    keywords = [
        'metamask', 'vault', 'keyring', 'mnemonic', 'seed', 'wallet',
        'private', 'key', 'encrypted', 'password', 'salt', 'iv',
        'cipher', 'aes', 'pbkdf2', 'scrypt'
    ]
    
    for keyword in keywords:
        # Find the keyword and extract surrounding context
        pattern = rf'.{{0,200}}{re.escape(keyword)}.{{0,200}}'
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        
        for match in matches:
            if len(match.strip()) > 10:
                results.append({
                    'type': f'KEYWORD_{keyword.upper()}',
                    'data': match.strip(),
                    'length': len(match.strip()),
                    'keyword': keyword,
                    'context': f'Text around keyword: {keyword}'
                })
    
    return results

def extract_all_strings(data: bytes, min_length: int = 20) -> List[str]:
    """Extract all readable strings from binary data"""
    strings = []
    
    # Extract UTF-8 strings
    try:
        text = data.decode('utf-8', errors='ignore')
        # Split by common separators and null bytes
        parts = re.split(r'[\x00-\x1f\x7f-\x9f]+', text)
        for part in parts:
            clean_part = part.strip()
            if len(clean_part) >= min_length:
                strings.append(clean_part)
    except:
        pass
    
    return strings

def analyze_file_thoroughly(filepath: str) -> Dict[str, Any]:
    """Thoroughly analyze a file for any MetaMask-related data"""
    print(f"\n=== THOROUGH ANALYSIS: {filepath} ===")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return {'file': filepath, 'error': str(e)}
    
    file_size = len(data)
    print(f"File size: {file_size:,} bytes")
    
    # Find encrypted-looking data
    encrypted_data = find_encrypted_looking_data(data)
    print(f"Found {len(encrypted_data)} encrypted-looking entries")
    
    # Scan for MetaMask strings
    metamask_strings = scan_for_metamask_strings(data)
    print(f"Found {len(metamask_strings)} MetaMask-related strings")
    
    # Extract all strings
    all_strings = extract_all_strings(data, min_length=30)
    print(f"Extracted {len(all_strings)} strings (30+ chars)")
    
    # Look for the most promising strings
    promising_strings = []
    for string in all_strings:
        string_lower = string.lower()
        if any(keyword in string_lower for keyword in ['vault', 'metamask', 'keyring', 'mnemonic', 'seed']):
            promising_strings.append(string)
    
    print(f"Found {len(promising_strings)} promising strings")
    
    # Show samples
    if encrypted_data:
        print(f"\nSample encrypted data:")
        for i, item in enumerate(encrypted_data[:3]):
            print(f"  {i+1}. {item['type']}: {item['data'][:80]}...")
    
    if metamask_strings:
        print(f"\nSample MetaMask strings:")
        for i, item in enumerate(metamask_strings[:3]):
            print(f"  {i+1}. {item['keyword']}: {item['data'][:80]}...")
    
    if promising_strings:
        print(f"\nSample promising strings:")
        for i, string in enumerate(promising_strings[:3]):
            print(f"  {i+1}. {string[:80]}...")
    
    return {
        'file': filepath,
        'size': file_size,
        'encrypted_data': encrypted_data,
        'metamask_strings': metamask_strings,
        'all_strings': all_strings[:100],  # Limit to first 100 for storage
        'promising_strings': promising_strings,
        'total_findings': len(encrypted_data) + len(metamask_strings) + len(promising_strings)
    }

def main():
    """Main analysis function"""
    print("Raw Data Scanner for MetaMask Recovery")
    print("=" * 50)
    
    # Analyze the most promising files
    target_files = ["000005 1.ldb", "000005.ldb", "000005_1.ldb"]
    
    all_results = []
    
    for filepath in target_files:
        if os.path.exists(filepath):
            result = analyze_file_thoroughly(filepath)
            all_results.append(result)
    
    # Summary
    print(f"\n{'='*60}")
    print("COMPREHENSIVE SUMMARY")
    print(f"{'='*60}")
    
    total_findings = sum(r.get('total_findings', 0) for r in all_results)
    print(f"Total findings across all files: {total_findings}")
    
    # Find best candidates
    best_files = sorted(all_results, key=lambda x: x.get('total_findings', 0), reverse=True)
    
    if best_files and best_files[0].get('total_findings', 0) > 0:
        print(f"\nBest candidate: {best_files[0]['file']}")
        best = best_files[0]
        
        print(f"  - Encrypted data entries: {len(best.get('encrypted_data', []))}")
        print(f"  - MetaMask strings: {len(best.get('metamask_strings', []))}")
        print(f"  - Promising strings: {len(best.get('promising_strings', []))}")
        
        # Show the most promising data
        if best.get('encrypted_data'):
            print(f"\nMost promising encrypted data:")
            for i, item in enumerate(best['encrypted_data'][:5]):
                print(f"  {i+1}. Type: {item['type']}, Length: {item['length']}")
                print(f"     Data: {item['data'][:100]}...")
        
        if best.get('promising_strings'):
            print(f"\nMost promising strings:")
            for i, string in enumerate(best['promising_strings'][:5]):
                print(f"  {i+1}. {string[:150]}...")
    
    # Save results
    output_file = 'raw_data_scan_results.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\nDetailed results saved to: {output_file}")
    
    if total_findings > 0:
        print(f"\nNext steps:")
        print(f"1. Review the most promising encrypted data")
        print(f"2. Try to identify vault structure")
        print(f"3. Attempt decryption with your password")
    else:
        print(f"\nNo obvious MetaMask data found. The files might be:")
        print(f"1. From a different browser extension")
        print(f"2. Corrupted during recovery")
        print(f"3. Not containing the MetaMask vault data")

if __name__ == "__main__":
    main()
