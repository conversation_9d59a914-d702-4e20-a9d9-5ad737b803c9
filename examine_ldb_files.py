#!/usr/bin/env python3
"""
MetaMask LevelDB File Examiner
Analyzes .ldb files to find MetaMask vault data
"""

import os
import json
import re
import struct
from typing import Dict, List, Optional, Tu<PERSON>

def read_binary_file(filepath: str) -> bytes:
    """Read binary file content"""
    try:
        with open(filepath, 'rb') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return b''

def find_json_strings(data: bytes) -> List[str]:
    """Find potential JSON strings in binary data"""
    json_strings = []
    
    # Look for JSON-like patterns
    patterns = [
        rb'\{"[^"]*":[^}]*\}',  # Simple JSON objects
        rb'\{[^}]*"vault"[^}]*\}',  # Objects containing "vault"
        rb'\{[^}]*"data"[^}]*\}',   # Objects containing "data"
        rb'\{[^}]*"KeyringController"[^}]*\}',  # MetaMask KeyringController
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, data, re.DOTALL)
        for match in matches:
            try:
                # Try to decode as UTF-8
                json_str = match.decode('utf-8', errors='ignore')
                # Clean up the string
                json_str = json_str.strip()
                if json_str and json_str.startswith('{') and json_str.endswith('}'):
                    json_strings.append(json_str)
            except:
                continue
    
    return json_strings

def find_metamask_data(data: bytes) -> List[Dict]:
    """Find MetaMask-specific data patterns"""
    metamask_data = []
    
    # Look for MetaMask-specific strings
    metamask_indicators = [
        b'vault',
        b'KeyringController',
        b'seed',
        b'mnemonic',
        b'encryptionKey',
        b'salt',
        b'iv',
        b'data'
    ]
    
    # Find all JSON-like strings
    json_strings = find_json_strings(data)
    
    for json_str in json_strings:
        try:
            # Try to parse as JSON
            parsed = json.loads(json_str)
            
            # Check if it contains MetaMask-related keys
            if isinstance(parsed, dict):
                keys = str(parsed.keys()).lower()
                values = str(parsed.values()).lower()
                content = keys + values
                
                if any(indicator.decode() in content for indicator in metamask_indicators):
                    metamask_data.append({
                        'json_data': parsed,
                        'raw_string': json_str
                    })
        except json.JSONDecodeError:
            continue
    
    return metamask_data

def examine_ldb_file(filepath: str) -> Dict:
    """Examine a single .ldb file for MetaMask data"""
    print(f"\n=== Examining {filepath} ===")
    
    data = read_binary_file(filepath)
    if not data:
        return {'file': filepath, 'size': 0, 'metamask_data': []}
    
    file_size = len(data)
    print(f"File size: {file_size} bytes")
    
    # Look for MetaMask data
    metamask_data = find_metamask_data(data)
    
    print(f"Found {len(metamask_data)} potential MetaMask data entries")
    
    # Look for specific vault patterns
    vault_patterns = [
        b'"vault"',
        b'KeyringController',
        b'"data"',
        b'"salt"',
        b'"iv"'
    ]
    
    found_patterns = []
    for pattern in vault_patterns:
        if pattern in data:
            found_patterns.append(pattern.decode())
            print(f"Found pattern: {pattern.decode()}")
    
    return {
        'file': filepath,
        'size': file_size,
        'metamask_data': metamask_data,
        'found_patterns': found_patterns
    }

def main():
    """Main function to examine all .ldb files"""
    print("MetaMask LevelDB File Examiner")
    print("=" * 40)
    
    # Get all .ldb files in current directory
    ldb_files = [f for f in os.listdir('.') if f.endswith('.ldb')]
    
    if not ldb_files:
        print("No .ldb files found in current directory")
        return
    
    print(f"Found {len(ldb_files)} .ldb files:")
    for f in ldb_files:
        print(f"  - {f}")
    
    results = []
    
    # Examine each file
    for ldb_file in ldb_files:
        result = examine_ldb_file(ldb_file)
        results.append(result)
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    total_metamask_entries = 0
    files_with_data = []
    
    for result in results:
        if result['metamask_data']:
            total_metamask_entries += len(result['metamask_data'])
            files_with_data.append(result['file'])
            print(f"\n{result['file']}:")
            print(f"  - Size: {result['size']} bytes")
            print(f"  - MetaMask entries: {len(result['metamask_data'])}")
            print(f"  - Patterns found: {', '.join(result['found_patterns'])}")
            
            # Show first few entries
            for i, entry in enumerate(result['metamask_data'][:3]):
                print(f"  - Entry {i+1}: {str(entry['json_data'])[:100]}...")
    
    print(f"\nTotal MetaMask entries found: {total_metamask_entries}")
    print(f"Files with potential data: {len(files_with_data)}")
    
    if files_with_data:
        print("\nFiles to focus on:")
        for f in files_with_data:
            print(f"  - {f}")
    
    # Save detailed results
    with open('ldb_analysis_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\nDetailed results saved to: ldb_analysis_results.json")

if __name__ == "__main__":
    main()
