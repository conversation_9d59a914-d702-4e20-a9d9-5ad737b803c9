# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@ampproject/remapping@npm:^2.1.0":
  version: 2.2.0
  resolution: "@ampproject/remapping@npm:2.2.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.1.0
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: d74d170d06468913921d72430259424b7e4c826b5a7d39ff839a29d547efb97dc577caa8ba3fb5cf023624e9af9d09651afc3d4112a45e2050328abc9b3a2292
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.12.13, @babel/code-frame@npm:^7.18.6, @babel/code-frame@npm:^7.22.13":
  version: 7.22.13
  resolution: "@babel/code-frame@npm:7.22.13"
  dependencies:
    "@babel/highlight": ^7.22.13
    chalk: ^2.4.2
  checksum: 22e342c8077c8b77eeb11f554ecca2ba14153f707b85294fcf6070b6f6150aae88a7b7436dd88d8c9289970585f3fe5b9b941c5aa3aa26a6d5a8ef3f292da058
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.17.7, @babel/compat-data@npm:^7.20.1, @babel/compat-data@npm:^7.20.5":
  version: 7.20.14
  resolution: "@babel/compat-data@npm:7.20.14"
  checksum: 6c9efe36232094e4ad0b70d165587f21ca718e5d011f7a52a77a18502a7524e90e2855aa5a2e086395bcfd21bd2c7c99128dcd8d9fdffe94316b72acf5c66f2c
  languageName: node
  linkType: hard

"@babel/core@npm:^7.11.6, @babel/core@npm:^7.12.3":
  version: 7.20.12
  resolution: "@babel/core@npm:7.20.12"
  dependencies:
    "@ampproject/remapping": ^2.1.0
    "@babel/code-frame": ^7.18.6
    "@babel/generator": ^7.20.7
    "@babel/helper-compilation-targets": ^7.20.7
    "@babel/helper-module-transforms": ^7.20.11
    "@babel/helpers": ^7.20.7
    "@babel/parser": ^7.20.7
    "@babel/template": ^7.20.7
    "@babel/traverse": ^7.20.12
    "@babel/types": ^7.20.7
    convert-source-map: ^1.7.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.2.2
    semver: ^6.3.0
  checksum: 62e6c3e2149a70b5c9729ef5f0d3e2e97e9dcde89fc039c8d8e3463d5d7ba9b29ee84d10faf79b61532ac1645aa62f2bd42338320617e6e3a8a4d8e2a27076e7
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.20.7, @babel/generator@npm:^7.23.0, @babel/generator@npm:^7.7.2":
  version: 7.23.0
  resolution: "@babel/generator@npm:7.23.0"
  dependencies:
    "@babel/types": ^7.23.0
    "@jridgewell/gen-mapping": ^0.3.2
    "@jridgewell/trace-mapping": ^0.3.17
    jsesc: ^2.5.1
  checksum: 8efe24adad34300f1f8ea2add420b28171a646edc70f2a1b3e1683842f23b8b7ffa7e35ef0119294e1901f45bfea5b3dc70abe1f10a1917ccdfb41bed69be5f1
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/helper-annotate-as-pure@npm:7.18.6"
  dependencies:
    "@babel/types": ^7.18.6
  checksum: 88ccd15ced475ef2243fdd3b2916a29ea54c5db3cd0cfabf9d1d29ff6e63b7f7cd1c27264137d7a40ac2e978b9b9a542c332e78f40eb72abe737a7400788fc1b
  languageName: node
  linkType: hard

"@babel/helper-builder-binary-assignment-operator-visitor@npm:^7.18.6":
  version: 7.18.9
  resolution: "@babel/helper-builder-binary-assignment-operator-visitor@npm:7.18.9"
  dependencies:
    "@babel/helper-explode-assignable-expression": ^7.18.6
    "@babel/types": ^7.18.9
  checksum: b4bc214cb56329daff6cc18a7f7a26aeafb55a1242e5362f3d47fe3808421f8c7cd91fff95d6b9b7ccb67e14e5a67d944e49dbe026942bfcbfda19b1c72a8e72
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.17.7, @babel/helper-compilation-targets@npm:^7.18.9, @babel/helper-compilation-targets@npm:^7.20.0, @babel/helper-compilation-targets@npm:^7.20.7":
  version: 7.20.7
  resolution: "@babel/helper-compilation-targets@npm:7.20.7"
  dependencies:
    "@babel/compat-data": ^7.20.5
    "@babel/helper-validator-option": ^7.18.6
    browserslist: ^4.21.3
    lru-cache: ^5.1.1
    semver: ^6.3.0
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 8c32c873ba86e2e1805b30e0807abd07188acbe00ebb97576f0b09061cc65007f1312b589eccb4349c5a8c7f8bb9f2ab199d41da7030bf103d9f347dcd3a3cf4
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.18.6, @babel/helper-create-class-features-plugin@npm:^7.20.5, @babel/helper-create-class-features-plugin@npm:^7.20.7":
  version: 7.20.12
  resolution: "@babel/helper-create-class-features-plugin@npm:7.20.12"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.18.6
    "@babel/helper-environment-visitor": ^7.18.9
    "@babel/helper-function-name": ^7.19.0
    "@babel/helper-member-expression-to-functions": ^7.20.7
    "@babel/helper-optimise-call-expression": ^7.18.6
    "@babel/helper-replace-supers": ^7.20.7
    "@babel/helper-skip-transparent-expression-wrappers": ^7.20.0
    "@babel/helper-split-export-declaration": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 1e9ed4243b75278fa24deb40dc62bf537b79307987223a2d2d2ae5abf7ba6dc8435d6e3bb55d52ceb30d3e1eba88e7eb6a1885a8bb519e5cfc3e9dedb97d43e6
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.20.5":
  version: 7.20.5
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.20.5"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.18.6
    regexpu-core: ^5.2.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 7f29c3cb7447cca047b0d394f8ab98e4923d00e86a7afa56e5df9770c48ec107891505d2d1f06b720ecc94ed24bf58d90986cc35fe4a43b549eb7b7a5077b693
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.3.3":
  version: 0.3.3
  resolution: "@babel/helper-define-polyfill-provider@npm:0.3.3"
  dependencies:
    "@babel/helper-compilation-targets": ^7.17.7
    "@babel/helper-plugin-utils": ^7.16.7
    debug: ^4.1.1
    lodash.debounce: ^4.0.8
    resolve: ^1.14.2
    semver: ^6.1.2
  peerDependencies:
    "@babel/core": ^7.4.0-0
  checksum: 8e3fe75513302e34f6d92bd67b53890e8545e6c5bca8fe757b9979f09d68d7e259f6daea90dc9e01e332c4f8781bda31c5fe551c82a277f9bc0bec007aed497c
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.18.9, @babel/helper-environment-visitor@npm:^7.22.20":
  version: 7.22.20
  resolution: "@babel/helper-environment-visitor@npm:7.22.20"
  checksum: d80ee98ff66f41e233f36ca1921774c37e88a803b2f7dca3db7c057a5fea0473804db9fb6729e5dbfd07f4bed722d60f7852035c2c739382e84c335661590b69
  languageName: node
  linkType: hard

"@babel/helper-explode-assignable-expression@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/helper-explode-assignable-expression@npm:7.18.6"
  dependencies:
    "@babel/types": ^7.18.6
  checksum: 225cfcc3376a8799023d15dc95000609e9d4e7547b29528c7f7111a0e05493ffb12c15d70d379a0bb32d42752f340233c4115bded6d299bc0c3ab7a12be3d30f
  languageName: node
  linkType: hard

"@babel/helper-function-name@npm:^7.18.9, @babel/helper-function-name@npm:^7.19.0, @babel/helper-function-name@npm:^7.23.0":
  version: 7.23.0
  resolution: "@babel/helper-function-name@npm:7.23.0"
  dependencies:
    "@babel/template": ^7.22.15
    "@babel/types": ^7.23.0
  checksum: e44542257b2d4634a1f979244eb2a4ad8e6d75eb6761b4cfceb56b562f7db150d134bc538c8e6adca3783e3bc31be949071527aa8e3aab7867d1ad2d84a26e10
  languageName: node
  linkType: hard

"@babel/helper-hoist-variables@npm:^7.18.6, @babel/helper-hoist-variables@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-hoist-variables@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 394ca191b4ac908a76e7c50ab52102669efe3a1c277033e49467913c7ed6f7c64d7eacbeabf3bed39ea1f41731e22993f763b1edce0f74ff8563fd1f380d92cc
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.20.7":
  version: 7.20.7
  resolution: "@babel/helper-member-expression-to-functions@npm:7.20.7"
  dependencies:
    "@babel/types": ^7.20.7
  checksum: cec17aab7e964830b0146e575bd141127032319f26ed864a65b35abd75ad618d264d3e11449b9b4e29cfd95bb1a7e774afddd4884fdcc29c36ac9cbd2b66359f
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/helper-module-imports@npm:7.18.6"
  dependencies:
    "@babel/types": ^7.18.6
  checksum: f393f8a3b3304b1b7a288a38c10989de754f01d29caf62ce7c4e5835daf0a27b81f3ac687d9d2780d39685aae7b55267324b512150e7b2be967b0c493b6a1def
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.18.6, @babel/helper-module-transforms@npm:^7.20.11":
  version: 7.20.11
  resolution: "@babel/helper-module-transforms@npm:7.20.11"
  dependencies:
    "@babel/helper-environment-visitor": ^7.18.9
    "@babel/helper-module-imports": ^7.18.6
    "@babel/helper-simple-access": ^7.20.2
    "@babel/helper-split-export-declaration": ^7.18.6
    "@babel/helper-validator-identifier": ^7.19.1
    "@babel/template": ^7.20.7
    "@babel/traverse": ^7.20.10
    "@babel/types": ^7.20.7
  checksum: 29319ebafa693d48756c6ba0d871677bb0037e0da084fbe221a17c38d57093fc8aa38543c07d76e788266a937976e37ab4901971ca7f237c5ab45f524b9ecca0
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/helper-optimise-call-expression@npm:7.18.6"
  dependencies:
    "@babel/types": ^7.18.6
  checksum: e518fe8418571405e21644cfb39cf694f30b6c47b10b006609a92469ae8b8775cbff56f0b19732343e2ea910641091c5a2dc73b56ceba04e116a33b0f8bd2fbd
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.16.7, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.18.9, @babel/helper-plugin-utils@npm:^7.19.0, @babel/helper-plugin-utils@npm:^7.20.2, @babel/helper-plugin-utils@npm:^7.8.0, @babel/helper-plugin-utils@npm:^7.8.3":
  version: 7.20.2
  resolution: "@babel/helper-plugin-utils@npm:7.20.2"
  checksum: f6cae53b7fdb1bf3abd50fa61b10b4470985b400cc794d92635da1e7077bb19729f626adc0741b69403d9b6e411cddddb9c0157a709cc7c4eeb41e663be5d74b
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.18.9":
  version: 7.18.9
  resolution: "@babel/helper-remap-async-to-generator@npm:7.18.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.18.6
    "@babel/helper-environment-visitor": ^7.18.9
    "@babel/helper-wrap-function": ^7.18.9
    "@babel/types": ^7.18.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 4be6076192308671b046245899b703ba090dbe7ad03e0bea897bb2944ae5b88e5e85853c9d1f83f643474b54c578d8ac0800b80341a86e8538264a725fbbefec
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.18.6, @babel/helper-replace-supers@npm:^7.20.7":
  version: 7.20.7
  resolution: "@babel/helper-replace-supers@npm:7.20.7"
  dependencies:
    "@babel/helper-environment-visitor": ^7.18.9
    "@babel/helper-member-expression-to-functions": ^7.20.7
    "@babel/helper-optimise-call-expression": ^7.18.6
    "@babel/template": ^7.20.7
    "@babel/traverse": ^7.20.7
    "@babel/types": ^7.20.7
  checksum: b8e0087c9b0c1446e3c6f3f72b73b7e03559c6b570e2cfbe62c738676d9ebd8c369a708cf1a564ef88113b4330750a50232ee1131d303d478b7a5e65e46fbc7c
  languageName: node
  linkType: hard

"@babel/helper-simple-access@npm:^7.20.2":
  version: 7.20.2
  resolution: "@babel/helper-simple-access@npm:7.20.2"
  dependencies:
    "@babel/types": ^7.20.2
  checksum: ad1e96ee2e5f654ffee2369a586e5e8d2722bf2d8b028a121b4c33ebae47253f64d420157b9f0a8927aea3a9e0f18c0103e74fdd531815cf3650a0a4adca11a1
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.20.0":
  version: 7.20.0
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.20.0"
  dependencies:
    "@babel/types": ^7.20.0
  checksum: 34da8c832d1c8a546e45d5c1d59755459ffe43629436707079989599b91e8c19e50e73af7a4bd09c95402d389266731b0d9c5f69e372d8ebd3a709c05c80d7dd
  languageName: node
  linkType: hard

"@babel/helper-split-export-declaration@npm:^7.18.6, @babel/helper-split-export-declaration@npm:^7.22.6":
  version: 7.22.6
  resolution: "@babel/helper-split-export-declaration@npm:7.22.6"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: e141cace583b19d9195f9c2b8e17a3ae913b7ee9b8120246d0f9ca349ca6f03cb2c001fd5ec57488c544347c0bb584afec66c936511e447fd20a360e591ac921
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-string-parser@npm:7.22.5"
  checksum: 836851ca5ec813077bbb303acc992d75a360267aa3b5de7134d220411c852a6f17de7c0d0b8c8dcc0f567f67874c00f4528672b2a4f1bc978a3ada64c8c78467
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.19.1, @babel/helper-validator-identifier@npm:^7.22.20":
  version: 7.22.20
  resolution: "@babel/helper-validator-identifier@npm:7.22.20"
  checksum: 136412784d9428266bcdd4d91c32bcf9ff0e8d25534a9d94b044f77fe76bc50f941a90319b05aafd1ec04f7d127cd57a179a3716009ff7f3412ef835ada95bdc
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/helper-validator-option@npm:7.18.6"
  checksum: f9cc6eb7cc5d759c5abf006402180f8d5e4251e9198197428a97e05d65eb2f8ae5a0ce73b1dfd2d35af41d0eb780627a64edf98a4e71f064eeeacef8de58f2cf
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.18.9":
  version: 7.20.5
  resolution: "@babel/helper-wrap-function@npm:7.20.5"
  dependencies:
    "@babel/helper-function-name": ^7.19.0
    "@babel/template": ^7.18.10
    "@babel/traverse": ^7.20.5
    "@babel/types": ^7.20.5
  checksum: 11a6fc28334368a193a9cb3ad16f29cd7603bab958433efc82ebe59fa6556c227faa24f07ce43983f7a85df826f71d441638442c4315e90a554fe0a70ca5005b
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.20.7":
  version: 7.20.13
  resolution: "@babel/helpers@npm:7.20.13"
  dependencies:
    "@babel/template": ^7.20.7
    "@babel/traverse": ^7.20.13
    "@babel/types": ^7.20.7
  checksum: d62076fa834f342798f8c3fd7aec0870cc1725d273d99e540cbaa8d6c3ed10258228dd14601c8e66bfeabbb9424c3b31090ecc467fe855f7bd72c4734df7fb09
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.22.13":
  version: 7.22.20
  resolution: "@babel/highlight@npm:7.22.20"
  dependencies:
    "@babel/helper-validator-identifier": ^7.22.20
    chalk: ^2.4.2
    js-tokens: ^4.0.0
  checksum: 84bd034dca309a5e680083cd827a766780ca63cef37308404f17653d32366ea76262bd2364b2d38776232f2d01b649f26721417d507e8b4b6da3e4e739f6d134
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.14.7, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.22.15, @babel/parser@npm:^7.23.0":
  version: 7.23.0
  resolution: "@babel/parser@npm:7.23.0"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 453fdf8b9e2c2b7d7b02139e0ce003d1af21947bbc03eb350fb248ee335c9b85e4ab41697ddbdd97079698de825a265e45a0846bb2ed47a2c7c1df833f42a354
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 845bd280c55a6a91d232cfa54eaf9708ec71e594676fe705794f494bb8b711d833b752b59d1a5c154695225880c23dbc9cab0e53af16fd57807976cd3ff41b8d
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.18.9":
  version: 7.20.7
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.20.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/helper-skip-transparent-expression-wrappers": ^7.20.0
    "@babel/plugin-proposal-optional-chaining": ^7.20.7
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: d610f532210bee5342f5b44a12395ccc6d904e675a297189bc1e401cc185beec09873da523466d7fec34ae1574f7a384235cba1ccc9fe7b89ba094167897c845
  languageName: node
  linkType: hard

"@babel/plugin-proposal-async-generator-functions@npm:^7.20.1":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-async-generator-functions@npm:7.20.7"
  dependencies:
    "@babel/helper-environment-visitor": ^7.18.9
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/helper-remap-async-to-generator": ^7.18.9
    "@babel/plugin-syntax-async-generators": ^7.8.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 111109ee118c9e69982f08d5e119eab04190b36a0f40e22e873802d941956eee66d2aa5a15f5321e51e3f9aa70a91136451b987fe15185ef8cc547ac88937723
  languageName: node
  linkType: hard

"@babel/plugin-proposal-class-properties@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-class-properties@npm:7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 49a78a2773ec0db56e915d9797e44fd079ab8a9b2e1716e0df07c92532f2c65d76aeda9543883916b8e0ff13606afeffa67c5b93d05b607bc87653ad18a91422
  languageName: node
  linkType: hard

"@babel/plugin-proposal-class-static-block@npm:^7.18.6":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-class-static-block@npm:7.20.7"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.20.7
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/plugin-syntax-class-static-block": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: ce1f3e8fd96437d820aa36323b7b3a0cb65b5f2600612665129880d5a4eb7194ce6a298ed2a5a4d3a9ea49bd33089ab95503c4c5b3ba9cea251a07d1706453d9
  languageName: node
  linkType: hard

"@babel/plugin-proposal-dynamic-import@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-dynamic-import@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 96b1c8a8ad8171d39e9ab106be33bde37ae09b22fb2c449afee9a5edf3c537933d79d963dcdc2694d10677cb96da739cdf1b53454e6a5deab9801f28a818bb2f
  languageName: node
  linkType: hard

"@babel/plugin-proposal-export-namespace-from@npm:^7.18.9":
  version: 7.18.9
  resolution: "@babel/plugin-proposal-export-namespace-from@npm:7.18.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.9
    "@babel/plugin-syntax-export-namespace-from": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 84ff22bacc5d30918a849bfb7e0e90ae4c5b8d8b65f2ac881803d1cf9068dffbe53bd657b0e4bc4c20b4db301b1c85f1e74183cf29a0dd31e964bd4e97c363ef
  languageName: node
  linkType: hard

"@babel/plugin-proposal-json-strings@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-json-strings@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
    "@babel/plugin-syntax-json-strings": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 25ba0e6b9d6115174f51f7c6787e96214c90dd4026e266976b248a2ed417fe50fddae72843ffb3cbe324014a18632ce5648dfac77f089da858022b49fd608cb3
  languageName: node
  linkType: hard

"@babel/plugin-proposal-logical-assignment-operators@npm:^7.18.9":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-logical-assignment-operators@npm:7.20.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cdd7b8136cc4db3f47714d5266f9e7b592a2ac5a94a5878787ce08890e97c8ab1ca8e94b27bfeba7b0f2b1549a026d9fc414ca2196de603df36fb32633bbdc19
  languageName: node
  linkType: hard

"@babel/plugin-proposal-nullish-coalescing-operator@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-nullish-coalescing-operator@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 949c9ddcdecdaec766ee610ef98f965f928ccc0361dd87cf9f88cf4896a6ccd62fce063d4494778e50da99dea63d270a1be574a62d6ab81cbe9d85884bf55a7d
  languageName: node
  linkType: hard

"@babel/plugin-proposal-numeric-separator@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-numeric-separator@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f370ea584c55bf4040e1f78c80b4eeb1ce2e6aaa74f87d1a48266493c33931d0b6222d8cee3a082383d6bb648ab8d6b7147a06f974d3296ef3bc39c7851683ec
  languageName: node
  linkType: hard

"@babel/plugin-proposal-object-rest-spread@npm:^7.20.2":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-object-rest-spread@npm:7.20.7"
  dependencies:
    "@babel/compat-data": ^7.20.5
    "@babel/helper-compilation-targets": ^7.20.7
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-transform-parameters": ^7.20.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1329db17009964bc644484c660eab717cb3ca63ac0ab0f67c651a028d1bc2ead51dc4064caea283e46994f1b7221670a35cbc0b4beb6273f55e915494b5aa0b2
  languageName: node
  linkType: hard

"@babel/plugin-proposal-optional-catch-binding@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-optional-catch-binding@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7b5b39fb5d8d6d14faad6cb68ece5eeb2fd550fb66b5af7d7582402f974f5bc3684641f7c192a5a57e0f59acfae4aada6786be1eba030881ddc590666eff4d1e
  languageName: node
  linkType: hard

"@babel/plugin-proposal-optional-chaining@npm:^7.18.9, @babel/plugin-proposal-optional-chaining@npm:^7.20.7":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-optional-chaining@npm:7.20.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/helper-skip-transparent-expression-wrappers": ^7.20.0
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 274b8932335bd064ca24cf1a4da2b2c20c92726d4bfa8b0cb5023857479b8481feef33505c16650c7b9239334e5c6959babc924816324c4cf223dd91c7ca79bc
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-methods@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-private-methods@npm:7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 22d8502ee96bca99ad2c8393e8493e2b8d4507576dd054490fd8201a36824373440106f5b098b6d821b026c7e72b0424ff4aeca69ed5f42e48f029d3a156d5ad
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:^7.18.6":
  version: 7.20.5
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.20.5"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.18.6
    "@babel/helper-create-class-features-plugin": ^7.20.5
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 513b5e0e2c1b2846be5336cf680e932ae17924ef885aa1429e1a4f7924724bdd99b15f28d67187d0a006d5f18a0c4b61d96c3ecb4902fed3c8fe2f0abfc9753a
  languageName: node
  linkType: hard

"@babel/plugin-proposal-unicode-property-regex@npm:^7.18.6, @babel/plugin-proposal-unicode-property-regex@npm:^7.4.4":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-unicode-property-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a8575ecb7ff24bf6c6e94808d5c84bb5a0c6dd7892b54f09f4646711ba0ee1e1668032b3c43e3e1dfec2c5716c302e851ac756c1645e15882d73df6ad21ae951
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7ed1c1d9b9e5b64ef028ea5e755c0be2d4e5e4e3d6cf7df757b9a8c4cfa4193d268176d0f1f7fbecdda6fe722885c7fda681f480f3741d8a2d26854736f05367
  languageName: node
  linkType: hard

"@babel/plugin-syntax-bigint@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-bigint@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3a10849d83e47aec50f367a9e56a6b22d662ddce643334b087f9828f4c3dd73bdc5909aaeabe123fed78515767f9ca43498a0e621c438d1cd2802d7fae3c9648
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13, @babel/plugin-syntax-class-properties@npm:^7.8.3":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": ^7.12.13
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 24f34b196d6342f28d4bad303612d7ff566ab0a013ce89e775d98d6f832969462e7235f3e7eaf17678a533d4be0ba45d3ae34ab4e5a9dcbda5d98d49e5efa2fc
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3e80814b5b6d4fe17826093918680a351c2d34398a914ce6e55d8083d72a9bdde4fbaf6a2dcea0e23a03de26dc2917ae3efd603d27099e2b98380345703bf948
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ce307af83cf433d4ec42932329fad25fa73138ab39c7436882ea28742e1c0066626d224e0ad2988724c82644e41601cef607b36194f695cb78a1fcdc959637bd
  languageName: node
  linkType: hard

"@babel/plugin-syntax-export-namespace-from@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-export-namespace-from@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85740478be5b0de185228e7814451d74ab8ce0a26fcca7613955262a26e99e8e15e9da58f60c754b84515d4c679b590dbd3f2148f0f58025f4ae706f1c5a5d4a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.20.0":
  version: 7.20.0
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.20.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.19.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6a86220e0aae40164cd3ffaf80e7c076a1be02a8f3480455dddbae05fda8140f429290027604df7a11b3f3f124866e8a6d69dbfa1dda61ee7377b920ad144d5b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.8.3":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 166ac1125d10b9c0c430e4156249a13858c0366d38844883d75d27389621ebe651115cb2ceb6dc011534d5055719fa1727b59f39e1ab3ca97820eef3dcab5b9b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bf5aea1f3188c9a507e16efe030efb996853ca3cadd6512c51db7233cc58f3ac89ff8c6bdfb01d30843b161cfe7d321e1bf28da82f7ab8d7e6bc5464666f354a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.7.2":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-jsx@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6d37ea972970195f1ffe1a54745ce2ae456e0ac6145fae9aa1480f297248b262ea6ebb93010eddb86ebfacb94f57c05a1fc5d232b9a67325b09060299d515c67
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4, @babel/plugin-syntax-logical-assignment-operators@npm:^7.8.3":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: aff33577037e34e515911255cdbb1fd39efee33658aa00b8a5fd3a4b903585112d037cce1cc9e4632f0487dc554486106b79ccd5ea63a2e00df4363f6d4ff886
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 87aca4918916020d1fedba54c0e232de408df2644a425d153be368313fdde40d96088feed6c4e5ab72aac89be5d07fef2ddf329a15109c5eb65df006bf2580d1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4, @babel/plugin-syntax-numeric-separator@npm:^7.8.3":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 01ec5547bd0497f76cc903ff4d6b02abc8c05f301c88d2622b6d834e33a5651aa7c7a3d80d8d57656a4588f7276eba357f6b7e006482f5b564b7a6488de493a1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fddcf581a57f77e80eb6b981b10658421bc321ba5f0a5b754118c6a92a5448f12a0c336f77b8abf734841e102e5126d69110a306eadb03ca3e1547cab31f5cbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 910d90e72bc90ea1ce698e89c1027fed8845212d5ab588e35ef91f13b93143845f94e2539d831dc8d8ededc14ec02f04f7bd6a8179edd43a326c784e7ed7f0b9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: eef94d53a1453361553c1f98b68d17782861a04a392840341bc91780838dd4e695209c783631cf0de14c635758beafb6a3a65399846ffa4386bff90639347f30
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b317174783e6e96029b743ccff2a67d63d38756876e7e5d0ba53a322e38d9ca452c13354a57de1ad476b4c066dbae699e0ca157441da611117a47af88985ecda
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5, @babel/plugin-syntax-top-level-await@npm:^7.8.3":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bbd1a56b095be7820029b209677b194db9b1d26691fe999856462e66b25b281f031f3dfd91b1619e9dcf95bebe336211833b854d0fb8780d618e35667c2d0d7e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.7.2":
  version: 7.20.0
  resolution: "@babel/plugin-syntax-typescript@npm:7.20.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.19.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6189c0b5c32ba3c9a80a42338bd50719d783b20ef29b853d4f03929e971913d3cefd80184e924ae98ad6db09080be8fe6f1ffde9a6db8972523234f0274d36f7
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.18.6":
  version: 7.20.7
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.20.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.20.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b43cabe3790c2de7710abe32df9a30005eddb2050dadd5d122c6872f679e5710e410f1b90c8f99a2aff7b614cccfecf30e7fd310236686f60d3ed43fd80b9847
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.18.6":
  version: 7.20.7
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.20.7"
  dependencies:
    "@babel/helper-module-imports": ^7.18.6
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/helper-remap-async-to-generator": ^7.18.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fe9ee8a5471b4317c1b9ea92410ace8126b52a600d7cfbfe1920dcac6fb0fad647d2e08beb4fd03c630eb54430e6c72db11e283e3eddc49615c68abd39430904
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0a0df61f94601e3666bf39f2cc26f5f7b22a94450fb93081edbed967bd752ce3f81d1227fefd3799f5ee2722171b5e28db61379234d1bb85b6ec689589f99d7e
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.20.2":
  version: 7.20.15
  resolution: "@babel/plugin-transform-block-scoping@npm:7.20.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.20.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1dddf7be578306837074cb5059f8408af0b1c0bfcf922ed920d4aa65d08fb7c6e6129ca254e9879c4c6d2a6be4937111551f51922e8b0e071ed16eb6564a4dbb
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.20.2":
  version: 7.20.7
  resolution: "@babel/plugin-transform-classes@npm:7.20.7"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.18.6
    "@babel/helper-compilation-targets": ^7.20.7
    "@babel/helper-environment-visitor": ^7.18.9
    "@babel/helper-function-name": ^7.19.0
    "@babel/helper-optimise-call-expression": ^7.18.6
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/helper-replace-supers": ^7.20.7
    "@babel/helper-split-export-declaration": ^7.18.6
    globals: ^11.1.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4cf55ad88e52c7c66a991add4c8e1c3324384bd52df7085962d396879561456a44352e5ab1725cc80f4e83737a2931e847c4a96c7aa4a549357f23631ff31799
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.18.9":
  version: 7.20.7
  resolution: "@babel/plugin-transform-computed-properties@npm:7.20.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/template": ^7.20.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: be70e54bda8b469146459f429e5f2bd415023b87b2d5af8b10e48f465ffb02847a3ed162ca60378c004b82db848e4d62e90010d41ded7e7176b6d8d1c2911139
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.20.2":
  version: 7.20.7
  resolution: "@babel/plugin-transform-destructuring@npm:7.20.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.20.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bd8affdb142c77662037215e37128b2110a786c92a67e1f00b38223c438c1610bd84cbc0386e9cd3479245ea811c5ca6c9838f49be4729b592159a30ce79add2
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.18.6, @babel/plugin-transform-dotall-regex@npm:^7.4.4":
  version: 7.18.6
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cbe5d7063eb8f8cca24cd4827bc97f5641166509e58781a5f8aa47fb3d2d786ce4506a30fca2e01f61f18792783a5cb5d96bf5434c3dd1ad0de8c9cc625a53da
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.18.9":
  version: 7.18.9
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.18.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 220bf4a9fec5c4d4a7b1de38810350260e8ea08481bf78332a464a21256a95f0df8cd56025f346238f09b04f8e86d4158fafc9f4af57abaef31637e3b58bd4fe
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.18.6"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7f70222f6829c82a36005508d34ddbe6fd0974ae190683a8670dd6ff08669aaf51fef2209d7403f9bd543cb2d12b18458016c99a6ed0332ccedb3ea127b01229
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.18.8":
  version: 7.18.8
  resolution: "@babel/plugin-transform-for-of@npm:7.18.8"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ca64c623cf0c7a80ab6f07ebd3e6e4ade95e2ae806696f70b43eafe6394fa8ce21f2b1ffdd15df2067f7363d2ecfe26472a97c6c774403d2163fa05f50c98f17
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.18.9":
  version: 7.18.9
  resolution: "@babel/plugin-transform-function-name@npm:7.18.9"
  dependencies:
    "@babel/helper-compilation-targets": ^7.18.9
    "@babel/helper-function-name": ^7.18.9
    "@babel/helper-plugin-utils": ^7.18.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 62dd9c6cdc9714704efe15545e782ee52d74dc73916bf954b4d3bee088fb0ec9e3c8f52e751252433656c09f744b27b757fc06ed99bcde28e8a21600a1d8e597
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.18.9":
  version: 7.18.9
  resolution: "@babel/plugin-transform-literals@npm:7.18.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3458dd2f1a47ac51d9d607aa18f3d321cbfa8560a985199185bed5a906bb0c61ba85575d386460bac9aed43fdd98940041fae5a67dff286f6f967707cff489f8
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 35a3d04f6693bc6b298c05453d85ee6e41cc806538acb6928427e0e97ae06059f97d2f07d21495fcf5f70d3c13a242e2ecbd09d5c1fcb1b1a73ff528dcb0b695
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.19.6":
  version: 7.20.11
  resolution: "@babel/plugin-transform-modules-amd@npm:7.20.11"
  dependencies:
    "@babel/helper-module-transforms": ^7.20.11
    "@babel/helper-plugin-utils": ^7.20.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 23665c1c20c8f11c89382b588fb9651c0756d130737a7625baeaadbd3b973bc5bfba1303bedffa8fb99db1e6d848afb01016e1df2b69b18303e946890c790001
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.19.6":
  version: 7.20.11
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.20.11"
  dependencies:
    "@babel/helper-module-transforms": ^7.20.11
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/helper-simple-access": ^7.20.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ddd0623e2ad4b5c0faaa0ae30d3407a3fa484d911c968ed33cfb1b339ac3691321c959db60b66dc136dbd67770fff586f7e48a7ce0d7d357f92d6ef6fb7ed1a7
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.19.6":
  version: 7.20.11
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.20.11"
  dependencies:
    "@babel/helper-hoist-variables": ^7.18.6
    "@babel/helper-module-transforms": ^7.20.11
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/helper-validator-identifier": ^7.19.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4546c47587f88156d66c7eb7808e903cf4bb3f6ba6ac9bc8e3af2e29e92eb9f0b3f44d52043bfd24eb25fa7827fd7b6c8bfeac0cac7584e019b87e1ecbd0e673
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-transform-modules-umd@npm:7.18.6"
  dependencies:
    "@babel/helper-module-transforms": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c3b6796c6f4579f1ba5ab0cdcc73910c1e9c8e1e773c507c8bb4da33072b3ae5df73c6d68f9126dab6e99c24ea8571e1563f8710d7c421fac1cde1e434c20153
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.19.1":
  version: 7.20.5
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.20.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.20.5
    "@babel/helper-plugin-utils": ^7.20.2
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 528c95fb1087e212f17e1c6456df041b28a83c772b9c93d2e407c9d03b72182b0d9d126770c1d6e0b23aab052599ceaf25ed6a2c0627f4249be34a83f6fae853
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-transform-new-target@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bd780e14f46af55d0ae8503b3cb81ca86dcc73ed782f177e74f498fff934754f9e9911df1f8f3bd123777eed7c1c1af4d66abab87c8daae5403e7719a6b845d1
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-transform-object-super@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
    "@babel/helper-replace-supers": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0fcb04e15deea96ae047c21cb403607d49f06b23b4589055993365ebd7a7d7541334f06bf9642e90075e66efce6ebaf1eb0ef066fbbab802d21d714f1aac3aef
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.20.1, @babel/plugin-transform-parameters@npm:^7.20.7":
  version: 7.20.7
  resolution: "@babel/plugin-transform-parameters@npm:7.20.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.20.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6ffe0dd9afb2d2b9bc247381aa2e95dd9997ff5568a0a11900528919a4e073ac68f46409431455badb8809644d47cff180045bc2b9700e3f36e3b23554978947
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-transform-property-literals@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1c16e64de554703f4b547541de2edda6c01346dd3031d4d29e881aa7733785cd26d53611a4ccf5353f4d3e69097bb0111c0a93ace9e683edd94fea28c4484144
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.18.6":
  version: 7.20.5
  resolution: "@babel/plugin-transform-regenerator@npm:7.20.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.20.2
    regenerator-transform: ^0.15.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 13164861e71fb23d84c6270ef5330b03c54d5d661c2c7468f28e21c4f8598558ca0c8c3cb1d996219352946e849d270a61372bc93c8fbe9676e78e3ffd0dea07
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-transform-reserved-words@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0738cdc30abdae07c8ec4b233b30c31f68b3ff0eaa40eddb45ae607c066127f5fa99ddad3c0177d8e2832e3a7d3ad115775c62b431ebd6189c40a951b867a80c
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b8e4e8acc2700d1e0d7d5dbfd4fdfb935651913de6be36e6afb7e739d8f9ca539a5150075a0f9b79c88be25ddf45abb912fe7abf525f0b80f5b9d9860de685d7
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.19.0":
  version: 7.20.7
  resolution: "@babel/plugin-transform-spread@npm:7.20.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/helper-skip-transparent-expression-wrappers": ^7.20.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8ea698a12da15718aac7489d4cde10beb8a3eea1f66167d11ab1e625033641e8b328157fd1a0b55dd6531933a160c01fc2e2e61132a385cece05f26429fd0cc2
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 68ea18884ae9723443ffa975eb736c8c0d751265859cd3955691253f7fee37d7a0f7efea96c8a062876af49a257a18ea0ed5fea0d95a7b3611ce40f7ee23aee3
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.18.9":
  version: 7.18.9
  resolution: "@babel/plugin-transform-template-literals@npm:7.18.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3d2fcd79b7c345917f69b92a85bdc3ddd68ce2c87dc70c7d61a8373546ccd1f5cb8adc8540b49dfba08e1b82bb7b3bbe23a19efdb2b9c994db2db42906ca9fb2
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.18.9":
  version: 7.18.9
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.18.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e754e0d8b8a028c52e10c148088606e3f7a9942c57bd648fc0438e5b4868db73c386a5ed47ab6d6f0594aae29ee5ffc2ffc0f7ebee7fae560a066d6dea811cd4
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.18.10":
  version: 7.18.10
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.18.10"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f5baca55cb3c11bc08ec589f5f522d85c1ab509b4d11492437e45027d64ae0b22f0907bd1381e8d7f2a436384bb1f9ad89d19277314242c5c2671a0f91d0f9cd
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d9e18d57536a2d317fb0b7c04f8f55347f3cfacb75e636b4c6fa2080ab13a3542771b5120e726b598b815891fc606d1472ac02b749c69fd527b03847f22dc25e
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.20.2":
  version: 7.20.2
  resolution: "@babel/preset-env@npm:7.20.2"
  dependencies:
    "@babel/compat-data": ^7.20.1
    "@babel/helper-compilation-targets": ^7.20.0
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/helper-validator-option": ^7.18.6
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": ^7.18.6
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": ^7.18.9
    "@babel/plugin-proposal-async-generator-functions": ^7.20.1
    "@babel/plugin-proposal-class-properties": ^7.18.6
    "@babel/plugin-proposal-class-static-block": ^7.18.6
    "@babel/plugin-proposal-dynamic-import": ^7.18.6
    "@babel/plugin-proposal-export-namespace-from": ^7.18.9
    "@babel/plugin-proposal-json-strings": ^7.18.6
    "@babel/plugin-proposal-logical-assignment-operators": ^7.18.9
    "@babel/plugin-proposal-nullish-coalescing-operator": ^7.18.6
    "@babel/plugin-proposal-numeric-separator": ^7.18.6
    "@babel/plugin-proposal-object-rest-spread": ^7.20.2
    "@babel/plugin-proposal-optional-catch-binding": ^7.18.6
    "@babel/plugin-proposal-optional-chaining": ^7.18.9
    "@babel/plugin-proposal-private-methods": ^7.18.6
    "@babel/plugin-proposal-private-property-in-object": ^7.18.6
    "@babel/plugin-proposal-unicode-property-regex": ^7.18.6
    "@babel/plugin-syntax-async-generators": ^7.8.4
    "@babel/plugin-syntax-class-properties": ^7.12.13
    "@babel/plugin-syntax-class-static-block": ^7.14.5
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
    "@babel/plugin-syntax-export-namespace-from": ^7.8.3
    "@babel/plugin-syntax-import-assertions": ^7.20.0
    "@babel/plugin-syntax-json-strings": ^7.8.3
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
    "@babel/plugin-syntax-top-level-await": ^7.14.5
    "@babel/plugin-transform-arrow-functions": ^7.18.6
    "@babel/plugin-transform-async-to-generator": ^7.18.6
    "@babel/plugin-transform-block-scoped-functions": ^7.18.6
    "@babel/plugin-transform-block-scoping": ^7.20.2
    "@babel/plugin-transform-classes": ^7.20.2
    "@babel/plugin-transform-computed-properties": ^7.18.9
    "@babel/plugin-transform-destructuring": ^7.20.2
    "@babel/plugin-transform-dotall-regex": ^7.18.6
    "@babel/plugin-transform-duplicate-keys": ^7.18.9
    "@babel/plugin-transform-exponentiation-operator": ^7.18.6
    "@babel/plugin-transform-for-of": ^7.18.8
    "@babel/plugin-transform-function-name": ^7.18.9
    "@babel/plugin-transform-literals": ^7.18.9
    "@babel/plugin-transform-member-expression-literals": ^7.18.6
    "@babel/plugin-transform-modules-amd": ^7.19.6
    "@babel/plugin-transform-modules-commonjs": ^7.19.6
    "@babel/plugin-transform-modules-systemjs": ^7.19.6
    "@babel/plugin-transform-modules-umd": ^7.18.6
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.19.1
    "@babel/plugin-transform-new-target": ^7.18.6
    "@babel/plugin-transform-object-super": ^7.18.6
    "@babel/plugin-transform-parameters": ^7.20.1
    "@babel/plugin-transform-property-literals": ^7.18.6
    "@babel/plugin-transform-regenerator": ^7.18.6
    "@babel/plugin-transform-reserved-words": ^7.18.6
    "@babel/plugin-transform-shorthand-properties": ^7.18.6
    "@babel/plugin-transform-spread": ^7.19.0
    "@babel/plugin-transform-sticky-regex": ^7.18.6
    "@babel/plugin-transform-template-literals": ^7.18.9
    "@babel/plugin-transform-typeof-symbol": ^7.18.9
    "@babel/plugin-transform-unicode-escapes": ^7.18.10
    "@babel/plugin-transform-unicode-regex": ^7.18.6
    "@babel/preset-modules": ^0.1.5
    "@babel/types": ^7.20.2
    babel-plugin-polyfill-corejs2: ^0.3.3
    babel-plugin-polyfill-corejs3: ^0.6.0
    babel-plugin-polyfill-regenerator: ^0.4.1
    core-js-compat: ^3.25.1
    semver: ^6.3.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ece2d7e9c7789db6116e962b8e1a55eb55c110c44c217f0c8f6ffea4ca234954e66557f7bd019b7affadf7fbb3a53ccc807e93fc935aacd48146234b73b6947e
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:^0.1.5":
  version: 0.1.5
  resolution: "@babel/preset-modules@npm:0.1.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@babel/plugin-proposal-unicode-property-regex": ^7.4.4
    "@babel/plugin-transform-dotall-regex": ^7.4.4
    "@babel/types": ^7.4.4
    esutils: ^2.0.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8430e0e9e9d520b53e22e8c4c6a5a080a12b63af6eabe559c2310b187bd62ae113f3da82ba33e9d1d0f3230930ca702843aae9dd226dec51f7d7114dc1f51c10
  languageName: node
  linkType: hard

"@babel/regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "@babel/regjsgen@npm:0.8.0"
  checksum: 89c338fee774770e5a487382170711014d49a68eb281e74f2b5eac88f38300a4ad545516a7786a8dd5702e9cf009c94c2f582d200f077ac5decd74c56b973730
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.12.1, @babel/runtime@npm:^7.8.4, @babel/runtime@npm:^7.9.2":
  version: 7.20.13
  resolution: "@babel/runtime@npm:7.20.13"
  dependencies:
    regenerator-runtime: ^0.13.11
  checksum: 09b7a97a05c80540db6c9e4ddf8c5d2ebb06cae5caf3a87e33c33f27f8c4d49d9c67a2d72f1570e796045288fad569f98a26ceba0c4f5fad2af84b6ad855c4fb
  languageName: node
  linkType: hard

"@babel/template@npm:^7.18.10, @babel/template@npm:^7.20.7, @babel/template@npm:^7.22.15, @babel/template@npm:^7.3.3":
  version: 7.22.15
  resolution: "@babel/template@npm:7.22.15"
  dependencies:
    "@babel/code-frame": ^7.22.13
    "@babel/parser": ^7.22.15
    "@babel/types": ^7.22.15
  checksum: 1f3e7dcd6c44f5904c184b3f7fe280394b191f2fed819919ffa1e529c259d5b197da8981b6ca491c235aee8dbad4a50b7e31304aa531271cb823a4a24a0dd8fd
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.20.10, @babel/traverse@npm:^7.20.12, @babel/traverse@npm:^7.20.13, @babel/traverse@npm:^7.20.5, @babel/traverse@npm:^7.20.7, @babel/traverse@npm:^7.7.2":
  version: 7.23.2
  resolution: "@babel/traverse@npm:7.23.2"
  dependencies:
    "@babel/code-frame": ^7.22.13
    "@babel/generator": ^7.23.0
    "@babel/helper-environment-visitor": ^7.22.20
    "@babel/helper-function-name": ^7.23.0
    "@babel/helper-hoist-variables": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    "@babel/parser": ^7.23.0
    "@babel/types": ^7.23.0
    debug: ^4.1.0
    globals: ^11.1.0
  checksum: 26a1eea0dde41ab99dde8b9773a013a0dc50324e5110a049f5d634e721ff08afffd54940b3974a20308d7952085ac769689369e9127dea655f868c0f6e1ab35d
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.18.6, @babel/types@npm:^7.18.9, @babel/types@npm:^7.20.0, @babel/types@npm:^7.20.2, @babel/types@npm:^7.20.5, @babel/types@npm:^7.20.7, @babel/types@npm:^7.22.15, @babel/types@npm:^7.22.5, @babel/types@npm:^7.23.0, @babel/types@npm:^7.3.0, @babel/types@npm:^7.3.3, @babel/types@npm:^7.4.4, @babel/types@npm:^7.8.3":
  version: 7.23.0
  resolution: "@babel/types@npm:7.23.0"
  dependencies:
    "@babel/helper-string-parser": ^7.22.5
    "@babel/helper-validator-identifier": ^7.22.20
    to-fast-properties: ^2.0.0
  checksum: 215fe04bd7feef79eeb4d33374b39909ce9cad1611c4135a4f7fdf41fe3280594105af6d7094354751514625ea92d0875aba355f53e86a92600f290e77b0e604
  languageName: node
  linkType: hard

"@bcoe/v8-coverage@npm:^0.2.3":
  version: 0.2.3
  resolution: "@bcoe/v8-coverage@npm:0.2.3"
  checksum: 850f9305536d0f2bd13e9e0881cb5f02e4f93fad1189f7b2d4bebf694e3206924eadee1068130d43c11b750efcc9405f88a8e42ef098b6d75239c0f047de1a27
  languageName: node
  linkType: hard

"@ethereumjs/common@npm:^3.2.0":
  version: 3.2.0
  resolution: "@ethereumjs/common@npm:3.2.0"
  dependencies:
    "@ethereumjs/util": ^8.1.0
    crc-32: ^1.2.0
  checksum: cb9cc11f5c868cb577ba611cebf55046e509218bbb89b47ccce010776dafe8256d70f8f43fab238aec74cf71f62601cd5842bc03a83261200802de365732a14b
  languageName: node
  linkType: hard

"@ethereumjs/rlp@npm:^4.0.1":
  version: 4.0.1
  resolution: "@ethereumjs/rlp@npm:4.0.1"
  bin:
    rlp: bin/rlp
  checksum: 30db19c78faa2b6ff27275ab767646929207bb207f903f09eb3e4c273ce2738b45f3c82169ddacd67468b4f063d8d96035f2bf36f02b6b7e4d928eefe2e3ecbc
  languageName: node
  linkType: hard

"@ethereumjs/tx@npm:^4.2.0":
  version: 4.2.0
  resolution: "@ethereumjs/tx@npm:4.2.0"
  dependencies:
    "@ethereumjs/common": ^3.2.0
    "@ethereumjs/rlp": ^4.0.1
    "@ethereumjs/util": ^8.1.0
    ethereum-cryptography: ^2.0.0
  checksum: 87a3f5f2452cfbf6712f8847525a80c213210ed453c211c793c5df801fe35ecef28bae17fadd222fcbdd94277478a47e52d2b916a90a6b30cda21f1e0cdaee42
  languageName: node
  linkType: hard

"@ethereumjs/util@npm:^8.1.0":
  version: 8.1.0
  resolution: "@ethereumjs/util@npm:8.1.0"
  dependencies:
    "@ethereumjs/rlp": ^4.0.1
    ethereum-cryptography: ^2.0.0
    micro-ftch: ^0.3.1
  checksum: 9ae5dee8f12b0faf81cd83f06a41560e79b0ba96a48262771d897a510ecae605eb6d84f687da001ab8ccffd50f612ae50f988ef76e6312c752897f462f3ac08d
  languageName: node
  linkType: hard

"@gar/promisify@npm:^1.1.3":
  version: 1.1.3
  resolution: "@gar/promisify@npm:1.1.3"
  checksum: 4059f790e2d07bf3c3ff3e0fec0daa8144fe35c1f6e0111c9921bd32106adaa97a4ab096ad7dab1e28ee6a9060083c4d1a4ada42a7f5f3f7a96b8812e2b757c1
  languageName: node
  linkType: hard

"@inquirer/confirm@npm:^0.0.14-alpha.0":
  version: 0.0.14-alpha.0
  resolution: "@inquirer/confirm@npm:0.0.14-alpha.0"
  dependencies:
    "@inquirer/core": ^0.0.15-alpha.0
    "@inquirer/input": ^0.0.15-alpha.0
    chalk: ^4.1.1
  checksum: 84daf47030318e0adf6eeef85388e341f6f68c0c06c0d1d329cb6185f6d21abf74c3124102bf62f4f42145c8dc9193d719209e3759987a1bdbcfb88139b9936c
  languageName: node
  linkType: hard

"@inquirer/core@npm:^0.0.15-alpha.0":
  version: 0.0.15-alpha.0
  resolution: "@inquirer/core@npm:0.0.15-alpha.0"
  dependencies:
    ansi-escapes: ^4.2.1
    chalk: ^4.1.1
    cli-spinners: ^2.6.0
    cli-width: ^3.0.0
    lodash: ^4.17.21
    mute-stream: ^0.0.8
    run-async: ^2.3.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: 26a94a8db80e57f926c889b5730c6c9a0f18d6bf1e6dbfb68eaa6bcda33550db66118ce6afd9e91130ea2e4da42b6d7236b94c16fbab931ed04b4f442b9a5c78
  languageName: node
  linkType: hard

"@inquirer/input@npm:^0.0.15-alpha.0":
  version: 0.0.15-alpha.0
  resolution: "@inquirer/input@npm:0.0.15-alpha.0"
  dependencies:
    "@inquirer/core": ^0.0.15-alpha.0
    chalk: ^4.1.1
  checksum: c294b2fa100e2955e271b1b0590b5f360c65c560d653f39554cd381f145fdf13be9ea2c3c069d39a71f84fc3c8ba20d9d78a0b210f8cb5533d138567029e28ee
  languageName: node
  linkType: hard

"@istanbuljs/load-nyc-config@npm:^1.0.0":
  version: 1.1.0
  resolution: "@istanbuljs/load-nyc-config@npm:1.1.0"
  dependencies:
    camelcase: ^5.3.1
    find-up: ^4.1.0
    get-package-type: ^0.1.0
    js-yaml: ^3.13.1
    resolve-from: ^5.0.0
  checksum: d578da5e2e804d5c93228450a1380e1a3c691de4953acc162f387b717258512a3e07b83510a936d9fab03eac90817473917e24f5d16297af3867f59328d58568
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 5282759d961d61350f33d9118d16bcaed914ebf8061a52f4fa474b2cb08720c9c81d165e13b82f2e5a8a212cc5af482f0c6fc1ac27b9e067e5394c9a6ed186c9
  languageName: node
  linkType: hard

"@jest/console@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/console@npm:29.4.3"
  dependencies:
    "@jest/types": ^29.4.3
    "@types/node": "*"
    chalk: ^4.0.0
    jest-message-util: ^29.4.3
    jest-util: ^29.4.3
    slash: ^3.0.0
  checksum: 8d9b163febe735153b523db527742309f4d598eda22f17f04e030060329bd3da4de7420fc1f7812f7a16f08273654a7de094c4b4e8b81a99dbfc17cfb1629008
  languageName: node
  linkType: hard

"@jest/core@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/core@npm:29.4.3"
  dependencies:
    "@jest/console": ^29.4.3
    "@jest/reporters": ^29.4.3
    "@jest/test-result": ^29.4.3
    "@jest/transform": ^29.4.3
    "@jest/types": ^29.4.3
    "@types/node": "*"
    ansi-escapes: ^4.2.1
    chalk: ^4.0.0
    ci-info: ^3.2.0
    exit: ^0.1.2
    graceful-fs: ^4.2.9
    jest-changed-files: ^29.4.3
    jest-config: ^29.4.3
    jest-haste-map: ^29.4.3
    jest-message-util: ^29.4.3
    jest-regex-util: ^29.4.3
    jest-resolve: ^29.4.3
    jest-resolve-dependencies: ^29.4.3
    jest-runner: ^29.4.3
    jest-runtime: ^29.4.3
    jest-snapshot: ^29.4.3
    jest-util: ^29.4.3
    jest-validate: ^29.4.3
    jest-watcher: ^29.4.3
    micromatch: ^4.0.4
    pretty-format: ^29.4.3
    slash: ^3.0.0
    strip-ansi: ^6.0.0
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 4aa10644d66f44f051d5dd9cdcedce27acc71216dbcc5e7adebdea458e27aefe27c78f457d7efd49f58b968c35f42de5a521590876e2013593e675120b9e6ab1
  languageName: node
  linkType: hard

"@jest/environment@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/environment@npm:29.4.3"
  dependencies:
    "@jest/fake-timers": ^29.4.3
    "@jest/types": ^29.4.3
    "@types/node": "*"
    jest-mock: ^29.4.3
  checksum: 7c1b0cc4e84b90f8a3bbeca9bbf088882c88aee70a81b3b8e24265dcb1cbc302cd1eee3319089cf65bfd39adbaea344903c712afea106cb8da6c86088d99c5fb
  languageName: node
  linkType: hard

"@jest/expect-utils@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/expect-utils@npm:29.4.3"
  dependencies:
    jest-get-type: ^29.4.3
  checksum: 2bbed39ff2fb59f5acac465a1ce7303e3b4b62b479e4f386261986c9827f7f799ea912761e22629c5daf10addf8513f16733c14a29c2647bb66d4ee625e9ff92
  languageName: node
  linkType: hard

"@jest/expect@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/expect@npm:29.4.3"
  dependencies:
    expect: ^29.4.3
    jest-snapshot: ^29.4.3
  checksum: 08d0d40077ec99a7491fe59d05821dbd31126cfba70875855d8a063698b7126b5f6c309c50811caacc6ae2f727c6e44f51bdcf1d6c1ea832b4f020045ef22d45
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/fake-timers@npm:29.4.3"
  dependencies:
    "@jest/types": ^29.4.3
    "@sinonjs/fake-timers": ^10.0.2
    "@types/node": "*"
    jest-message-util: ^29.4.3
    jest-mock: ^29.4.3
    jest-util: ^29.4.3
  checksum: adaceb9143c395cccf3d7baa0e49b7042c3092a554e8283146df19926247e34c21b5bde5688bb90e9e87b4a02e4587926c5d858ee0a38d397a63175d0a127874
  languageName: node
  linkType: hard

"@jest/globals@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/globals@npm:29.4.3"
  dependencies:
    "@jest/environment": ^29.4.3
    "@jest/expect": ^29.4.3
    "@jest/types": ^29.4.3
    jest-mock: ^29.4.3
  checksum: ea76b546ceb4aa5ce2bb3726df12f989b23150b51c9f7664790caa81b943012a657cf3a8525498af1c3518cdb387f54b816cfba1b0ddd22c7b20f03b1d7290b4
  languageName: node
  linkType: hard

"@jest/reporters@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/reporters@npm:29.4.3"
  dependencies:
    "@bcoe/v8-coverage": ^0.2.3
    "@jest/console": ^29.4.3
    "@jest/test-result": ^29.4.3
    "@jest/transform": ^29.4.3
    "@jest/types": ^29.4.3
    "@jridgewell/trace-mapping": ^0.3.15
    "@types/node": "*"
    chalk: ^4.0.0
    collect-v8-coverage: ^1.0.0
    exit: ^0.1.2
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    istanbul-lib-coverage: ^3.0.0
    istanbul-lib-instrument: ^5.1.0
    istanbul-lib-report: ^3.0.0
    istanbul-lib-source-maps: ^4.0.0
    istanbul-reports: ^3.1.3
    jest-message-util: ^29.4.3
    jest-util: ^29.4.3
    jest-worker: ^29.4.3
    slash: ^3.0.0
    string-length: ^4.0.1
    strip-ansi: ^6.0.0
    v8-to-istanbul: ^9.0.1
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 7aa2e429c915bd96c3334962addd69d2bbf52065725757ddde26b293f8c4420a1e8c65363cc3e1e5ec89100a5273ccd3771bec58325a2cc0d97afdc81995073a
  languageName: node
  linkType: hard

"@jest/schemas@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/schemas@npm:29.4.3"
  dependencies:
    "@sinclair/typebox": ^0.25.16
  checksum: ac754e245c19dc39e10ebd41dce09040214c96a4cd8efa143b82148e383e45128f24599195ab4f01433adae4ccfbe2db6574c90db2862ccd8551a86704b5bebd
  languageName: node
  linkType: hard

"@jest/source-map@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/source-map@npm:29.4.3"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.15
    callsites: ^3.0.0
    graceful-fs: ^4.2.9
  checksum: 2301d225145f8123540c0be073f35a80fd26a2f5e59550fd68525d8cea580fb896d12bf65106591ffb7366a8a19790076dbebc70e0f5e6ceb51f81827ed1f89c
  languageName: node
  linkType: hard

"@jest/test-result@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/test-result@npm:29.4.3"
  dependencies:
    "@jest/console": ^29.4.3
    "@jest/types": ^29.4.3
    "@types/istanbul-lib-coverage": ^2.0.0
    collect-v8-coverage: ^1.0.0
  checksum: 164f102b96619ec283c2c39e208b8048e4674f75bf3c3a4f2e95048ae0f9226105add684b25f10d286d91c221625f877e2c1cfc3da46c42d7e1804da239318cb
  languageName: node
  linkType: hard

"@jest/test-sequencer@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/test-sequencer@npm:29.4.3"
  dependencies:
    "@jest/test-result": ^29.4.3
    graceful-fs: ^4.2.9
    jest-haste-map: ^29.4.3
    slash: ^3.0.0
  checksum: 145e1fa9379e5be3587bde6d585b8aee5cf4442b06926928a87e9aec7de5be91b581711d627c6ca13144d244fe05e5d248c13b366b51bedc404f9dcfbfd79e9e
  languageName: node
  linkType: hard

"@jest/transform@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/transform@npm:29.4.3"
  dependencies:
    "@babel/core": ^7.11.6
    "@jest/types": ^29.4.3
    "@jridgewell/trace-mapping": ^0.3.15
    babel-plugin-istanbul: ^6.1.1
    chalk: ^4.0.0
    convert-source-map: ^2.0.0
    fast-json-stable-stringify: ^2.1.0
    graceful-fs: ^4.2.9
    jest-haste-map: ^29.4.3
    jest-regex-util: ^29.4.3
    jest-util: ^29.4.3
    micromatch: ^4.0.4
    pirates: ^4.0.4
    slash: ^3.0.0
    write-file-atomic: ^4.0.2
  checksum: 082d74e04044213aa7baa8de29f8383e5010034f867969c8602a2447a4ef2f484cfaf2491eba3179ce42f369f7a0af419cbd087910f7e5caf7aa5d1fe03f2ff9
  languageName: node
  linkType: hard

"@jest/types@npm:^29.4.3":
  version: 29.4.3
  resolution: "@jest/types@npm:29.4.3"
  dependencies:
    "@jest/schemas": ^29.4.3
    "@types/istanbul-lib-coverage": ^2.0.0
    "@types/istanbul-reports": ^3.0.0
    "@types/node": "*"
    "@types/yargs": ^17.0.8
    chalk: ^4.0.0
  checksum: 1756f4149d360f98567f56f434144f7af23ed49a2c42889261a314df6b6654c2de70af618fb2ee0ee39cadaf10835b885845557184509503646c9cb9dcc02bac
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.1.0":
  version: 0.1.1
  resolution: "@jridgewell/gen-mapping@npm:0.1.1"
  dependencies:
    "@jridgewell/set-array": ^1.0.0
    "@jridgewell/sourcemap-codec": ^1.4.10
  checksum: 3bcc21fe786de6ffbf35c399a174faab05eb23ce6a03e8769569de28abbf4facc2db36a9ddb0150545ae23a8d35a7cf7237b2aa9e9356a7c626fb4698287d5cc
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.2":
  version: 0.3.2
  resolution: "@jridgewell/gen-mapping@npm:0.3.2"
  dependencies:
    "@jridgewell/set-array": ^1.0.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: 1832707a1c476afebe4d0fbbd4b9434fdb51a4c3e009ab1e9938648e21b7a97049fa6009393bdf05cab7504108413441df26d8a3c12193996e65493a4efb6882
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.1
  resolution: "@jridgewell/resolve-uri@npm:3.1.1"
  checksum: f5b441fe7900eab4f9155b3b93f9800a916257f4e8563afbcd3b5a5337b55e52bd8ae6735453b1b745457d9f6cdb16d74cd6220bbdd98cf153239e13f6cbb653
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.0.0, @jridgewell/set-array@npm:^1.0.1":
  version: 1.1.2
  resolution: "@jridgewell/set-array@npm:1.1.2"
  checksum: 69a84d5980385f396ff60a175f7177af0b8da4ddb81824cb7016a9ef914eee9806c72b6b65942003c63f7983d4f39a5c6c27185bbca88eb4690b62075602e28e
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.4.15
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.15"
  checksum: b881c7e503db3fc7f3c1f35a1dd2655a188cc51a3612d76efc8a6eb74728bef5606e6758ee77423e564092b4a518aba569bbb21c9bac5ab7a35b0c6ae7e344c8
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.12, @jridgewell/trace-mapping@npm:^0.3.15, @jridgewell/trace-mapping@npm:^0.3.17, @jridgewell/trace-mapping@npm:^0.3.9":
  version: 0.3.20
  resolution: "@jridgewell/trace-mapping@npm:0.3.20"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: cd1a7353135f385909468ff0cf20bdd37e59f2ee49a13a966dedf921943e222082c583ade2b579ff6cd0d8faafcb5461f253e1bf2a9f48fec439211fdbe788f5
  languageName: node
  linkType: hard

"@metamask/browser-passworder@npm:^4.3.0":
  version: 4.3.0
  resolution: "@metamask/browser-passworder@npm:4.3.0"
  dependencies:
    "@metamask/utils": ^8.2.0
  checksum: 7992553a0cd91902aa316a931d36c2628cb5a73fcbc28a31dce4177704036d739214c580ed833079003b2c7dfd60c5648a20734badf91e2c665cfe2f56012a8c
  languageName: node
  linkType: hard

"@metamask/utils@npm:^8.2.0":
  version: 8.2.1
  resolution: "@metamask/utils@npm:8.2.1"
  dependencies:
    "@ethereumjs/tx": ^4.2.0
    "@noble/hashes": ^1.3.1
    "@scure/base": ^1.1.3
    "@types/debug": ^4.1.7
    debug: ^4.3.4
    pony-cause: ^2.1.10
    semver: ^7.5.4
    superstruct: ^1.0.3
  checksum: 36a714a17e4949d2040bedb28d4373a22e7e86bb797aa2d59223f9799fd76e662443bcede113719c4e200f5e9d90a9d62feafad5028fff8b9a7a85fface097ca
  languageName: node
  linkType: hard

"@metamask/vault-decryptor@workspace:.":
  version: 0.0.0-use.local
  resolution: "@metamask/vault-decryptor@workspace:."
  dependencies:
    "@babel/preset-env": ^7.20.2
    "@metamask/browser-passworder": ^4.3.0
    babelify: ^10.0.0
    brfs: ^1.4.3
    browserify: ^17.0.0
    ethereumjs-util: ^7.1.4
    jest: ^29.4.1
    jest-it-up: ^2.1.0
    react: ^18.0.0
    react-dom: ^18.0.0
    react-hyperscript: ^3.2.0
    react-redux: ^8.0.1
    redux: ^4.2.0
    redux-logger: ^3.0.6
    redux-thunk: ^2.4.1
    xtend: ^4.0.2
  languageName: unknown
  linkType: soft

"@noble/curves@npm:1.1.0, @noble/curves@npm:~1.1.0":
  version: 1.1.0
  resolution: "@noble/curves@npm:1.1.0"
  dependencies:
    "@noble/hashes": 1.3.1
  checksum: 2658cdd3f84f71079b4e3516c47559d22cf4b55c23ac8ee9d2b1f8e5b72916d9689e59820e0f9d9cb4a46a8423af5b56dc6bb7782405c88be06a015180508db5
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.3.1":
  version: 1.3.1
  resolution: "@noble/hashes@npm:1.3.1"
  checksum: 7fdefc0f7a0c1ec27acc6ff88841793e3f93ec4ce6b8a6a12bfc0dd70ae6b7c4c82fe305fdfeda1735d5ad4a9eebe761e6693b3d355689c559e91242f4bc95b1
  languageName: node
  linkType: hard

"@noble/hashes@npm:^1.3.1, @noble/hashes@npm:~1.3.0, @noble/hashes@npm:~1.3.1":
  version: 1.3.2
  resolution: "@noble/hashes@npm:1.3.2"
  checksum: fe23536b436539d13f90e4b9be843cc63b1b17666a07634a2b1259dded6f490be3d050249e6af98076ea8f2ea0d56f578773c2197f2aa0eeaa5fba5bc18ba474
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^2.1.0":
  version: 2.1.2
  resolution: "@npmcli/fs@npm:2.1.2"
  dependencies:
    "@gar/promisify": ^1.1.3
    semver: ^7.3.5
  checksum: 405074965e72d4c9d728931b64d2d38e6ea12066d4fad651ac253d175e413c06fe4350970c783db0d749181da8fe49c42d3880bd1cbc12cd68e3a7964d820225
  languageName: node
  linkType: hard

"@npmcli/move-file@npm:^2.0.0":
  version: 2.0.1
  resolution: "@npmcli/move-file@npm:2.0.1"
  dependencies:
    mkdirp: ^1.0.4
    rimraf: ^3.0.2
  checksum: 52dc02259d98da517fae4cb3a0a3850227bdae4939dda1980b788a7670636ca2b4a01b58df03dd5f65c1e3cb70c50fa8ce5762b582b3f499ec30ee5ce1fd9380
  languageName: node
  linkType: hard

"@scure/base@npm:^1.1.3, @scure/base@npm:~1.1.0":
  version: 1.1.3
  resolution: "@scure/base@npm:1.1.3"
  checksum: 1606ab8a4db898cb3a1ada16c15437c3bce4e25854fadc8eb03ae93cbbbac1ed90655af4b0be3da37e12056fef11c0374499f69b9e658c9e5b7b3e06353c630c
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.3.1":
  version: 1.3.1
  resolution: "@scure/bip32@npm:1.3.1"
  dependencies:
    "@noble/curves": ~1.1.0
    "@noble/hashes": ~1.3.1
    "@scure/base": ~1.1.0
  checksum: 394d65f77a40651eba21a5096da0f4233c3b50d422864751d373fcf142eeedb94a1149f9ab1dbb078086dab2d0bc27e2b1afec8321bf22d4403c7df2fea5bfe2
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.2.1":
  version: 1.2.1
  resolution: "@scure/bip39@npm:1.2.1"
  dependencies:
    "@noble/hashes": ~1.3.0
    "@scure/base": ~1.1.0
  checksum: c5bd6f1328fdbeae2dcdd891825b1610225310e5e62a4942714db51066866e4f7bef242c7b06a1b9dcc8043a4a13412cf5c5df76d3b10aa9e36b82e9b6e3eeaa
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.25.16":
  version: 0.25.22
  resolution: "@sinclair/typebox@npm:0.25.22"
  checksum: 82bea74649dd9e4ac81e14815d7e04fc70847065666c3af2023e478a40097a5345fbbaf526f9df4cdafad5f014b3c6060ce0777a7fd2e086a33b972b3322eb88
  languageName: node
  linkType: hard

"@sinonjs/commons@npm:^2.0.0":
  version: 2.0.0
  resolution: "@sinonjs/commons@npm:2.0.0"
  dependencies:
    type-detect: 4.0.8
  checksum: 5023ba17edf2b85ed58262313b8e9b59e23c6860681a9af0200f239fe939e2b79736d04a260e8270ddd57196851dde3ba754d7230be5c5234e777ae2ca8af137
  languageName: node
  linkType: hard

"@sinonjs/fake-timers@npm:^10.0.2":
  version: 10.0.2
  resolution: "@sinonjs/fake-timers@npm:10.0.2"
  dependencies:
    "@sinonjs/commons": ^2.0.0
  checksum: c62aa98e7cefda8dedc101ce227abc888dc46b8ff9706c5f0a8dfd9c3ada97d0a5611384738d9ba0b26b59f99c2ba24efece8e779bb08329e9e87358fa309824
  languageName: node
  linkType: hard

"@tootallnate/once@npm:2":
  version: 2.0.0
  resolution: "@tootallnate/once@npm:2.0.0"
  checksum: ad87447820dd3f24825d2d947ebc03072b20a42bfc96cbafec16bff8bbda6c1a81fcb0be56d5b21968560c5359a0af4038a68ba150c3e1694fe4c109a063bed8
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.1.14":
  version: 7.20.0
  resolution: "@types/babel__core@npm:7.20.0"
  dependencies:
    "@babel/parser": ^7.20.7
    "@babel/types": ^7.20.7
    "@types/babel__generator": "*"
    "@types/babel__template": "*"
    "@types/babel__traverse": "*"
  checksum: 49b601a0a7637f1f387442c8156bd086cfd10ff4b82b0e1994e73a6396643b5435366fb33d6b604eade8467cca594ef97adcbc412aede90bb112ebe88d0ad6df
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.6.4
  resolution: "@types/babel__generator@npm:7.6.4"
  dependencies:
    "@babel/types": ^7.0.0
  checksum: 20effbbb5f8a3a0211e95959d06ae70c097fb6191011b73b38fe86deebefad8e09ee014605e0fd3cdaedc73d158be555866810e9166e1f09e4cfd880b874dcb0
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.1
  resolution: "@types/babel__template@npm:7.4.1"
  dependencies:
    "@babel/parser": ^7.1.0
    "@babel/types": ^7.0.0
  checksum: 649fe8b42c2876be1fd28c6ed9b276f78152d5904ec290b6c861d9ef324206e0a5c242e8305c421ac52ecf6358fa7e32ab7a692f55370484825c1df29b1596ee
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*, @types/babel__traverse@npm:^7.0.6":
  version: 7.18.3
  resolution: "@types/babel__traverse@npm:7.18.3"
  dependencies:
    "@babel/types": ^7.3.0
  checksum: d20953338b2f012ab7750932ece0a78e7d1645b0a6ff42d49be90f55e9998085da1374a9786a7da252df89555c6586695ba4d1d4b4e88ab2b9f306bcd35e00d3
  languageName: node
  linkType: hard

"@types/bn.js@npm:^5.1.0":
  version: 5.1.1
  resolution: "@types/bn.js@npm:5.1.1"
  dependencies:
    "@types/node": "*"
  checksum: e50ed2dd3abe997e047caf90e0352c71e54fc388679735217978b4ceb7e336e51477791b715f49fd77195ac26dd296c7bad08a3be9750e235f9b2e1edb1b51c2
  languageName: node
  linkType: hard

"@types/debug@npm:^4.1.7":
  version: 4.1.12
  resolution: "@types/debug@npm:4.1.12"
  dependencies:
    "@types/ms": "*"
  checksum: 47876a852de8240bfdaf7481357af2b88cb660d30c72e73789abf00c499d6bc7cd5e52f41c915d1b9cd8ec9fef5b05688d7b7aef17f7f272c2d04679508d1053
  languageName: node
  linkType: hard

"@types/graceful-fs@npm:^4.1.3":
  version: 4.1.6
  resolution: "@types/graceful-fs@npm:4.1.6"
  dependencies:
    "@types/node": "*"
  checksum: c3070ccdc9ca0f40df747bced1c96c71a61992d6f7c767e8fd24bb6a3c2de26e8b84135ede000b7e79db530a23e7e88dcd9db60eee6395d0f4ce1dae91369dd4
  languageName: node
  linkType: hard

"@types/hoist-non-react-statics@npm:^3.3.1":
  version: 3.3.1
  resolution: "@types/hoist-non-react-statics@npm:3.3.1"
  dependencies:
    "@types/react": "*"
    hoist-non-react-statics: ^3.3.0
  checksum: 2c0778570d9a01d05afabc781b32163f28409bb98f7245c38d5eaf082416fdb73034003f5825eb5e21313044e8d2d9e1f3fe2831e345d3d1b1d20bcd12270719
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0, @types/istanbul-lib-coverage@npm:^2.0.1":
  version: 2.0.4
  resolution: "@types/istanbul-lib-coverage@npm:2.0.4"
  checksum: a25d7589ee65c94d31464c16b72a9dc81dfa0bea9d3e105ae03882d616e2a0712a9c101a599ec482d297c3591e16336962878cb3eb1a0a62d5b76d277a890ce7
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.0
  resolution: "@types/istanbul-lib-report@npm:3.0.0"
  dependencies:
    "@types/istanbul-lib-coverage": "*"
  checksum: 656398b62dc288e1b5226f8880af98087233cdb90100655c989a09f3052b5775bf98ba58a16c5ae642fb66c61aba402e07a9f2bff1d1569e3b306026c59f3f36
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^3.0.0":
  version: 3.0.1
  resolution: "@types/istanbul-reports@npm:3.0.1"
  dependencies:
    "@types/istanbul-lib-report": "*"
  checksum: f1ad54bc68f37f60b30c7915886b92f86b847033e597f9b34f2415acdbe5ed742fa559a0a40050d74cdba3b6a63c342cac1f3a64dba5b68b66a6941f4abd7903
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 0.7.34
  resolution: "@types/ms@npm:0.7.34"
  checksum: f38d36e7b6edecd9badc9cf50474159e9da5fa6965a75186cceaf883278611b9df6669dc3a3cc122b7938d317b68a9e3d573d316fcb35d1be47ec9e468c6bd8a
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 18.11.19
  resolution: "@types/node@npm:18.11.19"
  checksum: d7cd19fcfc59cbdd3f9ba0b4072cb7adc21bd575bd8eb7d7e698975e63564aaa83f03434f32b12331f84f73d0b369d9cbe2371e359d9d7f5c3361f4987f4f7da
  languageName: node
  linkType: hard

"@types/pbkdf2@npm:^3.0.0":
  version: 3.1.0
  resolution: "@types/pbkdf2@npm:3.1.0"
  dependencies:
    "@types/node": "*"
  checksum: d15024b1957c21cf3b8887329d9bd8dfde754cf13a09d76ae25f1391cfc62bb8b8d7b760773c5dbaa748172fba8b3e0c3dbe962af6ccbd69b76df12a48dfba40
  languageName: node
  linkType: hard

"@types/prettier@npm:^2.1.5":
  version: 2.7.2
  resolution: "@types/prettier@npm:2.7.2"
  checksum: b47d76a5252265f8d25dd2fe2a5a61dc43ba0e6a96ffdd00c594cb4fd74c1982c2e346497e3472805d97915407a09423804cc2110a0b8e1b22cffcab246479b7
  languageName: node
  linkType: hard

"@types/prop-types@npm:*":
  version: 15.7.5
  resolution: "@types/prop-types@npm:15.7.5"
  checksum: 5b43b8b15415e1f298243165f1d44390403bb2bd42e662bca3b5b5633fdd39c938e91b7fce3a9483699db0f7a715d08cef220c121f723a634972fdf596aec980
  languageName: node
  linkType: hard

"@types/react@npm:*":
  version: 18.0.27
  resolution: "@types/react@npm:18.0.27"
  dependencies:
    "@types/prop-types": "*"
    "@types/scheduler": "*"
    csstype: ^3.0.2
  checksum: 600fdbc39a92ea4a77047db3e12f05f67776a710f5918248c0189a59ac2a38900c9db5a5d2e433a16df528a3ecab1aa114b322cacea573bb1ca2fc0b094c52d1
  languageName: node
  linkType: hard

"@types/scheduler@npm:*":
  version: 0.16.2
  resolution: "@types/scheduler@npm:0.16.2"
  checksum: b6b4dcfeae6deba2e06a70941860fb1435730576d3689225a421280b7742318d1548b3d22c1f66ab68e414f346a9542f29240bc955b6332c5b11e561077583bc
  languageName: node
  linkType: hard

"@types/secp256k1@npm:^4.0.1":
  version: 4.0.3
  resolution: "@types/secp256k1@npm:4.0.3"
  dependencies:
    "@types/node": "*"
  checksum: 1bd10b9afa724084b655dc81b7b315def3d2d0e272014ef16009fa76e17537411c07c0695fdea412bc7b36d2a02687f5fea33522d55b8ef29eda42992f812913
  languageName: node
  linkType: hard

"@types/stack-utils@npm:^2.0.0":
  version: 2.0.1
  resolution: "@types/stack-utils@npm:2.0.1"
  checksum: 205fdbe3326b7046d7eaf5e494d8084f2659086a266f3f9cf00bccc549c8e36e407f88168ad4383c8b07099957ad669f75f2532ed4bc70be2b037330f7bae019
  languageName: node
  linkType: hard

"@types/use-sync-external-store@npm:^0.0.3":
  version: 0.0.3
  resolution: "@types/use-sync-external-store@npm:0.0.3"
  checksum: 161ddb8eec5dbe7279ac971531217e9af6b99f7783213566d2b502e2e2378ea19cf5e5ea4595039d730aa79d3d35c6567d48599f69773a02ffcff1776ec2a44e
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.0
  resolution: "@types/yargs-parser@npm:21.0.0"
  checksum: b2f4c8d12ac18a567440379909127cf2cec393daffb73f246d0a25df36ea983b93b7e9e824251f959e9f928cbc7c1aab6728d0a0ff15d6145f66cec2be67d9a2
  languageName: node
  linkType: hard

"@types/yargs@npm:^17.0.8":
  version: 17.0.22
  resolution: "@types/yargs@npm:17.0.22"
  dependencies:
    "@types/yargs-parser": "*"
  checksum: 0773523fda71bafdc52f13f5970039e535a353665a60ba9261149a5c9c2b908242e6e77fbb7a8c06931ec78ce889d64d09673c68ba23eb5f5742d5385d0d1982
  languageName: node
  linkType: hard

"JSONStream@npm:^1.0.3":
  version: 1.3.5
  resolution: "JSONStream@npm:1.3.5"
  dependencies:
    jsonparse: ^1.2.0
    through: ">=2.2.7 <3"
  bin:
    JSONStream: ./bin.js
  checksum: 2605fa124260c61bad38bb65eba30d2f72216a78e94d0ab19b11b4e0327d572b8d530c0c9cc3b0764f727ad26d39e00bf7ebad57781ca6368394d73169c59e46
  languageName: node
  linkType: hard

"abbrev@npm:^1.0.0":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: a4a97ec07d7ea112c517036882b2ac22f3109b7b19077dc656316d07d308438aac28e4d9746dc4d84bf6b1e75b4a7b0a5f3cb30592419f128ca9a8cee3bcfa17
  languageName: node
  linkType: hard

"acorn-node@npm:^1.2.0, acorn-node@npm:^1.3.0, acorn-node@npm:^1.5.2, acorn-node@npm:^1.8.2":
  version: 1.8.2
  resolution: "acorn-node@npm:1.8.2"
  dependencies:
    acorn: ^7.0.0
    acorn-walk: ^7.0.0
    xtend: ^4.0.2
  checksum: 02e1564a1ccf8bd1fcefcd01235398af4a9effaf032c5397994ddd275590a72894cb3e26e4b82579ccdda1e48ade7486aef61e771ddae3563ca452b927f443d8
  languageName: node
  linkType: hard

"acorn-walk@npm:^7.0.0":
  version: 7.2.0
  resolution: "acorn-walk@npm:7.2.0"
  checksum: 9252158a79b9d92f1bc0dd6acc0fcfb87a67339e84bcc301bb33d6078936d27e35d606b4d35626d2962cd43c256d6f27717e70cbe15c04fff999ab0b2260b21f
  languageName: node
  linkType: hard

"acorn@npm:^7.0.0, acorn@npm:^7.1.1":
  version: 7.4.1
  resolution: "acorn@npm:7.4.1"
  bin:
    acorn: bin/acorn
  checksum: 1860f23c2107c910c6177b7b7be71be350db9e1080d814493fae143ae37605189504152d1ba8743ba3178d0b37269ce1ffc42b101547fdc1827078f82671e407
  languageName: node
  linkType: hard

"agent-base@npm:6, agent-base@npm:^6.0.2":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: 4
  checksum: f52b6872cc96fd5f622071b71ef200e01c7c4c454ee68bc9accca90c98cfb39f2810e3e9aa330435835eedc8c23f4f8a15267f67c6e245d2b33757575bdac49d
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.2.1":
  version: 4.2.1
  resolution: "agentkeepalive@npm:4.2.1"
  dependencies:
    debug: ^4.1.0
    depd: ^1.1.2
    humanize-ms: ^1.2.1
  checksum: 39cb49ed8cf217fd6da058a92828a0a84e0b74c35550f82ee0a10e1ee403c4b78ade7948be2279b188b7a7303f5d396ea2738b134731e464bf28de00a4f72a18
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ansi-colors@npm:^4.1.0":
  version: 4.1.3
  resolution: "ansi-colors@npm:4.1.3"
  checksum: a9c2ec842038a1fabc7db9ece7d3177e2fe1c5dc6f0c51ecfbf5f39911427b89c00b5dc6b8bd95f82a26e9b16aaae2e83d45f060e98070ce4d1333038edceb0e
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.2.1":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: ^0.21.3
  checksum: 93111c42189c0a6bed9cdb4d7f2829548e943827ee8479c74d6e0b22ee127b2a21d3f8b5ca57723b8ef78ce011fbfc2784350eb2bde3ccfccf2f575fa8489815
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: d7f4e97ce0623aea6bc0d90dcd28881ee04cba06c570b97fd3391bd7a268eedfd9d5e2dd4fdcbdd82b8105df5faf6f24aaedc08eaf3da898e702db5948f63469
  languageName: node
  linkType: hard

"anymatch@npm:^3.0.3":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: 5615cadcfb45289eea63f8afd064ab656006361020e1735112e346593856f87435e02d8dcc7ff0d11928bc7d425f27bc7c2a84f6c0b35ab0ff659c814c138a24
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^3.0.0":
  version: 3.0.1
  resolution: "are-we-there-yet@npm:3.0.1"
  dependencies:
    delegates: ^1.0.0
    readable-stream: ^3.6.0
  checksum: 52590c24860fa7173bedeb69a4c05fb573473e860197f618b9a28432ee4379049336727ae3a1f9c4cb083114601c1140cee578376164d0e651217a9843f9fe83
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: ~1.0.2
  checksum: 7ca6e45583a28de7258e39e13d81e925cfa25d7d4aacbf806a382d3c02fcb13403a07fb8aeef949f10a7cfe4a62da0e2e807b348a5980554cc28ee573ef95945
  languageName: node
  linkType: hard

"asn1.js@npm:^5.2.0":
  version: 5.4.1
  resolution: "asn1.js@npm:5.4.1"
  dependencies:
    bn.js: ^4.0.0
    inherits: ^2.0.1
    minimalistic-assert: ^1.0.0
    safer-buffer: ^2.1.0
  checksum: 3786a101ac6f304bd4e9a7df79549a7561950a13d4bcaec0c7790d44c80d147c1a94ba3d4e663673406064642a40b23fcd6c82a9952468e386c1a1376d747f9a
  languageName: node
  linkType: hard

"assert@npm:^1.4.0":
  version: 1.5.0
  resolution: "assert@npm:1.5.0"
  dependencies:
    object-assign: ^4.1.1
    util: 0.10.3
  checksum: 9be48435f726029ae7020c5888a3566bf4d617687aab280827f2e4029644b6515a9519ea10d018b342147c02faf73d9e9419e780e8937b3786ee4945a0ca71e5
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.5":
  version: 1.0.5
  resolution: "available-typed-arrays@npm:1.0.5"
  checksum: 20eb47b3cefd7db027b9bbb993c658abd36d4edd3fe1060e83699a03ee275b0c9b216cc076ff3f2db29073225fb70e7613987af14269ac1fe2a19803ccc97f1a
  languageName: node
  linkType: hard

"babel-jest@npm:^29.4.3":
  version: 29.4.3
  resolution: "babel-jest@npm:29.4.3"
  dependencies:
    "@jest/transform": ^29.4.3
    "@types/babel__core": ^7.1.14
    babel-plugin-istanbul: ^6.1.1
    babel-preset-jest: ^29.4.3
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    slash: ^3.0.0
  peerDependencies:
    "@babel/core": ^7.8.0
  checksum: a1a95937adb5e717dbffc2eb9e583fa6d26c7e5d5b07bb492a2d7f68631510a363e9ff097eafb642ad642dfac9dc2b13872b584f680e166a4f0922c98ea95853
  languageName: node
  linkType: hard

"babel-plugin-istanbul@npm:^6.1.1":
  version: 6.1.1
  resolution: "babel-plugin-istanbul@npm:6.1.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@istanbuljs/load-nyc-config": ^1.0.0
    "@istanbuljs/schema": ^0.1.2
    istanbul-lib-instrument: ^5.0.4
    test-exclude: ^6.0.0
  checksum: cb4fd95738219f232f0aece1116628cccff16db891713c4ccb501cddbbf9272951a5df81f2f2658dfdf4b3e7b236a9d5cbcf04d5d8c07dd5077297339598061a
  languageName: node
  linkType: hard

"babel-plugin-jest-hoist@npm:^29.4.3":
  version: 29.4.3
  resolution: "babel-plugin-jest-hoist@npm:29.4.3"
  dependencies:
    "@babel/template": ^7.3.3
    "@babel/types": ^7.3.3
    "@types/babel__core": ^7.1.14
    "@types/babel__traverse": ^7.0.6
  checksum: c8702a6db6b30ec39dfb9f8e72b501c13895231ed80b15ed2648448f9f0c7b7cc4b1529beac31802ae655f63479a05110ca612815aa25fb1b0e6c874e1589137
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.3.3":
  version: 0.3.3
  resolution: "babel-plugin-polyfill-corejs2@npm:0.3.3"
  dependencies:
    "@babel/compat-data": ^7.17.7
    "@babel/helper-define-polyfill-provider": ^0.3.3
    semver: ^6.1.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7db3044993f3dddb3cc3d407bc82e640964a3bfe22de05d90e1f8f7a5cb71460011ab136d3c03c6c1ba428359ebf635688cd6205e28d0469bba221985f5c6179
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.6.0":
  version: 0.6.0
  resolution: "babel-plugin-polyfill-corejs3@npm:0.6.0"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.3.3
    core-js-compat: ^3.25.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 470bb8c59f7c0912bd77fe1b5a2e72f349b3f65bbdee1d60d6eb7e1f4a085c6f24b2dd5ab4ac6c2df6444a96b070ef6790eccc9edb6a2668c60d33133bfb62c6
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.4.1":
  version: 0.4.1
  resolution: "babel-plugin-polyfill-regenerator@npm:0.4.1"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.3.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ab0355efbad17d29492503230387679dfb780b63b25408990d2e4cf421012dae61d6199ddc309f4d2409ce4e9d3002d187702700dd8f4f8770ebbba651ed066c
  languageName: node
  linkType: hard

"babel-preset-current-node-syntax@npm:^1.0.0":
  version: 1.0.1
  resolution: "babel-preset-current-node-syntax@npm:1.0.1"
  dependencies:
    "@babel/plugin-syntax-async-generators": ^7.8.4
    "@babel/plugin-syntax-bigint": ^7.8.3
    "@babel/plugin-syntax-class-properties": ^7.8.3
    "@babel/plugin-syntax-import-meta": ^7.8.3
    "@babel/plugin-syntax-json-strings": ^7.8.3
    "@babel/plugin-syntax-logical-assignment-operators": ^7.8.3
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-numeric-separator": ^7.8.3
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-syntax-top-level-await": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: d118c2742498c5492c095bc8541f4076b253e705b5f1ad9a2e7d302d81a84866f0070346662355c8e25fc02caa28dc2da8d69bcd67794a0d60c4d6fab6913cc8
  languageName: node
  linkType: hard

"babel-preset-jest@npm:^29.4.3":
  version: 29.4.3
  resolution: "babel-preset-jest@npm:29.4.3"
  dependencies:
    babel-plugin-jest-hoist: ^29.4.3
    babel-preset-current-node-syntax: ^1.0.0
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a091721861ea2f8d969ace8fe06570cff8f2e847dbc6e4800abacbe63f72131abde615ce0a3b6648472c97e55a5be7f8bf7ae381e2b194ad2fa1737096febcf5
  languageName: node
  linkType: hard

"babelify@npm:^10.0.0":
  version: 10.0.0
  resolution: "babelify@npm:10.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 2f6263bd05e6126823056aa7c73de69b2dcc5f28f7eebcfeba6098f30bb013f853aaca4b9a45c4a5fd564167ee7081a0bfc49d3a14b91d7fa2d11205b040bb84
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base-x@npm:^3.0.2":
  version: 3.0.9
  resolution: "base-x@npm:3.0.9"
  dependencies:
    safe-buffer: ^5.0.1
  checksum: 957101d6fd09e1903e846fd8f69fd7e5e3e50254383e61ab667c725866bec54e5ece5ba49ce385128ae48f9ec93a26567d1d5ebb91f4d56ef4a9cc0d5a5481e8
  languageName: node
  linkType: hard

"base64-js@npm:^1.0.2":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"blakejs@npm:^1.1.0":
  version: 1.2.1
  resolution: "blakejs@npm:1.2.1"
  checksum: d699ba116cfa21d0b01d12014a03e484dd76d483133e6dc9eb415aa70a119f08beb3bcefb8c71840106a00b542cba77383f8be60cd1f0d4589cb8afb922eefbe
  languageName: node
  linkType: hard

"bn.js@npm:^4.0.0, bn.js@npm:^4.1.0, bn.js@npm:^4.11.9":
  version: 4.12.0
  resolution: "bn.js@npm:4.12.0"
  checksum: 39afb4f15f4ea537b55eaf1446c896af28ac948fdcf47171961475724d1bb65118cca49fa6e3d67706e4790955ec0e74de584e45c8f1ef89f46c812bee5b5a12
  languageName: node
  linkType: hard

"bn.js@npm:^5.0.0, bn.js@npm:^5.1.2, bn.js@npm:^5.2.0, bn.js@npm:^5.2.1":
  version: 5.2.1
  resolution: "bn.js@npm:5.2.1"
  checksum: 3dd8c8d38055fedfa95c1d5fc3c99f8dd547b36287b37768db0abab3c239711f88ff58d18d155dd8ad902b0b0cee973747b7ae20ea12a09473272b0201c9edd3
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.3":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"brfs@npm:^1.4.3":
  version: 1.6.1
  resolution: "brfs@npm:1.6.1"
  dependencies:
    quote-stream: ^1.0.1
    resolve: ^1.1.5
    static-module: ^2.2.0
    through2: ^2.0.0
  bin:
    brfs: bin/cmd.js
  checksum: 007d9dc507bbb1d770ca2fb3a9c1b5f4710592ff160535e959ceaed47a481a08c8a3bce3ea7eecbb781e7fff9801eca6da58a968f66668e89df83d559b624f19
  languageName: node
  linkType: hard

"brorand@npm:^1.0.1, brorand@npm:^1.1.0":
  version: 1.1.0
  resolution: "brorand@npm:1.1.0"
  checksum: 8a05c9f3c4b46572dec6ef71012b1946db6cae8c7bb60ccd4b7dd5a84655db49fe043ecc6272e7ef1f69dc53d6730b9e2a3a03a8310509a3d797a618cbee52be
  languageName: node
  linkType: hard

"browser-pack@npm:^6.0.1":
  version: 6.1.0
  resolution: "browser-pack@npm:6.1.0"
  dependencies:
    JSONStream: ^1.0.3
    combine-source-map: ~0.8.0
    defined: ^1.0.0
    safe-buffer: ^5.1.1
    through2: ^2.0.0
    umd: ^3.0.0
  bin:
    browser-pack: bin/cmd.js
  checksum: 9e5993d3eefb7c56a68cfc8810e59a2920481f93bdcb0a53e07b322f273f697cfeb3a2302aa7fc0f725d29be0e8cc629561f463f2c8b06e2958497869d42cc53
  languageName: node
  linkType: hard

"browser-resolve@npm:^2.0.0":
  version: 2.0.0
  resolution: "browser-resolve@npm:2.0.0"
  dependencies:
    resolve: ^1.17.0
  checksum: 69225e73b555bd6d2a08fb93c7342cfcf3b5058b975099c52649cd5c3cec84c2066c5385084d190faedfb849684d9dabe10129f0cd401d1883572f2e6650f440
  languageName: node
  linkType: hard

"browserify-aes@npm:^1.0.0, browserify-aes@npm:^1.0.4, browserify-aes@npm:^1.2.0":
  version: 1.2.0
  resolution: "browserify-aes@npm:1.2.0"
  dependencies:
    buffer-xor: ^1.0.3
    cipher-base: ^1.0.0
    create-hash: ^1.1.0
    evp_bytestokey: ^1.0.3
    inherits: ^2.0.1
    safe-buffer: ^5.0.1
  checksum: 4a17c3eb55a2aa61c934c286f34921933086bf6d67f02d4adb09fcc6f2fc93977b47d9d884c25619144fccd47b3b3a399e1ad8b3ff5a346be47270114bcf7104
  languageName: node
  linkType: hard

"browserify-cipher@npm:^1.0.0":
  version: 1.0.1
  resolution: "browserify-cipher@npm:1.0.1"
  dependencies:
    browserify-aes: ^1.0.4
    browserify-des: ^1.0.0
    evp_bytestokey: ^1.0.0
  checksum: 2d8500acf1ee535e6bebe808f7a20e4c3a9e2ed1a6885fff1facbfd201ac013ef030422bec65ca9ece8ffe82b03ca580421463f9c45af6c8415fd629f4118c13
  languageName: node
  linkType: hard

"browserify-des@npm:^1.0.0":
  version: 1.0.2
  resolution: "browserify-des@npm:1.0.2"
  dependencies:
    cipher-base: ^1.0.1
    des.js: ^1.0.0
    inherits: ^2.0.1
    safe-buffer: ^5.1.2
  checksum: b15a3e358a1d78a3b62ddc06c845d02afde6fc826dab23f1b9c016e643e7b1fda41de628d2110b712f6a44fb10cbc1800bc6872a03ddd363fb50768e010395b7
  languageName: node
  linkType: hard

"browserify-rsa@npm:^4.0.0, browserify-rsa@npm:^4.1.0":
  version: 4.1.0
  resolution: "browserify-rsa@npm:4.1.0"
  dependencies:
    bn.js: ^5.0.0
    randombytes: ^2.0.1
  checksum: 155f0c135873efc85620571a33d884aa8810e40176125ad424ec9d85016ff105a07f6231650914a760cca66f29af0494087947b7be34880dd4599a0cd3c38e54
  languageName: node
  linkType: hard

"browserify-sign@npm:^4.0.0":
  version: 4.2.2
  resolution: "browserify-sign@npm:4.2.2"
  dependencies:
    bn.js: ^5.2.1
    browserify-rsa: ^4.1.0
    create-hash: ^1.2.0
    create-hmac: ^1.1.7
    elliptic: ^6.5.4
    inherits: ^2.0.4
    parse-asn1: ^5.1.6
    readable-stream: ^3.6.2
    safe-buffer: ^5.2.1
  checksum: b622730c0fc183328c3a1c9fdaaaa5118821ed6822b266fa6b0375db7e20061ebec87301d61931d79b9da9a96ada1cab317fce3c68f233e5e93ed02dbb35544c
  languageName: node
  linkType: hard

"browserify-zlib@npm:~0.2.0":
  version: 0.2.0
  resolution: "browserify-zlib@npm:0.2.0"
  dependencies:
    pako: ~1.0.5
  checksum: 5cd9d6a665190fedb4a97dfbad8dabc8698d8a507298a03f42c734e96d58ca35d3c7d4085e283440bbca1cd1938cff85031728079bedb3345310c58ab1ec92d6
  languageName: node
  linkType: hard

"browserify@npm:^17.0.0":
  version: 17.0.0
  resolution: "browserify@npm:17.0.0"
  dependencies:
    JSONStream: ^1.0.3
    assert: ^1.4.0
    browser-pack: ^6.0.1
    browser-resolve: ^2.0.0
    browserify-zlib: ~0.2.0
    buffer: ~5.2.1
    cached-path-relative: ^1.0.0
    concat-stream: ^1.6.0
    console-browserify: ^1.1.0
    constants-browserify: ~1.0.0
    crypto-browserify: ^3.0.0
    defined: ^1.0.0
    deps-sort: ^2.0.1
    domain-browser: ^1.2.0
    duplexer2: ~0.1.2
    events: ^3.0.0
    glob: ^7.1.0
    has: ^1.0.0
    htmlescape: ^1.1.0
    https-browserify: ^1.0.0
    inherits: ~2.0.1
    insert-module-globals: ^7.2.1
    labeled-stream-splicer: ^2.0.0
    mkdirp-classic: ^0.5.2
    module-deps: ^6.2.3
    os-browserify: ~0.3.0
    parents: ^1.0.1
    path-browserify: ^1.0.0
    process: ~0.11.0
    punycode: ^1.3.2
    querystring-es3: ~0.2.0
    read-only-stream: ^2.0.0
    readable-stream: ^2.0.2
    resolve: ^1.1.4
    shasum-object: ^1.0.0
    shell-quote: ^1.6.1
    stream-browserify: ^3.0.0
    stream-http: ^3.0.0
    string_decoder: ^1.1.1
    subarg: ^1.0.0
    syntax-error: ^1.1.1
    through2: ^2.0.0
    timers-browserify: ^1.0.1
    tty-browserify: 0.0.1
    url: ~0.11.0
    util: ~0.12.0
    vm-browserify: ^1.0.0
    xtend: ^4.0.0
  bin:
    browserify: bin/cmd.js
  checksum: 6b1dda742eb0eaef8bddffc00328fe4a874e4db251fcea85402663aa74c41d39bee424bedab6094ea9e965b9207cb0ac836f44c024e47045fde5ccb2bb845cb8
  languageName: node
  linkType: hard

"browserslist@npm:^4.21.3, browserslist@npm:^4.21.5":
  version: 4.21.5
  resolution: "browserslist@npm:4.21.5"
  dependencies:
    caniuse-lite: ^1.0.30001449
    electron-to-chromium: ^1.4.284
    node-releases: ^2.0.8
    update-browserslist-db: ^1.0.10
  bin:
    browserslist: cli.js
  checksum: 9755986b22e73a6a1497fd8797aedd88e04270be33ce66ed5d85a1c8a798292a65e222b0f251bafa1c2522261e237d73b08b58689d4920a607e5a53d56dc4706
  languageName: node
  linkType: hard

"bs58@npm:^4.0.0":
  version: 4.0.1
  resolution: "bs58@npm:4.0.1"
  dependencies:
    base-x: ^3.0.2
  checksum: b3c5365bb9e0c561e1a82f1a2d809a1a692059fae016be233a6127ad2f50a6b986467c3a50669ce4c18929dcccb297c5909314dd347a25a68c21b68eb3e95ac2
  languageName: node
  linkType: hard

"bs58check@npm:^2.1.2":
  version: 2.1.2
  resolution: "bs58check@npm:2.1.2"
  dependencies:
    bs58: ^4.0.0
    create-hash: ^1.1.0
    safe-buffer: ^5.1.2
  checksum: 43bdf08a5dd04581b78f040bc4169480e17008da482ffe2a6507327bbc4fc5c28de0501f7faf22901cfe57fbca79cbb202ca529003fedb4cb8dccd265b38e54d
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: ^0.4.0
  checksum: 9ba4dc58ce86300c862bffc3ae91f00b2a03b01ee07f3564beeeaf82aa243b8b03ba53f123b0b842c190d4399b94697970c8e7cf7b1ea44b61aa28c3526a4449
  languageName: node
  linkType: hard

"buffer-equal@npm:0.0.1":
  version: 0.0.1
  resolution: "buffer-equal@npm:0.0.1"
  checksum: ca4b52e6c01143529d957a78cb9a93e4257f172bbab30d9eb87c20ae085ed23c5e07f236ac051202dacbf3d17aba42e1455f84cba21ea79b67d57f2b05e9a613
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"buffer-xor@npm:^1.0.3":
  version: 1.0.3
  resolution: "buffer-xor@npm:1.0.3"
  checksum: 10c520df29d62fa6e785e2800e586a20fc4f6dfad84bcdbd12e1e8a83856de1cb75c7ebd7abe6d036bbfab738a6cf18a3ae9c8e5a2e2eb3167ca7399ce65373a
  languageName: node
  linkType: hard

"buffer@npm:~5.2.1":
  version: 5.2.1
  resolution: "buffer@npm:5.2.1"
  dependencies:
    base64-js: ^1.0.2
    ieee754: ^1.1.4
  checksum: aa3f25bb88d313b8317b436677b46e9e32db64ae397dd5a9d1f867da132985b857c71deaa36cc37666fdb955d8d0f66abeae9460aa7d9b2dca36a9da2f50d05e
  languageName: node
  linkType: hard

"builtin-status-codes@npm:^3.0.0":
  version: 3.0.0
  resolution: "builtin-status-codes@npm:3.0.0"
  checksum: 1119429cf4b0d57bf76b248ad6f529167d343156ebbcc4d4e4ad600484f6bc63002595cbb61b67ad03ce55cd1d3c4711c03bbf198bf24653b8392420482f3773
  languageName: node
  linkType: hard

"cacache@npm:^16.1.0":
  version: 16.1.3
  resolution: "cacache@npm:16.1.3"
  dependencies:
    "@npmcli/fs": ^2.1.0
    "@npmcli/move-file": ^2.0.0
    chownr: ^2.0.0
    fs-minipass: ^2.1.0
    glob: ^8.0.1
    infer-owner: ^1.0.4
    lru-cache: ^7.7.1
    minipass: ^3.1.6
    minipass-collect: ^1.0.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    mkdirp: ^1.0.4
    p-map: ^4.0.0
    promise-inflight: ^1.0.1
    rimraf: ^3.0.2
    ssri: ^9.0.0
    tar: ^6.1.11
    unique-filename: ^2.0.0
  checksum: d91409e6e57d7d9a3a25e5dcc589c84e75b178ae8ea7de05cbf6b783f77a5fae938f6e8fda6f5257ed70000be27a681e1e44829251bfffe4c10216002f8f14e6
  languageName: node
  linkType: hard

"cached-path-relative@npm:^1.0.0, cached-path-relative@npm:^1.0.2":
  version: 1.1.0
  resolution: "cached-path-relative@npm:1.1.0"
  checksum: 2f1d63c2301feda575039b945811e54b2dc851b49e94aa366d2916fece745fe4f4490a28a68bd0afe79c2fe336bebf62cbdfa2ad75b178d33b074089114d402d
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind@npm:1.0.2"
  dependencies:
    function-bind: ^1.1.1
    get-intrinsic: ^1.0.2
  checksum: f8e31de9d19988a4b80f3e704788c4a2d6b6f3d17cfec4f57dc29ced450c53a49270dc66bf0fbd693329ee948dd33e6c90a329519aef17474a4d961e8d6426b0
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001449":
  version: 1.0.30001452
  resolution: "caniuse-lite@npm:1.0.30001452"
  checksum: de02aad7b71112409f30de53e8080bef0fe612ed95bba8b14fb830f59683e8caabc27bdd520563686965be77f2cb56e239e44b920144630b91d7fe9911ba8ad5
  languageName: node
  linkType: hard

"chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.1":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"char-regex@npm:^1.0.2":
  version: 1.0.2
  resolution: "char-regex@npm:1.0.2"
  checksum: b563e4b6039b15213114626621e7a3d12f31008bdce20f9c741d69987f62aeaace7ec30f6018890ad77b2e9b4d95324c9f5acfca58a9441e3b1dcdd1e2525d17
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0":
  version: 3.8.0
  resolution: "ci-info@npm:3.8.0"
  checksum: d0a4d3160497cae54294974a7246202244fff031b0a6ea20dd57b10ec510aa17399c41a1b0982142c105f3255aff2173e5c0dd7302ee1b2f28ba3debda375098
  languageName: node
  linkType: hard

"cipher-base@npm:^1.0.0, cipher-base@npm:^1.0.1, cipher-base@npm:^1.0.3":
  version: 1.0.4
  resolution: "cipher-base@npm:1.0.4"
  dependencies:
    inherits: ^2.0.1
    safe-buffer: ^5.0.1
  checksum: 47d3568dbc17431a339bad1fe7dff83ac0891be8206911ace3d3b818fc695f376df809bea406e759cdea07fff4b454fa25f1013e648851bec790c1d75763032e
  languageName: node
  linkType: hard

"cjs-module-lexer@npm:^1.0.0":
  version: 1.2.2
  resolution: "cjs-module-lexer@npm:1.2.2"
  checksum: 977f3f042bd4f08e368c890d91eecfbc4f91da0bc009a3c557bc4dfbf32022ad1141244ac1178d44de70fc9f3dea7add7cd9a658a34b9fae98a55d8f92331ce5
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.6.0":
  version: 2.7.0
  resolution: "cli-spinners@npm:2.7.0"
  checksum: a9afaf73f58d1f951fb23742f503631b3cf513f43f4c7acb1b640100eb76bfa16efbcd1994d149ffc6603a6d75dd3d4a516a76f125f90dce437de9b16fd0ee6f
  languageName: node
  linkType: hard

"cli-width@npm:^3.0.0":
  version: 3.0.0
  resolution: "cli-width@npm:3.0.0"
  checksum: 4c94af3769367a70e11ed69aa6095f1c600c0ff510f3921ab4045af961820d57c0233acfa8b6396037391f31b4c397e1f614d234294f979ff61430a6c166c3f6
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.1
    wrap-ansi: ^7.0.0
  checksum: 79648b3b0045f2e285b76fb2e24e207c6db44323581e421c3acbd0e86454cba1b37aea976ab50195a49e7384b871e6dfb2247ad7dec53c02454ac6497394cb56
  languageName: node
  linkType: hard

"co@npm:^4.6.0":
  version: 4.6.0
  resolution: "co@npm:4.6.0"
  checksum: 5210d9223010eb95b29df06a91116f2cf7c8e0748a9013ed853b53f362ea0e822f1e5bb054fb3cefc645239a4cf966af1f6133a3b43f40d591f3b68ed6cf0510
  languageName: node
  linkType: hard

"collect-v8-coverage@npm:^1.0.0":
  version: 1.0.1
  resolution: "collect-v8-coverage@npm:1.0.1"
  checksum: 4efe0a1fccd517b65478a2364b33dadd0a43fc92a56f59aaece9b6186fe5177b2de471253587de7c91516f07c7268c2f6770b6cbcffc0e0ece353b766ec87e55
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-support@npm:^1.1.3":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 9b7356817670b9a13a26ca5af1c21615463b500783b739b7634a0c2047c16cef4b2865d7576875c31c3cddf9dd621fa19285e628f20198b233a5cfdda6d0793b
  languageName: node
  linkType: hard

"combine-source-map@npm:^0.8.0, combine-source-map@npm:~0.8.0":
  version: 0.8.0
  resolution: "combine-source-map@npm:0.8.0"
  dependencies:
    convert-source-map: ~1.1.0
    inline-source-map: ~0.6.0
    lodash.memoize: ~3.0.3
    source-map: ~0.5.3
  checksum: 26b3064a4e58400e04089acbf5c8741c47db079706bb2fcd79a7768f99d68de9baf1eb48081cdfbc568e308633105af2aeaf52c73e388619ba1f56463fb73a2e
  languageName: node
  linkType: hard

"commander@npm:^9.0.0":
  version: 9.5.0
  resolution: "commander@npm:9.5.0"
  checksum: c7a3e27aa59e913b54a1bafd366b88650bc41d6651f0cbe258d4ff09d43d6a7394232a4dadd0bf518b3e696fdf595db1028a0d82c785b88bd61f8a440cecfade
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"concat-stream@npm:^1.6.0, concat-stream@npm:^1.6.1, concat-stream@npm:~1.6.0":
  version: 1.6.2
  resolution: "concat-stream@npm:1.6.2"
  dependencies:
    buffer-from: ^1.0.0
    inherits: ^2.0.3
    readable-stream: ^2.2.2
    typedarray: ^0.0.6
  checksum: 1ef77032cb4459dcd5187bd710d6fc962b067b64ec6a505810de3d2b8cc0605638551b42f8ec91edf6fcd26141b32ef19ad749239b58fae3aba99187adc32285
  languageName: node
  linkType: hard

"console-browserify@npm:^1.1.0":
  version: 1.2.0
  resolution: "console-browserify@npm:1.2.0"
  checksum: 226591eeff8ed68e451dffb924c1fb750c654d54b9059b3b261d360f369d1f8f70650adecf2c7136656236a4bfeb55c39281b5d8a55d792ebbb99efd3d848d52
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 8755d76787f94e6cf79ce4666f0c5519906d7f5b02d4b884cf41e11dcd759ed69c57da0670afd9236d229a46e0f9cf519db0cd829c6dca820bb5a5c3def584ed
  languageName: node
  linkType: hard

"constants-browserify@npm:~1.0.0":
  version: 1.0.0
  resolution: "constants-browserify@npm:1.0.0"
  checksum: f7ac8c6d0b6e4e0c77340a1d47a3574e25abd580bfd99ad707b26ff7618596cf1a5e5ce9caf44715e9e01d4a5d12cb3b4edaf1176f34c19adb2874815a56e64f
  languageName: node
  linkType: hard

"convert-source-map@npm:^1.5.1, convert-source-map@npm:^1.6.0, convert-source-map@npm:^1.7.0":
  version: 1.9.0
  resolution: "convert-source-map@npm:1.9.0"
  checksum: dc55a1f28ddd0e9485ef13565f8f756b342f9a46c4ae18b843fe3c30c675d058d6a4823eff86d472f187b176f0adf51ea7b69ea38be34be4a63cbbf91b0593c8
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 63ae9933be5a2b8d4509daca5124e20c14d023c820258e484e32dc324d34c2754e71297c94a05784064ad27615037ef677e3f0c00469fb55f409d2bb21261035
  languageName: node
  linkType: hard

"convert-source-map@npm:~1.1.0":
  version: 1.1.3
  resolution: "convert-source-map@npm:1.1.3"
  checksum: 0ed6bdecd330fd05941b417b63ebc9001b438f6d6681cd9a068617c3d4b649794dc35c95ba239d0a01f0b9499912b9e0d0d1b7c612e3669c57c65ce4bbc8fdd8
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.25.1":
  version: 3.28.0
  resolution: "core-js-compat@npm:3.28.0"
  dependencies:
    browserslist: ^4.21.5
  checksum: 41d1d58c99ce7ee7abd8cf070f4c07a8f2655dbed1777d90a26246dddd7fac68315d53d2192584c8621a5328e6fe1a10da39b6bf2666e90fd5c2ff3b8f24e874
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"crc-32@npm:^1.2.0":
  version: 1.2.2
  resolution: "crc-32@npm:1.2.2"
  bin:
    crc32: bin/crc32.njs
  checksum: ad2d0ad0cbd465b75dcaeeff0600f8195b686816ab5f3ba4c6e052a07f728c3e70df2e3ca9fd3d4484dc4ba70586e161ca5a2334ec8bf5a41bf022a6103ff243
  languageName: node
  linkType: hard

"create-ecdh@npm:^4.0.0":
  version: 4.0.4
  resolution: "create-ecdh@npm:4.0.4"
  dependencies:
    bn.js: ^4.1.0
    elliptic: ^6.5.3
  checksum: 0dd7fca9711d09e152375b79acf1e3f306d1a25ba87b8ff14c2fd8e68b83aafe0a7dd6c4e540c9ffbdd227a5fa1ad9b81eca1f233c38bb47770597ba247e614b
  languageName: node
  linkType: hard

"create-hash@npm:^1.1.0, create-hash@npm:^1.1.2, create-hash@npm:^1.2.0":
  version: 1.2.0
  resolution: "create-hash@npm:1.2.0"
  dependencies:
    cipher-base: ^1.0.1
    inherits: ^2.0.1
    md5.js: ^1.3.4
    ripemd160: ^2.0.1
    sha.js: ^2.4.0
  checksum: 02a6ae3bb9cd4afee3fabd846c1d8426a0e6b495560a977ba46120c473cb283be6aa1cace76b5f927cf4e499c6146fb798253e48e83d522feba807d6b722eaa9
  languageName: node
  linkType: hard

"create-hmac@npm:^1.1.0, create-hmac@npm:^1.1.4, create-hmac@npm:^1.1.7":
  version: 1.1.7
  resolution: "create-hmac@npm:1.1.7"
  dependencies:
    cipher-base: ^1.0.3
    create-hash: ^1.1.0
    inherits: ^2.0.1
    ripemd160: ^2.0.0
    safe-buffer: ^5.0.1
    sha.js: ^2.4.8
  checksum: ba12bb2257b585a0396108c72830e85f882ab659c3320c83584b1037f8ab72415095167ced80dc4ce8e446a8ecc4b2acf36d87befe0707d73b26cf9dc77440ed
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 671cc7c7288c3a8406f3c69a3ae2fc85555c04169e9d611def9a675635472614f1c0ed0ef80955d5b6d4e724f6ced67f0ad1bb006c2ea643488fcfef994d7f52
  languageName: node
  linkType: hard

"crypto-browserify@npm:^3.0.0":
  version: 3.12.0
  resolution: "crypto-browserify@npm:3.12.0"
  dependencies:
    browserify-cipher: ^1.0.0
    browserify-sign: ^4.0.0
    create-ecdh: ^4.0.0
    create-hash: ^1.1.0
    create-hmac: ^1.1.0
    diffie-hellman: ^5.0.0
    inherits: ^2.0.1
    pbkdf2: ^3.0.3
    public-encrypt: ^4.0.0
    randombytes: ^2.0.0
    randomfill: ^1.0.3
  checksum: c1609af82605474262f3eaa07daa0b2140026bd264ab316d4bf1170272570dbe02f0c49e29407fe0d3634f96c507c27a19a6765fb856fed854a625f9d15618e2
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.1
  resolution: "csstype@npm:3.1.1"
  checksum: 1f7b4f5fdd955b7444b18ebdddf3f5c699159f13e9cf8ac9027ae4a60ae226aef9bbb14a6e12ca7dba3358b007cee6354b116e720262867c398de6c955ea451d
  languageName: node
  linkType: hard

"dash-ast@npm:^1.0.0":
  version: 1.0.0
  resolution: "dash-ast@npm:1.0.0"
  checksum: db59e5e275d8159fb3b84bcd2936470c3fecb626f6486c179a28afad141cd95a578faaa3695ad6106153ca861da99a3d891fda37757b49afab773b3a46c638e6
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.3, debug@npm:^4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: 2.1.2
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 3dbad3f94ea64f34431a9cbf0bafb61853eda57bff2880036153438f50fb5a84f27683ba0d8e5426bf41a8c6ff03879488120cf5b3a761e77953169c0600a708
  languageName: node
  linkType: hard

"dedent@npm:^0.7.0":
  version: 0.7.0
  resolution: "dedent@npm:0.7.0"
  checksum: 87de191050d9a40dd70cad01159a0bcf05ecb59750951242070b6abf9569088684880d00ba92a955b4058804f16eeaf91d604f283929b4f614d181cd7ae633d2
  languageName: node
  linkType: hard

"deep-diff@npm:^0.3.5":
  version: 0.3.8
  resolution: "deep-diff@npm:0.3.8"
  checksum: 8a0fb6cbe468e50211836f8daa1c14798b2d7436bfbcb7d8eb0902e0d61bf1dfd48d5b9edd46a10596182b90ad25f87461b8e55111ff9257b6067ad0676f79c9
  languageName: node
  linkType: hard

"deep-is@npm:~0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2":
  version: 4.3.0
  resolution: "deepmerge@npm:4.3.0"
  checksum: c7980eb5c5be040b371f1df0d566473875cfabed9f672ccc177b81ba8eee5686ce2478de2f1d0076391621cbe729e5eacda397179a59ef0f68901849647db126
  languageName: node
  linkType: hard

"defined@npm:^1.0.0":
  version: 1.0.1
  resolution: "defined@npm:1.0.1"
  checksum: b1a852300bdb57f297289b55eafdd0c517afaa3ec8190e78fce91b9d8d0c0369d4505ecbdacfd3d98372e664f4a267d9bd793938d4a8c76209c9d9516fbe2101
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: a51744d9b53c164ba9c0492471a1a2ffa0b6727451bdc89e31627fdf4adda9d51277cfcbfb20f0a6f08ccb3c436f341df3e92631a3440226d93a8971724771fd
  languageName: node
  linkType: hard

"depd@npm:^1.1.2":
  version: 1.1.2
  resolution: "depd@npm:1.1.2"
  checksum: 6b406620d269619852885ce15965272b829df6f409724415e0002c8632ab6a8c0a08ec1f0bd2add05dc7bd7507606f7e2cc034fa24224ab829580040b835ecd9
  languageName: node
  linkType: hard

"deps-sort@npm:^2.0.1":
  version: 2.0.1
  resolution: "deps-sort@npm:2.0.1"
  dependencies:
    JSONStream: ^1.0.3
    shasum-object: ^1.0.0
    subarg: ^1.0.0
    through2: ^2.0.0
  bin:
    deps-sort: bin/cmd.js
  checksum: 1cbaad500aa1592d7497321faf39c7bb7b86ed0930b1efd0c54efdf68433fc53d8bc844bb220723c7861b397ba886495ebdab2cb0fbf13262d1342d98a88622b
  languageName: node
  linkType: hard

"des.js@npm:^1.0.0":
  version: 1.0.1
  resolution: "des.js@npm:1.0.1"
  dependencies:
    inherits: ^2.0.1
    minimalistic-assert: ^1.0.0
  checksum: 1ec2eedd7ed6bd61dd5e0519fd4c96124e93bb22de8a9d211b02d63e5dd152824853d919bb2090f965cc0e3eb9c515950a9836b332020d810f9c71feb0fd7df4
  languageName: node
  linkType: hard

"detect-newline@npm:^3.0.0":
  version: 3.1.0
  resolution: "detect-newline@npm:3.1.0"
  checksum: ae6cd429c41ad01b164c59ea36f264a2c479598e61cba7c99da24175a7ab80ddf066420f2bec9a1c57a6bead411b4655ff15ad7d281c000a89791f48cbe939e7
  languageName: node
  linkType: hard

"detective@npm:^5.2.0":
  version: 5.2.1
  resolution: "detective@npm:5.2.1"
  dependencies:
    acorn-node: ^1.8.2
    defined: ^1.0.0
    minimist: ^1.2.6
  bin:
    detective: bin/detective.js
  checksum: dc4601bbc6be850edb3c2dab7a0eaf5a6169a15ad201679c66d40ea1986df816eeaecd590047f15b0780285f3eeea13b82dca0d4c52a47e744a571e326a72dc9
  languageName: node
  linkType: hard

"diff-sequences@npm:^29.4.3":
  version: 29.4.3
  resolution: "diff-sequences@npm:29.4.3"
  checksum: 28b265e04fdddcf7f9f814effe102cc95a9dec0564a579b5aed140edb24fc345c611ca52d76d725a3cab55d3888b915b5e8a4702e0f6058968a90fa5f41fcde7
  languageName: node
  linkType: hard

"diffie-hellman@npm:^5.0.0":
  version: 5.0.3
  resolution: "diffie-hellman@npm:5.0.3"
  dependencies:
    bn.js: ^4.1.0
    miller-rabin: ^4.0.0
    randombytes: ^2.0.0
  checksum: 0e620f322170c41076e70181dd1c24e23b08b47dbb92a22a644f3b89b6d3834b0f8ee19e37916164e5eb1ee26d2aa836d6129f92723995267250a0b541811065
  languageName: node
  linkType: hard

"domain-browser@npm:^1.2.0":
  version: 1.2.0
  resolution: "domain-browser@npm:1.2.0"
  checksum: 8f1235c7f49326fb762f4675795246a6295e7dd566b4697abec24afdba2460daa7dfbd1a73d31efbf5606b3b7deadb06ce47cf06f0a476e706153d62a4ff2b90
  languageName: node
  linkType: hard

"duplexer2@npm:^0.1.2, duplexer2@npm:~0.1.0, duplexer2@npm:~0.1.2, duplexer2@npm:~0.1.4":
  version: 0.1.4
  resolution: "duplexer2@npm:0.1.4"
  dependencies:
    readable-stream: ^2.0.2
  checksum: 744961f03c7f54313f90555ac20284a3fb7bf22fdff6538f041a86c22499560eb6eac9d30ab5768054137cb40e6b18b40f621094e0261d7d8c35a37b7a5ad241
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.4.284":
  version: 1.4.297
  resolution: "electron-to-chromium@npm:1.4.297"
  checksum: d5b83384c378cb22e023abd9ad566abfd573ea7a24109b992f034752bcf77602b67b5a20064a8810ee6bd4db17547d80fa7e30c3602f57a503950e8063491eb2
  languageName: node
  linkType: hard

"elliptic@npm:^6.6.1":
  version: 6.6.1
  resolution: "elliptic@npm:6.6.1"
  dependencies:
    bn.js: ^4.11.9
    brorand: ^1.1.0
    hash.js: ^1.0.0
    hmac-drbg: ^1.0.1
    inherits: ^2.0.4
    minimalistic-assert: ^1.0.1
    minimalistic-crypto-utils: ^1.0.1
  checksum: 27b14a52f68bbbc0720da259f712cb73e953f6d2047958cd02fb0d0ade2e83849dc39fb4af630889c67df8817e24237428cf59c4f4c07700f755b401149a7375
  languageName: node
  linkType: hard

"emittery@npm:^0.13.1":
  version: 0.13.1
  resolution: "emittery@npm:0.13.1"
  checksum: 2b089ab6306f38feaabf4f6f02792f9ec85fc054fda79f44f6790e61bbf6bc4e1616afb9b232e0c5ec5289a8a452f79bfa6d905a6fd64e94b49981f0934001c6
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.1
  resolution: "escalade@npm:3.1.1"
  checksum: a3e2a99f07acb74b3ad4989c48ca0c3140f69f923e56d0cba0526240ee470b91010f9d39001f2a4a313841d237ede70a729e92125191ba5d21e74b106800b133
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 9f8a2d5743677c16e85c810e3024d54f0c8dea6424fad3c79ef6666e81dd0846f7437f5e729dfcdac8981bc9e5294c39b4580814d114076b8d36318f46ae4395
  languageName: node
  linkType: hard

"escodegen@npm:^1.11.1":
  version: 1.14.3
  resolution: "escodegen@npm:1.14.3"
  dependencies:
    esprima: ^4.0.1
    estraverse: ^4.2.0
    esutils: ^2.0.2
    optionator: ^0.8.1
    source-map: ~0.6.1
  dependenciesMeta:
    source-map:
      optional: true
  bin:
    escodegen: bin/escodegen.js
    esgenerate: bin/esgenerate.js
  checksum: 381cdc4767ecdb221206bbbab021b467bbc2a6f5c9a99c9e6353040080bdd3dfe73d7604ad89a47aca6ea7d58bc635f6bd3fbc8da9a1998e9ddfa8372362ccd0
  languageName: node
  linkType: hard

"escodegen@npm:~1.9.0":
  version: 1.9.1
  resolution: "escodegen@npm:1.9.1"
  dependencies:
    esprima: ^3.1.3
    estraverse: ^4.2.0
    esutils: ^2.0.2
    optionator: ^0.8.1
    source-map: ~0.6.1
  dependenciesMeta:
    source-map:
      optional: true
  bin:
    escodegen: ./bin/escodegen.js
    esgenerate: ./bin/esgenerate.js
  checksum: 628dae3d486db2428d2349960c0e5c523e3941582c481030fbc851577c512f1216d09e89711ef5234e9c4b81a2ff089e5231c17ee86eb093b1d9706d3491f3d0
  languageName: node
  linkType: hard

"esprima@npm:^3.1.3":
  version: 3.1.3
  resolution: "esprima@npm:3.1.3"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 2771c059aa61f2b2fe9d898f11c737c45c26eae6052908f1e2b8bd91c6a440607420f2679cbfd6cbb79f2fa502b37a3053048d287bcd30ea582d46c969fcf67e
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0, esprima@npm:^4.0.1":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: b45bc805a613dbea2835278c306b91aff6173c8d034223fa81498c77dcbce3b2931bf6006db816f62eacd9fd4ea975dfd85a5b7f3c6402cfd050d4ca3c13a628
  languageName: node
  linkType: hard

"estraverse@npm:^4.2.0":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:^0.1.3":
  version: 0.1.3
  resolution: "ethereum-cryptography@npm:0.1.3"
  dependencies:
    "@types/pbkdf2": ^3.0.0
    "@types/secp256k1": ^4.0.1
    blakejs: ^1.1.0
    browserify-aes: ^1.2.0
    bs58check: ^2.1.2
    create-hash: ^1.2.0
    create-hmac: ^1.1.7
    hash.js: ^1.1.7
    keccak: ^3.0.0
    pbkdf2: ^3.0.17
    randombytes: ^2.1.0
    safe-buffer: ^5.1.2
    scrypt-js: ^3.0.0
    secp256k1: ^4.0.1
    setimmediate: ^1.0.5
  checksum: 54bae7a4a96bd81398cdc35c91cfcc74339f71a95ed1b5b694663782e69e8e3afd21357de3b8bac9ff4877fd6f043601e200a7ad9133d94be6fd7d898ee0a449
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:^2.0.0":
  version: 2.1.2
  resolution: "ethereum-cryptography@npm:2.1.2"
  dependencies:
    "@noble/curves": 1.1.0
    "@noble/hashes": 1.3.1
    "@scure/bip32": 1.3.1
    "@scure/bip39": 1.2.1
  checksum: 2e8f7b8cc90232ae838ab6a8167708e8362621404d26e79b5d9e762c7b53d699f7520aff358d9254de658fcd54d2d0af168ff909943259ed27dc4cef2736410c
  languageName: node
  linkType: hard

"ethereumjs-util@npm:^7.1.4":
  version: 7.1.5
  resolution: "ethereumjs-util@npm:7.1.5"
  dependencies:
    "@types/bn.js": ^5.1.0
    bn.js: ^5.1.2
    create-hash: ^1.1.2
    ethereum-cryptography: ^0.1.3
    rlp: ^2.2.4
  checksum: 27a3c79d6e06b2df34b80d478ce465b371c8458b58f5afc14d91c8564c13363ad336e6e83f57eb0bd719fde94d10ee5697ceef78b5aa932087150c5287b286d1
  languageName: node
  linkType: hard

"events@npm:^3.0.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: f6f487ad2198aa41d878fa31452f1a3c00958f46e9019286ff4787c84aac329332ab45c9cdc8c445928fc6d7ded294b9e005a7fce9426488518017831b272780
  languageName: node
  linkType: hard

"evp_bytestokey@npm:^1.0.0, evp_bytestokey@npm:^1.0.3":
  version: 1.0.3
  resolution: "evp_bytestokey@npm:1.0.3"
  dependencies:
    md5.js: ^1.3.4
    node-gyp: latest
    safe-buffer: ^5.1.1
  checksum: ad4e1577f1a6b721c7800dcc7c733fe01f6c310732bb5bf2240245c2a5b45a38518b91d8be2c610611623160b9d1c0e91f1ce96d639f8b53e8894625cf20fa45
  languageName: node
  linkType: hard

"execa@npm:^5.0.0":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^6.0.0
    human-signals: ^2.1.0
    is-stream: ^2.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^4.0.1
    onetime: ^5.1.2
    signal-exit: ^3.0.3
    strip-final-newline: ^2.0.0
  checksum: fba9022c8c8c15ed862847e94c252b3d946036d7547af310e344a527e59021fd8b6bb0723883ea87044dc4f0201f949046993124a42ccb0855cae5bf8c786343
  languageName: node
  linkType: hard

"exit@npm:^0.1.2":
  version: 0.1.2
  resolution: "exit@npm:0.1.2"
  checksum: abc407f07a875c3961e4781dfcb743b58d6c93de9ab263f4f8c9d23bb6da5f9b7764fc773f86b43dd88030444d5ab8abcb611cb680fba8ca075362b77114bba3
  languageName: node
  linkType: hard

"expect@npm:^29.4.3":
  version: 29.4.3
  resolution: "expect@npm:29.4.3"
  dependencies:
    "@jest/expect-utils": ^29.4.3
    jest-get-type: ^29.4.3
    jest-matcher-utils: ^29.4.3
    jest-message-util: ^29.4.3
    jest-util: ^29.4.3
  checksum: ff9dd8c50c0c6fd4b2b00f6dbd7ab0e2063fe1953be81a8c10ae1c005c7f5667ba452918e2efb055504b72b701a4f82575a081a0a7158efb16d87991b0366feb
  languageName: node
  linkType: hard

"falafel@npm:^2.1.0":
  version: 2.2.5
  resolution: "falafel@npm:2.2.5"
  dependencies:
    acorn: ^7.1.1
    isarray: ^2.0.1
  checksum: bfd46e92bca87670fd2ef31c6123088431271f98f3b2a300a58e9c3e5f4f9944f0058f7daaaaa8cefd68d461a334bd528c952bcec17061522b68b61f7925b382
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.1.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:~2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fast-safe-stringify@npm:^2.0.7":
  version: 2.1.1
  resolution: "fast-safe-stringify@npm:2.1.1"
  checksum: a851cbddc451745662f8f00ddb622d6766f9bd97642dabfd9a405fb0d646d69fc0b9a1243cbf67f5f18a39f40f6fa821737651ff1bceeba06c9992ca2dc5bd3d
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0":
  version: 2.0.2
  resolution: "fb-watchman@npm:2.0.2"
  dependencies:
    bser: 2.1.1
  checksum: b15a124cef28916fe07b400eb87cbc73ca082c142abf7ca8e8de6af43eca79ca7bd13eb4d4d48240b3bd3136eaac40d16e42d6edf87a8e5d1dd8070626860c78
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.3
  resolution: "for-each@npm:0.3.3"
  dependencies:
    is-callable: ^1.1.3
  checksum: 6c48ff2bc63362319c65e2edca4a8e1e3483a2fabc72fbe7feaf8c73db94fc7861bd53bc02c8a66a0c1dd709da6b04eec42e0abdd6b40ce47305ae92a25e5d28
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0, fs-minipass@npm:^2.1.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: ^3.0.0
  checksum: 1b8d128dae2ac6cc94230cc5ead341ba3e0efaef82dab46a33d171c044caaa6ca001364178d42069b2809c35a1c3c35079a32107c770e9ffab3901b59af8c8b1
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:^2.3.2":
  version: 2.3.2
  resolution: "fsevents@npm:2.3.2"
  dependencies:
    node-gyp: latest
  checksum: 97ade64e75091afee5265e6956cb72ba34db7819b4c3e94c431d4be2b19b8bb7a2d4116da417950c3425f17c8fe693d25e20212cac583ac1521ad066b77ae31f
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@^2.3.2#~builtin<compat/fsevents>":
  version: 2.3.2
  resolution: "fsevents@patch:fsevents@npm%3A2.3.2#~builtin<compat/fsevents>::version=2.3.2&hash=df0bf1"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.1":
  version: 1.1.1
  resolution: "function-bind@npm:1.1.1"
  checksum: b32fbaebb3f8ec4969f033073b43f5c8befbb58f1a79e12f1d7490358150359ebd92f49e72ff0144f65f2c48ea2a605bff2d07965f548f6474fd8efd95bf361a
  languageName: node
  linkType: hard

"gauge@npm:^4.0.3":
  version: 4.0.4
  resolution: "gauge@npm:4.0.4"
  dependencies:
    aproba: ^1.0.3 || ^2.0.0
    color-support: ^1.1.3
    console-control-strings: ^1.1.0
    has-unicode: ^2.0.1
    signal-exit: ^3.0.7
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wide-align: ^1.1.5
  checksum: 788b6bfe52f1dd8e263cda800c26ac0ca2ff6de0b6eee2fe0d9e3abf15e149b651bd27bf5226be10e6e3edb5c4e5d5985a5a1a98137e7a892f75eff76467ad2d
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-assigned-identifiers@npm:^1.2.0":
  version: 1.2.0
  resolution: "get-assigned-identifiers@npm:1.2.0"
  checksum: 5ea831c744a645ebd56fff818c80ffc583995c2ca3958236c7cfaac670242300e4f08498a9bbafd3ecbe30027d58ed50e7fa6268ecfe4b8e5c888ea7275cb56c
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.0.2, get-intrinsic@npm:^1.1.3":
  version: 1.2.0
  resolution: "get-intrinsic@npm:1.2.0"
  dependencies:
    function-bind: ^1.1.1
    has: ^1.0.3
    has-symbols: ^1.0.3
  checksum: 78fc0487b783f5c58cf2dccafc3ae656ee8d2d8062a8831ce4a95e7057af4587a1d4882246c033aca0a7b4965276f4802b45cc300338d1b77a73d3e3e3f4877d
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: bba0811116d11e56d702682ddef7c73ba3481f114590e705fc549f4d868972263896af313c57a25c076e3c0d567e11d919a64ba1b30c879be985fc9d44f96148
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: e04ecece32c92eebf5b8c940f51468cd53554dcbb0ea725b2748be583c9523d00128137966afce410b9b051eb2ef16d657cd2b120ca8edafcf5a65e81af63cad
  languageName: node
  linkType: hard

"glob@npm:^7.1.0, glob@npm:^7.1.3, glob@npm:^7.1.4":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 29452e97b38fa704dabb1d1045350fb2467cf0277e155aa9ff7077e90ad81d1ea9d53d3ee63bd37c05b09a065e90f16aec4a65f5b8de401d1dac40bc5605d133
  languageName: node
  linkType: hard

"glob@npm:^8.0.1":
  version: 8.1.0
  resolution: "glob@npm:8.1.0"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^5.0.1
    once: ^1.3.0
  checksum: 92fbea3221a7d12075f26f0227abac435de868dd0736a17170663783296d0dd8d3d532a5672b4488a439bf5d7fb85cdd07c11185d6cd39184f0385cbdfb86a47
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 67051a45eca3db904aee189dfc7cd53c20c7d881679c93f6146ddd4c9f4ab2268e68a919df740d39c71f4445d2b38ee360fc234428baea1dbdfe68bbcb46979e
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1":
  version: 1.0.1
  resolution: "gopd@npm:1.0.1"
  dependencies:
    get-intrinsic: ^1.1.3
  checksum: a5ccfb8806e0917a94e0b3de2af2ea4979c1da920bc381667c260e00e7cafdbe844e2cb9c5bcfef4e5412e8bf73bab837285bc35c7ba73aaaf0134d4583393a6
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.10
  resolution: "graceful-fs@npm:4.2.10"
  checksum: 3f109d70ae123951905d85032ebeae3c2a5a7a997430df00ea30df0e3a6c60cf6689b109654d6fdacd28810a053348c4d14642da1d075049e6be1ba5216218da
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.2, has-symbols@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-symbols@npm:1.0.3"
  checksum: a054c40c631c0d5741a8285010a0777ea0c068f99ed43e5d6eb12972da223f8af553a455132fdb0801bdcfa0e0f443c0c03a68d8555aa529b3144b446c3f2410
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-tostringtag@npm:1.0.0"
  dependencies:
    has-symbols: ^1.0.2
  checksum: cc12eb28cb6ae22369ebaad3a8ab0799ed61270991be88f208d508076a1e99abe4198c965935ce85ea90b60c94ddda73693b0920b58e7ead048b4a391b502c1c
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: 1eab07a7436512db0be40a710b29b5dc21fa04880b7f63c9980b706683127e3c1b57cb80ea96d47991bdae2dfe479604f6a1ba410106ee1046a41d1bd0814400
  languageName: node
  linkType: hard

"has@npm:^1.0.0, has@npm:^1.0.1, has@npm:^1.0.3":
  version: 1.0.3
  resolution: "has@npm:1.0.3"
  dependencies:
    function-bind: ^1.1.1
  checksum: b9ad53d53be4af90ce5d1c38331e712522417d017d5ef1ebd0507e07c2fbad8686fffb8e12ddecd4c39ca9b9b47431afbb975b8abf7f3c3b82c98e9aad052792
  languageName: node
  linkType: hard

"hash-base@npm:^3.0.0":
  version: 3.1.0
  resolution: "hash-base@npm:3.1.0"
  dependencies:
    inherits: ^2.0.4
    readable-stream: ^3.6.0
    safe-buffer: ^5.2.0
  checksum: 26b7e97ac3de13cb23fc3145e7e3450b0530274a9562144fc2bf5c1e2983afd0e09ed7cc3b20974ba66039fad316db463da80eb452e7373e780cbee9a0d2f2dc
  languageName: node
  linkType: hard

"hash.js@npm:^1.0.0, hash.js@npm:^1.0.3, hash.js@npm:^1.1.7":
  version: 1.1.7
  resolution: "hash.js@npm:1.1.7"
  dependencies:
    inherits: ^2.0.3
    minimalistic-assert: ^1.0.1
  checksum: e350096e659c62422b85fa508e4b3669017311aa4c49b74f19f8e1bc7f3a54a584fdfd45326d4964d6011f2b2d882e38bea775a96046f2a61b7779a979629d8f
  languageName: node
  linkType: hard

"hmac-drbg@npm:^1.0.1":
  version: 1.0.1
  resolution: "hmac-drbg@npm:1.0.1"
  dependencies:
    hash.js: ^1.0.3
    minimalistic-assert: ^1.0.0
    minimalistic-crypto-utils: ^1.0.1
  checksum: bd30b6a68d7f22d63f10e1888aee497d7c2c5c0bb469e66bbdac99f143904d1dfe95f8131f95b3e86c86dd239963c9d972fcbe147e7cffa00e55d18585c43fe0
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.3.0, hoist-non-react-statics@npm:^3.3.2":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: ^16.7.0
  checksum: b1538270429b13901ee586aa44f4cc3ecd8831c061d06cb8322e50ea17b3f5ce4d0e2e66394761e6c8e152cd8c34fb3b4b690116c6ce2bd45b18c746516cb9e8
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: d2df2da3ad40ca9ee3a39c5cc6475ef67c8f83c234475f24d8e9ce0dc80a2c82df8e1d6fa78ddd1e9022a586ea1bd247a615e80a5cd9273d90111ddda7d9e974
  languageName: node
  linkType: hard

"htmlescape@npm:^1.1.0":
  version: 1.1.1
  resolution: "htmlescape@npm:1.1.1"
  checksum: c59a915ae6ae076b5720243c8c594fd8c76e927d511ed5f205e4d586f47d521478d7148dc7fbe3d4a0cfc30abcc2dd215b30255903c09ed04eb38bca44367c5d
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.0":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "http-proxy-agent@npm:5.0.0"
  dependencies:
    "@tootallnate/once": 2
    agent-base: 6
    debug: 4
  checksum: e2ee1ff1656a131953839b2a19cd1f3a52d97c25ba87bd2559af6ae87114abf60971e498021f9b73f9fd78aea8876d1fb0d4656aac8a03c6caa9fc175f22b786
  languageName: node
  linkType: hard

"https-browserify@npm:^1.0.0":
  version: 1.0.0
  resolution: "https-browserify@npm:1.0.0"
  checksum: 09b35353e42069fde2435760d13f8a3fb7dd9105e358270e2e225b8a94f811b461edd17cb57594e5f36ec1218f121c160ddceeec6e8be2d55e01dcbbbed8cbae
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: 6
    debug: 4
  checksum: 571fccdf38184f05943e12d37d6ce38197becdd69e58d03f43637f7fa1269cf303a7d228aa27e5b27bbd3af8f09fd938e1c91dcfefff2df7ba77c20ed8dfc765
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: b87fd89fce72391625271454e70f67fe405277415b48bcc0117ca73d31fa23a4241787afdc8d67f5a116cf37258c052f59ea82daffa72364d61351423848e3b8
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: ^2.0.0
  checksum: 9c7a74a2827f9294c009266c82031030eae811ca87b0da3dceb8d6071b9bde22c9f3daef0469c3c533cc67a97d8a167cd9fc0389350e5f415f61a79b171ded16
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.4":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"import-local@npm:^3.0.2":
  version: 3.1.0
  resolution: "import-local@npm:3.1.0"
  dependencies:
    pkg-dir: ^4.2.0
    resolve-cwd: ^3.0.0
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: bfcdb63b5e3c0e245e347f3107564035b128a414c4da1172a20dc67db2504e05ede4ac2eee1252359f78b0bfd7b19ef180aec427c2fce6493ae782d73a04cddd
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"infer-owner@npm:^1.0.4":
  version: 1.0.4
  resolution: "infer-owner@npm:1.0.4"
  checksum: 181e732764e4a0611576466b4b87dac338972b839920b2a8cde43642e4ed6bd54dc1fb0b40874728f2a2df9a1b097b8ff83b56d5f8f8e3927f837fdcb47d8a89
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.1, inherits@npm:~2.0.3, inherits@npm:~2.0.4":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"inherits@npm:2.0.1":
  version: 2.0.1
  resolution: "inherits@npm:2.0.1"
  checksum: 6536b9377296d4ce8ee89c5c543cb75030934e61af42dba98a428e7d026938c5985ea4d1e3b87743a5b834f40ed1187f89c2d7479e9d59e41d2d1051aefba07b
  languageName: node
  linkType: hard

"inline-source-map@npm:~0.6.0":
  version: 0.6.2
  resolution: "inline-source-map@npm:0.6.2"
  dependencies:
    source-map: ~0.5.3
  checksum: 1f7fa2ad1764d03a0a525d5c47993f9e3d0445f29c2e2413d2878deecb6ecb1e6f9137a6207e3db8dc129565bde15de88c1ba2665407e753e7f3ec768ca29262
  languageName: node
  linkType: hard

"insert-module-globals@npm:^7.2.1":
  version: 7.2.1
  resolution: "insert-module-globals@npm:7.2.1"
  dependencies:
    JSONStream: ^1.0.3
    acorn-node: ^1.5.2
    combine-source-map: ^0.8.0
    concat-stream: ^1.6.1
    is-buffer: ^1.1.0
    path-is-absolute: ^1.0.1
    process: ~0.11.0
    through2: ^2.0.0
    undeclared-identifiers: ^1.1.2
    xtend: ^4.0.0
  bin:
    insert-module-globals: bin/cmd.js
  checksum: c44de7e802186e3207e24beadd71a5bb834700456a9e6f5c8fbb415b6f8356aff44df806e32bf9131143c53348d873fb050ea2b8f3c4cac762922e191b6bef15
  languageName: node
  linkType: hard

"ip@npm:^2.0.0":
  version: 2.0.0
  resolution: "ip@npm:2.0.0"
  checksum: cfcfac6b873b701996d71ec82a7dd27ba92450afdb421e356f44044ed688df04567344c36cbacea7d01b1c39a4c732dc012570ebe9bebfb06f27314bca625349
  languageName: node
  linkType: hard

"is-arguments@npm:^1.0.4":
  version: 1.1.1
  resolution: "is-arguments@npm:1.1.1"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: 7f02700ec2171b691ef3e4d0e3e6c0ba408e8434368504bb593d0d7c891c0dbfda6d19d30808b904a6cb1929bca648c061ba438c39f296c2a8ca083229c49f27
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-buffer@npm:^1.1.0":
  version: 1.1.6
  resolution: "is-buffer@npm:1.1.6"
  checksum: 4a186d995d8bbf9153b4bd9ff9fd04ae75068fe695d29025d25e592d9488911eeece84eefbd8fa41b8ddcc0711058a71d4c466dcf6f1f6e1d83830052d8ca707
  languageName: node
  linkType: hard

"is-callable@npm:^1.1.3":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-core-module@npm:^2.9.0":
  version: 2.11.0
  resolution: "is-core-module@npm:2.11.0"
  dependencies:
    has: ^1.0.3
  checksum: f96fd490c6b48eb4f6d10ba815c6ef13f410b0ba6f7eb8577af51697de523e5f2cd9de1c441b51d27251bf0e4aebc936545e33a5d26d5d51f28d25698d4a8bab
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-fn@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-generator-fn@npm:2.1.0"
  checksum: a6ad5492cf9d1746f73b6744e0c43c0020510b59d56ddcb78a91cbc173f09b5e6beff53d75c9c5a29feb618bfef2bf458e025ecf3a57ad2268e2fb2569f56215
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.7":
  version: 1.0.10
  resolution: "is-generator-function@npm:1.0.10"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: d54644e7dbaccef15ceb1e5d91d680eb5068c9ee9f9eb0a9e04173eb5542c9b51b5ab52c5537f5703e48d5fddfd376817c1ca07a84a407b7115b769d4bdde72b
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 93a32f01940220532e5948538699ad610d5924ac86093fcee83022252b363eb0cc99ba53ab084a04e4fb62bf7b5731f55496257a4c38adf87af9c4d352c71c35
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.10, is-typed-array@npm:^1.1.3":
  version: 1.1.10
  resolution: "is-typed-array@npm:1.1.10"
  dependencies:
    available-typed-arrays: ^1.0.5
    call-bind: ^1.0.2
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-tostringtag: ^1.0.0
  checksum: aac6ecb59d4c56a1cdeb69b1f129154ef462bbffe434cb8a8235ca89b42f258b7ae94073c41b3cb7bce37f6a1733ad4499f07882d5d5093a7ba84dfc4ebb8017
  languageName: node
  linkType: hard

"isarray@npm:^2.0.1":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: bd5bbe4104438c4196ba58a54650116007fa0262eccef13a4c55b2e09a5b36b59f1e75b9fcc49883dd9d4953892e6fc007eef9e9155648ceea036e184b0f930a
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0":
  version: 3.2.0
  resolution: "istanbul-lib-coverage@npm:3.2.0"
  checksum: a2a545033b9d56da04a8571ed05c8120bf10e9bce01cf8633a3a2b0d1d83dff4ac4fe78d6d5673c27fc29b7f21a41d75f83a36be09f82a61c367b56aa73c1ff9
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^5.0.4, istanbul-lib-instrument@npm:^5.1.0":
  version: 5.2.1
  resolution: "istanbul-lib-instrument@npm:5.2.1"
  dependencies:
    "@babel/core": ^7.12.3
    "@babel/parser": ^7.14.7
    "@istanbuljs/schema": ^0.1.2
    istanbul-lib-coverage: ^3.2.0
    semver: ^6.3.0
  checksum: bf16f1803ba5e51b28bbd49ed955a736488381e09375d830e42ddeb403855b2006f850711d95ad726f2ba3f1ae8e7366de7e51d2b9ac67dc4d80191ef7ddf272
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0":
  version: 3.0.0
  resolution: "istanbul-lib-report@npm:3.0.0"
  dependencies:
    istanbul-lib-coverage: ^3.0.0
    make-dir: ^3.0.0
    supports-color: ^7.1.0
  checksum: 3f29eb3f53c59b987386e07fe772d24c7f58c6897f34c9d7a296f4000de7ae3de9eb95c3de3df91dc65b134c84dee35c54eee572a56243e8907c48064e34ff1b
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^4.0.0":
  version: 4.0.1
  resolution: "istanbul-lib-source-maps@npm:4.0.1"
  dependencies:
    debug: ^4.1.1
    istanbul-lib-coverage: ^3.0.0
    source-map: ^0.6.1
  checksum: 21ad3df45db4b81852b662b8d4161f6446cd250c1ddc70ef96a585e2e85c26ed7cd9c2a396a71533cfb981d1a645508bc9618cae431e55d01a0628e7dec62ef2
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.3":
  version: 3.1.5
  resolution: "istanbul-reports@npm:3.1.5"
  dependencies:
    html-escaper: ^2.0.0
    istanbul-lib-report: ^3.0.0
  checksum: 7867228f83ed39477b188ea07e7ccb9b4f5320b6f73d1db93a0981b7414fa4ef72d3f80c4692c442f90fc250d9406e71d8d7ab65bb615cb334e6292b73192b89
  languageName: node
  linkType: hard

"jest-changed-files@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-changed-files@npm:29.4.3"
  dependencies:
    execa: ^5.0.0
    p-limit: ^3.1.0
  checksum: 9a70bd8e92b37e18ad26d8bea97c516f41119fb7046b4255a13c76d557b0e54fa0629726de5a093fadfd6a0a08ce45da65a57086664d505b8db4b3133133e141
  languageName: node
  linkType: hard

"jest-circus@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-circus@npm:29.4.3"
  dependencies:
    "@jest/environment": ^29.4.3
    "@jest/expect": ^29.4.3
    "@jest/test-result": ^29.4.3
    "@jest/types": ^29.4.3
    "@types/node": "*"
    chalk: ^4.0.0
    co: ^4.6.0
    dedent: ^0.7.0
    is-generator-fn: ^2.0.0
    jest-each: ^29.4.3
    jest-matcher-utils: ^29.4.3
    jest-message-util: ^29.4.3
    jest-runtime: ^29.4.3
    jest-snapshot: ^29.4.3
    jest-util: ^29.4.3
    p-limit: ^3.1.0
    pretty-format: ^29.4.3
    slash: ^3.0.0
    stack-utils: ^2.0.3
  checksum: 2739bef9c888743b49ff3fe303131381618e5d2f250f613a91240d9c86e19e6874fc904cbd8bcb02ec9ec59a84e5dae4ffec929f0c6171e87ddbc05508a137f4
  languageName: node
  linkType: hard

"jest-cli@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-cli@npm:29.4.3"
  dependencies:
    "@jest/core": ^29.4.3
    "@jest/test-result": ^29.4.3
    "@jest/types": ^29.4.3
    chalk: ^4.0.0
    exit: ^0.1.2
    graceful-fs: ^4.2.9
    import-local: ^3.0.2
    jest-config: ^29.4.3
    jest-util: ^29.4.3
    jest-validate: ^29.4.3
    prompts: ^2.0.1
    yargs: ^17.3.1
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: f4c9f6d76cde2c60a4169acbebb3f862728be03bcf3fe0077d2e55da7f9f3c3e9483cfa6e936832d35eabf96ee5ebf0300c4b0bd43cffff099801793466bfdd8
  languageName: node
  linkType: hard

"jest-config@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-config@npm:29.4.3"
  dependencies:
    "@babel/core": ^7.11.6
    "@jest/test-sequencer": ^29.4.3
    "@jest/types": ^29.4.3
    babel-jest: ^29.4.3
    chalk: ^4.0.0
    ci-info: ^3.2.0
    deepmerge: ^4.2.2
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    jest-circus: ^29.4.3
    jest-environment-node: ^29.4.3
    jest-get-type: ^29.4.3
    jest-regex-util: ^29.4.3
    jest-resolve: ^29.4.3
    jest-runner: ^29.4.3
    jest-util: ^29.4.3
    jest-validate: ^29.4.3
    micromatch: ^4.0.4
    parse-json: ^5.2.0
    pretty-format: ^29.4.3
    slash: ^3.0.0
    strip-json-comments: ^3.1.1
  peerDependencies:
    "@types/node": "*"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    "@types/node":
      optional: true
    ts-node:
      optional: true
  checksum: 92f9a9c6850b18682cb01892774a33967472af23a5844438d8c68077d5f2a29b15b665e4e4db7de3d74002a6dca158cd5b2cb9f5debfd2cce5e1aee6c74e3873
  languageName: node
  linkType: hard

"jest-diff@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-diff@npm:29.4.3"
  dependencies:
    chalk: ^4.0.0
    diff-sequences: ^29.4.3
    jest-get-type: ^29.4.3
    pretty-format: ^29.4.3
  checksum: 877fd1edffef6b319688c27b152e5b28e2bc4bcda5ce0ca90d7e137f9fafda4280bae25403d4c0bfd9806c2c0b15d966aa2dfaf5f9928ec8f1ccea7fa1d08ed6
  languageName: node
  linkType: hard

"jest-docblock@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-docblock@npm:29.4.3"
  dependencies:
    detect-newline: ^3.0.0
  checksum: e0e9df1485bb8926e5b33478cdf84b3387d9caf3658e7dc1eaa6dc34cb93dea0d2d74797f6e940f0233a88f3dadd60957f2288eb8f95506361f85b84bf8661df
  languageName: node
  linkType: hard

"jest-each@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-each@npm:29.4.3"
  dependencies:
    "@jest/types": ^29.4.3
    chalk: ^4.0.0
    jest-get-type: ^29.4.3
    jest-util: ^29.4.3
    pretty-format: ^29.4.3
  checksum: 1f72738338399efab0139eaea18bc198be0c6ed889770c8cbfa70bf9c724e8171fe1d3a29a94f9f39b8493ee6b2529bb350fb7c7c75e0d7eddfd28c253c79f9d
  languageName: node
  linkType: hard

"jest-environment-node@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-environment-node@npm:29.4.3"
  dependencies:
    "@jest/environment": ^29.4.3
    "@jest/fake-timers": ^29.4.3
    "@jest/types": ^29.4.3
    "@types/node": "*"
    jest-mock: ^29.4.3
    jest-util: ^29.4.3
  checksum: 3c7362edfdbd516e83af7367c95dde35761a482b174de9735c07633405486ec73e19624e9bea4333fca33c24e8d65eaa1aa6594e0cb6bfeeeb564ccc431ee61d
  languageName: node
  linkType: hard

"jest-get-type@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-get-type@npm:29.4.3"
  checksum: 6ac7f2dde1c65e292e4355b6c63b3a4897d7e92cb4c8afcf6d397f2682f8080e094c8b0b68205a74d269882ec06bf696a9de6cd3e1b7333531e5ed7b112605ce
  languageName: node
  linkType: hard

"jest-haste-map@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-haste-map@npm:29.4.3"
  dependencies:
    "@jest/types": ^29.4.3
    "@types/graceful-fs": ^4.1.3
    "@types/node": "*"
    anymatch: ^3.0.3
    fb-watchman: ^2.0.0
    fsevents: ^2.3.2
    graceful-fs: ^4.2.9
    jest-regex-util: ^29.4.3
    jest-util: ^29.4.3
    jest-worker: ^29.4.3
    micromatch: ^4.0.4
    walker: ^1.0.8
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: c7a83ebe6008b3fe96a96235e8153092e54b14df68e0f4205faedec57450df26b658578495a71c6d82494c01fbb44bca98c1506a6b2b9c920696dcc5d2e2bc59
  languageName: node
  linkType: hard

"jest-it-up@npm:^2.1.0":
  version: 2.1.0
  resolution: "jest-it-up@npm:2.1.0"
  dependencies:
    "@inquirer/confirm": ^0.0.14-alpha.0
    ansi-colors: ^4.1.0
    commander: ^9.0.0
  bin:
    jest-it-up: bin/jest-it-up
  checksum: 3c1587aca43c8d7c84ae31bcf984e49e87ff13d0f2a50f25e25ec8657794bf35577a2b6d8a302db77d97d4033e9543065095e166d9212b7d3ba1b76f5ffcf057
  languageName: node
  linkType: hard

"jest-leak-detector@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-leak-detector@npm:29.4.3"
  dependencies:
    jest-get-type: ^29.4.3
    pretty-format: ^29.4.3
  checksum: ec2b45e6f0abce81bd0dd0f6fd06b433c24d1ec865267af7640fae540ec868b93752598e407a9184d9c7419cbf32e8789007cc8c1be1a84f8f7321a0f8ad01f1
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-matcher-utils@npm:29.4.3"
  dependencies:
    chalk: ^4.0.0
    jest-diff: ^29.4.3
    jest-get-type: ^29.4.3
    pretty-format: ^29.4.3
  checksum: 9e13cbe42d2113bab2691110c7c3ba5cec3b94abad2727e1de90929d0f67da444e9b2066da3b476b5bf788df53a8ede0e0a950cfb06a04e4d6d566d115ee4f1d
  languageName: node
  linkType: hard

"jest-message-util@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-message-util@npm:29.4.3"
  dependencies:
    "@babel/code-frame": ^7.12.13
    "@jest/types": ^29.4.3
    "@types/stack-utils": ^2.0.0
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    micromatch: ^4.0.4
    pretty-format: ^29.4.3
    slash: ^3.0.0
    stack-utils: ^2.0.3
  checksum: 64f06b9550021e68da0059020bea8691283cf818918810bb67192d7b7fb9b691c7eadf55c2ca3cd04df5394918f2327245077095cdc0d6b04be3532d2c7d0ced
  languageName: node
  linkType: hard

"jest-mock@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-mock@npm:29.4.3"
  dependencies:
    "@jest/types": ^29.4.3
    "@types/node": "*"
    jest-util: ^29.4.3
  checksum: 8eb4a29b02d2cd03faac0290b6df6d23b4ffa43f72b21c7fff3c7dd04a2797355b1e85862b70b15341dd33ee3a693b17db5520a6f6e6b81ee75601987de6a1a2
  languageName: node
  linkType: hard

"jest-pnp-resolver@npm:^1.2.2":
  version: 1.2.3
  resolution: "jest-pnp-resolver@npm:1.2.3"
  peerDependencies:
    jest-resolve: "*"
  peerDependenciesMeta:
    jest-resolve:
      optional: true
  checksum: db1a8ab2cb97ca19c01b1cfa9a9c8c69a143fde833c14df1fab0766f411b1148ff0df878adea09007ac6a2085ec116ba9a996a6ad104b1e58c20adbf88eed9b2
  languageName: node
  linkType: hard

"jest-regex-util@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-regex-util@npm:29.4.3"
  checksum: 96fc7fc28cd4dd73a63c13a526202c4bd8b351d4e5b68b1a2a2c88da3308c2a16e26feaa593083eb0bac38cca1aa9dd05025412e7de013ba963fb8e66af22b8a
  languageName: node
  linkType: hard

"jest-resolve-dependencies@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-resolve-dependencies@npm:29.4.3"
  dependencies:
    jest-regex-util: ^29.4.3
    jest-snapshot: ^29.4.3
  checksum: 3ad934cd2170c9658d8800f84a975dafc866ec85b7ce391c640c09c3744ced337787620d8667dc8d1fa5e0b1493f973caa1a1bb980e4e6a50b46a1720baf0bd1
  languageName: node
  linkType: hard

"jest-resolve@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-resolve@npm:29.4.3"
  dependencies:
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    jest-haste-map: ^29.4.3
    jest-pnp-resolver: ^1.2.2
    jest-util: ^29.4.3
    jest-validate: ^29.4.3
    resolve: ^1.20.0
    resolve.exports: ^2.0.0
    slash: ^3.0.0
  checksum: 056a66beccf833f3c7e5a8fc9bfec218886e87b0b103decdbdf11893669539df489d1490cd6d5f0eea35731e8be0d2e955a6710498f970d2eae734da4df029dc
  languageName: node
  linkType: hard

"jest-runner@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-runner@npm:29.4.3"
  dependencies:
    "@jest/console": ^29.4.3
    "@jest/environment": ^29.4.3
    "@jest/test-result": ^29.4.3
    "@jest/transform": ^29.4.3
    "@jest/types": ^29.4.3
    "@types/node": "*"
    chalk: ^4.0.0
    emittery: ^0.13.1
    graceful-fs: ^4.2.9
    jest-docblock: ^29.4.3
    jest-environment-node: ^29.4.3
    jest-haste-map: ^29.4.3
    jest-leak-detector: ^29.4.3
    jest-message-util: ^29.4.3
    jest-resolve: ^29.4.3
    jest-runtime: ^29.4.3
    jest-util: ^29.4.3
    jest-watcher: ^29.4.3
    jest-worker: ^29.4.3
    p-limit: ^3.1.0
    source-map-support: 0.5.13
  checksum: c41108e5da01e0b8fdc2a06c5042eb49bb1d8db0e0d4651769fd1b9fe84ab45188617c11a3a8e1c83748b29bfe57dd77001ec57e86e3e3c30f3534e0314f8882
  languageName: node
  linkType: hard

"jest-runtime@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-runtime@npm:29.4.3"
  dependencies:
    "@jest/environment": ^29.4.3
    "@jest/fake-timers": ^29.4.3
    "@jest/globals": ^29.4.3
    "@jest/source-map": ^29.4.3
    "@jest/test-result": ^29.4.3
    "@jest/transform": ^29.4.3
    "@jest/types": ^29.4.3
    "@types/node": "*"
    chalk: ^4.0.0
    cjs-module-lexer: ^1.0.0
    collect-v8-coverage: ^1.0.0
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    jest-haste-map: ^29.4.3
    jest-message-util: ^29.4.3
    jest-mock: ^29.4.3
    jest-regex-util: ^29.4.3
    jest-resolve: ^29.4.3
    jest-snapshot: ^29.4.3
    jest-util: ^29.4.3
    slash: ^3.0.0
    strip-bom: ^4.0.0
  checksum: b99f8a910d1a38e7476058ba04ad44dfd3d93e837bb7c301d691e646a1085412fde87f06fbe271c9145f0e72d89400bfa7f6994bc30d456c7742269f37d0f570
  languageName: node
  linkType: hard

"jest-snapshot@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-snapshot@npm:29.4.3"
  dependencies:
    "@babel/core": ^7.11.6
    "@babel/generator": ^7.7.2
    "@babel/plugin-syntax-jsx": ^7.7.2
    "@babel/plugin-syntax-typescript": ^7.7.2
    "@babel/traverse": ^7.7.2
    "@babel/types": ^7.3.3
    "@jest/expect-utils": ^29.4.3
    "@jest/transform": ^29.4.3
    "@jest/types": ^29.4.3
    "@types/babel__traverse": ^7.0.6
    "@types/prettier": ^2.1.5
    babel-preset-current-node-syntax: ^1.0.0
    chalk: ^4.0.0
    expect: ^29.4.3
    graceful-fs: ^4.2.9
    jest-diff: ^29.4.3
    jest-get-type: ^29.4.3
    jest-haste-map: ^29.4.3
    jest-matcher-utils: ^29.4.3
    jest-message-util: ^29.4.3
    jest-util: ^29.4.3
    natural-compare: ^1.4.0
    pretty-format: ^29.4.3
    semver: ^7.3.5
  checksum: 79ba52f2435e23ce72b1309be4b17fdbcb299d1c2ce97ebb61df9a62711e9463035f63b4c849181b2fe5aa17b3e09d30ee4668cc25fb3c6f59511c010b4d9494
  languageName: node
  linkType: hard

"jest-util@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-util@npm:29.4.3"
  dependencies:
    "@jest/types": ^29.4.3
    "@types/node": "*"
    chalk: ^4.0.0
    ci-info: ^3.2.0
    graceful-fs: ^4.2.9
    picomatch: ^2.2.3
  checksum: 606b3e6077895baf8fb4ad4d08c134f37a6b81d5ba77ae654c942b1ae4b7294ab3b5a0eb93db34f129407b367970cf3b76bf5c80897b30f215f2bc8bf20a5f3f
  languageName: node
  linkType: hard

"jest-validate@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-validate@npm:29.4.3"
  dependencies:
    "@jest/types": ^29.4.3
    camelcase: ^6.2.0
    chalk: ^4.0.0
    jest-get-type: ^29.4.3
    leven: ^3.1.0
    pretty-format: ^29.4.3
  checksum: 983e56430d86bed238448cae031535c1d908f760aa312cd4a4ec0e92f3bc1b6675415ddf57cdeceedb8ad9c698e5bcd10f0a856dfc93a8923bdecc7733f4ba80
  languageName: node
  linkType: hard

"jest-watcher@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-watcher@npm:29.4.3"
  dependencies:
    "@jest/test-result": ^29.4.3
    "@jest/types": ^29.4.3
    "@types/node": "*"
    ansi-escapes: ^4.2.1
    chalk: ^4.0.0
    emittery: ^0.13.1
    jest-util: ^29.4.3
    string-length: ^4.0.1
  checksum: 44b64991b3414db853c3756f14690028f4edef7aebfb204a4291cc1901c2239fa27a8687c5c5abbecc74bf613e0bb9b1378bf766430c9febcc71e9c0cb5ad8fc
  languageName: node
  linkType: hard

"jest-worker@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-worker@npm:29.4.3"
  dependencies:
    "@types/node": "*"
    jest-util: ^29.4.3
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: c99ae66f257564613e72c5797c3a68f21a22e1c1fb5f30d14695ff5b508a0d2405f22748f13a3df8d1015b5e16abb130170f81f047ff68f58b6b1d2ff6ebc51b
  languageName: node
  linkType: hard

"jest@npm:^29.4.1":
  version: 29.4.3
  resolution: "jest@npm:29.4.3"
  dependencies:
    "@jest/core": ^29.4.3
    "@jest/types": ^29.4.3
    import-local: ^3.0.2
    jest-cli: ^29.4.3
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 084d10d1ceaade3c40e6d3bbd71b9b71b8919ba6fbd6f1f6699bdc259a6ba2f7350c7ccbfa10c11f7e3e01662853650a6244210179542fe4ba87e77dc3f3109f
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: ^1.0.7
    esprima: ^4.0.0
  bin:
    js-yaml: bin/js-yaml.js
  checksum: bef146085f472d44dee30ec34e5cf36bf89164f5d585435a3d3da89e52622dff0b188a580e4ad091c3341889e14cb88cac6e4deb16dc5b1e9623bb0601fc255c
  languageName: node
  linkType: hard

"jsesc@npm:^2.5.1":
  version: 2.5.2
  resolution: "jsesc@npm:2.5.2"
  bin:
    jsesc: bin/jsesc
  checksum: 4dc190771129e12023f729ce20e1e0bfceac84d73a85bc3119f7f938843fe25a4aeccb54b6494dce26fcf263d815f5f31acdefac7cc9329efb8422a4f4d9fa9d
  languageName: node
  linkType: hard

"jsesc@npm:~0.5.0":
  version: 0.5.0
  resolution: "jsesc@npm:0.5.0"
  bin:
    jsesc: bin/jsesc
  checksum: b8b44cbfc92f198ad972fba706ee6a1dfa7485321ee8c0b25f5cedd538dcb20cde3197de16a7265430fce8277a12db066219369e3d51055038946039f6e20e17
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json5@npm:^2.2.2":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsonparse@npm:^1.2.0":
  version: 1.3.1
  resolution: "jsonparse@npm:1.3.1"
  checksum: 6514a7be4674ebf407afca0eda3ba284b69b07f9958a8d3113ef1005f7ec610860c312be067e450c569aab8b89635e332cee3696789c750692bb60daba627f4d
  languageName: node
  linkType: hard

"keccak@npm:^3.0.0":
  version: 3.0.3
  resolution: "keccak@npm:3.0.3"
  dependencies:
    node-addon-api: ^2.0.0
    node-gyp: latest
    node-gyp-build: ^4.2.0
    readable-stream: ^3.6.0
  checksum: f08f04f5cc87013a3fc9e87262f761daff38945c86dd09c01a7f7930a15ae3e14f93b310ef821dcc83675a7b814eb1c983222399a2f263ad980251201d1b9a99
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: df82cd1e172f957bae9c536286265a5cdbd5eeca487cb0a3b2a7b41ef959fc61f8e7c0e9aeea9c114ccf2c166b6a8dd45a46fd619c1c569d210ecd2765ad5169
  languageName: node
  linkType: hard

"labeled-stream-splicer@npm:^2.0.0":
  version: 2.0.2
  resolution: "labeled-stream-splicer@npm:2.0.2"
  dependencies:
    inherits: ^2.0.1
    stream-splicer: ^2.0.0
  checksum: 4f7097b7666cd6d110f2a700f2905f703aa2a6d21c76fb390fcf441f436b269f5b1ad813178af4406cf6ddf01f3ac24435b3ff8fe2d9678664c147bf92f056b3
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 638401d534585261b6003db9d99afd244dfe82d75ddb6db5c0df412842d5ab30b2ef18de471aaec70fe69a46f17b4ae3c7f01d8a4e6580ef7adb9f4273ad1e55
  languageName: node
  linkType: hard

"levn@npm:~0.3.0":
  version: 0.3.0
  resolution: "levn@npm:0.3.0"
  dependencies:
    prelude-ls: ~1.1.2
    type-check: ~0.3.2
  checksum: 0d084a524231a8246bb10fec48cdbb35282099f6954838604f3c7fc66f2e16fa66fd9cc2f3f20a541a113c4dafdf181e822c887c8a319c9195444e6c64ac395e
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: a3f527d22c548f43ae31c861ada88b2637eb48ac6aa3eb56e82d44917971b8aa96fbb37aa60efea674dc4ee8c42074f90f7b1f772e9db375435f6c83a19b3bc6
  languageName: node
  linkType: hard

"lodash.memoize@npm:~3.0.3":
  version: 3.0.4
  resolution: "lodash.memoize@npm:3.0.4"
  checksum: fc52e0916b896fa79d6b85fbeaa0e44a381b70f1fcab7acab10188aaeeb2107e21b9b992bff560f405696e0a6e3bb5c08af18955d628a1e8ab6b11df14ff6172
  languageName: node
  linkType: hard

"lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"loose-envify@npm:^1.1.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: f97f499f898f23e4585742138a22f22526254fdba6d75d41a1c2526b3b6cc5747ef59c5612ba7375f42aca4f8461950e925ba08c991ead0651b4918b7c978297
  languageName: node
  linkType: hard

"lru-cache@npm:^7.7.1":
  version: 7.14.1
  resolution: "lru-cache@npm:7.14.1"
  checksum: d72c6713c6a6d86836a7a6523b3f1ac6764768cca47ec99341c3e76db06aacd4764620e5e2cda719a36848785a52a70e531822dc2b33fb071fa709683746c104
  languageName: node
  linkType: hard

"magic-string@npm:^0.22.4":
  version: 0.22.5
  resolution: "magic-string@npm:0.22.5"
  dependencies:
    vlq: ^0.2.2
  checksum: e4d8b2083ed81f139ca471c19f5d0505c0f9083e06b71f48835057b3295a9494a15aac6a111d335573b986a67d3932e41c726d2eb6c76af30d0391b5e254ced3
  languageName: node
  linkType: hard

"make-dir@npm:^3.0.0":
  version: 3.1.0
  resolution: "make-dir@npm:3.1.0"
  dependencies:
    semver: ^6.0.0
  checksum: 484200020ab5a1fdf12f393fe5f385fc8e4378824c940fba1729dcd198ae4ff24867bc7a5646331e50cead8abff5d9270c456314386e629acec6dff4b8016b78
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^10.0.3":
  version: 10.2.1
  resolution: "make-fetch-happen@npm:10.2.1"
  dependencies:
    agentkeepalive: ^4.2.1
    cacache: ^16.1.0
    http-cache-semantics: ^4.1.0
    http-proxy-agent: ^5.0.0
    https-proxy-agent: ^5.0.0
    is-lambda: ^1.0.1
    lru-cache: ^7.7.1
    minipass: ^3.1.6
    minipass-collect: ^1.0.2
    minipass-fetch: ^2.0.3
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.3
    promise-retry: ^2.0.1
    socks-proxy-agent: ^7.0.0
    ssri: ^9.0.0
  checksum: 2332eb9a8ec96f1ffeeea56ccefabcb4193693597b132cd110734d50f2928842e22b84cfa1508e921b8385cdfd06dda9ad68645fed62b50fff629a580f5fb72c
  languageName: node
  linkType: hard

"makeerror@npm:1.0.12":
  version: 1.0.12
  resolution: "makeerror@npm:1.0.12"
  dependencies:
    tmpl: 1.0.5
  checksum: b38a025a12c8146d6eeea5a7f2bf27d51d8ad6064da8ca9405fcf7bf9b54acd43e3b30ddd7abb9b1bfa4ddb266019133313482570ddb207de568f71ecfcf6060
  languageName: node
  linkType: hard

"md5.js@npm:^1.3.4":
  version: 1.3.5
  resolution: "md5.js@npm:1.3.5"
  dependencies:
    hash-base: ^3.0.0
    inherits: ^2.0.1
    safe-buffer: ^5.1.2
  checksum: 098494d885684bcc4f92294b18ba61b7bd353c23147fbc4688c75b45cb8590f5a95fd4584d742415dcc52487f7a1ef6ea611cfa1543b0dc4492fe026357f3f0c
  languageName: node
  linkType: hard

"merge-source-map@npm:1.0.4":
  version: 1.0.4
  resolution: "merge-source-map@npm:1.0.4"
  dependencies:
    source-map: ^0.5.6
  checksum: 86a4e60d83980393e61f069c7ae33e7899c4c012c3cd2cf50e01482e7a284bbe9c8cd08d37adbf241fc9eacfa4425241432e7461cf6559f7e9902587889660de
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"micro-ftch@npm:^0.3.1":
  version: 0.3.1
  resolution: "micro-ftch@npm:0.3.1"
  checksum: 0e496547253a36e98a83fb00c628c53c3fb540fa5aaeaf718438873785afd193244988c09d219bb1802984ff227d04938d9571ef90fe82b48bd282262586aaff
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 79920eb634e6f400b464a954fcfa589c4e7c7143209488e44baf627f9affc8b1e306f41f4f0deedde97e69cb725920879462d3e750ab3bd3c1aed675bb3a8966
  languageName: node
  linkType: hard

"miller-rabin@npm:^4.0.0":
  version: 4.0.1
  resolution: "miller-rabin@npm:4.0.1"
  dependencies:
    bn.js: ^4.0.0
    brorand: ^1.0.1
  bin:
    miller-rabin: bin/miller-rabin
  checksum: 00cd1ab838ac49b03f236cc32a14d29d7d28637a53096bf5c6246a032a37749c9bd9ce7360cbf55b41b89b7d649824949ff12bc8eee29ac77c6b38eada619ece
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0, minimalistic-assert@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: cc7974a9268fbf130fb055aff76700d7e2d8be5f761fb5c60318d0ed010d839ab3661a533ad29a5d37653133385204c503bfac995aaa4236f4e847461ea32ba7
  languageName: node
  linkType: hard

"minimalistic-crypto-utils@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-crypto-utils@npm:1.0.1"
  checksum: 6e8a0422b30039406efd4c440829ea8f988845db02a3299f372fceba56ffa94994a9c0f2fd70c17f9969eedfbd72f34b5070ead9656a34d3f71c0bd72583a0ed
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.1.1":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 7564208ef81d7065a370f788d337cd80a689e981042cb9a1d0e6580b6c6a8c9279eba80010516e258835a988363f99f54a6f711a315089b8b42694f5da9d0d77
  languageName: node
  linkType: hard

"minimist@npm:^1.1.0, minimist@npm:^1.1.3, minimist@npm:^1.2.6":
  version: 1.2.7
  resolution: "minimist@npm:1.2.7"
  checksum: 7346574a1038ca23c32e02252f603801f09384dd1d78b69a943a4e8c2c28730b80e96193882d3d3b22a063445f460e48316b29b8a25addca2d7e5e8f75478bec
  languageName: node
  linkType: hard

"minipass-collect@npm:^1.0.2":
  version: 1.0.2
  resolution: "minipass-collect@npm:1.0.2"
  dependencies:
    minipass: ^3.0.0
  checksum: 14df761028f3e47293aee72888f2657695ec66bd7d09cae7ad558da30415fdc4752bbfee66287dcc6fd5e6a2fa3466d6c484dc1cbd986525d9393b9523d97f10
  languageName: node
  linkType: hard

"minipass-fetch@npm:^2.0.3":
  version: 2.1.2
  resolution: "minipass-fetch@npm:2.1.2"
  dependencies:
    encoding: ^0.1.13
    minipass: ^3.1.6
    minipass-sized: ^1.0.3
    minizlib: ^2.1.2
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 3f216be79164e915fc91210cea1850e488793c740534985da017a4cbc7a5ff50506956d0f73bb0cb60e4fe91be08b6b61ef35101706d3ef5da2c8709b5f08f91
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0, minipass@npm:^3.1.1, minipass@npm:^3.1.6":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 425dab288738853fded43da3314a0b5c035844d6f3097a8e3b5b29b328da8f3c1af6fc70618b32c29ff906284cf6406b6841376f21caaadd0793c1d5a6a620ea
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: ^3.0.0
    yallist: ^4.0.0
  checksum: f1fdeac0b07cf8f30fcf12f4b586795b97be856edea22b5e9072707be51fc95d41487faec3f265b42973a304fe3a64acd91a44a3826a963e37b37bafde0212c3
  languageName: node
  linkType: hard

"mkdirp-classic@npm:^0.5.2":
  version: 0.5.3
  resolution: "mkdirp-classic@npm:0.5.3"
  checksum: 3f4e088208270bbcc148d53b73e9a5bd9eef05ad2cbf3b3d0ff8795278d50dd1d11a8ef1875ff5aea3fa888931f95bfcb2ad5b7c1061cfefd6284d199e6776ac
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3, mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"module-deps@npm:^6.2.3":
  version: 6.2.3
  resolution: "module-deps@npm:6.2.3"
  dependencies:
    JSONStream: ^1.0.3
    browser-resolve: ^2.0.0
    cached-path-relative: ^1.0.2
    concat-stream: ~1.6.0
    defined: ^1.0.0
    detective: ^5.2.0
    duplexer2: ^0.1.2
    inherits: ^2.0.1
    parents: ^1.0.0
    readable-stream: ^2.0.2
    resolve: ^1.4.0
    stream-combiner2: ^1.1.1
    subarg: ^1.0.0
    through2: ^2.0.0
    xtend: ^4.0.0
  bin:
    module-deps: bin/cmd.js
  checksum: cccead8f81b77ec621c29c4407978ce50de6f15c7152b54e81b65ff043d4254fd40071e53a3989a36066ff0d3ce9ae9e65f81aed79b3b5397024dbc8be5d68c7
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 673cdb2c3133eb050c745908d8ce632ed2c02d85640e2edb3ace856a2266a813b30c613569bf3354fdf4ea7d1a1494add3bfa95e2713baa27d0c2c71fc44f58f
  languageName: node
  linkType: hard

"ms@npm:^2.0.0":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"mute-stream@npm:^0.0.8":
  version: 0.0.8
  resolution: "mute-stream@npm:0.0.8"
  checksum: ff48d251fc3f827e5b1206cda0ffdaec885e56057ee86a3155e1951bc940fd5f33531774b1cc8414d7668c10a8907f863f6561875ee6e8768931a62121a531a1
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:^0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: b8ffeb1e262eff7968fc90a2b6767b04cfd9842582a9d0ece0af7049537266e7b2506dfb1d107a32f06dd849ab2aea834d5830f7f4d0e5cb7d36e1ae55d021d9
  languageName: node
  linkType: hard

"node-addon-api@npm:^2.0.0":
  version: 2.0.2
  resolution: "node-addon-api@npm:2.0.2"
  dependencies:
    node-gyp: latest
  checksum: 31fb22d674648204f8dd94167eb5aac896c841b84a9210d614bf5d97c74ef059cc6326389cf0c54d2086e35312938401d4cc82e5fcd679202503eb8ac84814f8
  languageName: node
  linkType: hard

"node-addon-api@npm:^5.0.0":
  version: 5.1.0
  resolution: "node-addon-api@npm:5.1.0"
  dependencies:
    node-gyp: latest
  checksum: 2508bd2d2981945406243a7bd31362fc7af8b70b8b4d65f869c61731800058fb818cc2fd36c8eac714ddd0e568cc85becf5e165cebbdf7b5024d5151bbc75ea1
  languageName: node
  linkType: hard

"node-gyp-build@npm:^4.2.0":
  version: 4.6.0
  resolution: "node-gyp-build@npm:4.6.0"
  bin:
    node-gyp-build: bin.js
    node-gyp-build-optional: optional.js
    node-gyp-build-test: build-test.js
  checksum: 25d78c5ef1f8c24291f4a370c47ba52fcea14f39272041a90a7894cd50d766f7c8cb8fb06c0f42bf6f69b204b49d9be3c8fc344aac09714d5bdb95965499eb15
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 9.3.1
  resolution: "node-gyp@npm:9.3.1"
  dependencies:
    env-paths: ^2.2.0
    glob: ^7.1.4
    graceful-fs: ^4.2.6
    make-fetch-happen: ^10.0.3
    nopt: ^6.0.0
    npmlog: ^6.0.0
    rimraf: ^3.0.2
    semver: ^7.3.5
    tar: ^6.1.2
    which: ^2.0.2
  bin:
    node-gyp: bin/node-gyp.js
  checksum: b860e9976fa645ca0789c69e25387401b4396b93c8375489b5151a6c55cf2640a3b6183c212b38625ef7c508994930b72198338e3d09b9d7ade5acc4aaf51ea7
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: d0b30b1ee6d961851c60d5eaa745d30b5c95d94bc0e74b81e5292f7c42a49e3af87f1eb9e89f59456f80645d679202537de751b7d72e9e40ceea40c5e449057e
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.8":
  version: 2.0.10
  resolution: "node-releases@npm:2.0.10"
  checksum: d784ecde25696a15d449c4433077f5cce620ed30a1656c4abf31282bfc691a70d9618bae6868d247a67914d1be5cc4fde22f65a05f4398cdfb92e0fc83cadfbc
  languageName: node
  linkType: hard

"nopt@npm:^6.0.0":
  version: 6.0.0
  resolution: "nopt@npm:6.0.0"
  dependencies:
    abbrev: ^1.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 82149371f8be0c4b9ec2f863cc6509a7fd0fa729929c009f3a58e4eb0c9e4cae9920e8f1f8eb46e7d032fec8fb01bede7f0f41a67eb3553b7b8e14fa53de1dac
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: ^3.0.0
  checksum: 5374c0cea4b0bbfdfae62da7bbdf1e1558d338335f4cacf2515c282ff358ff27b2ecb91ffa5330a8b14390ac66a1e146e10700440c1ab868208430f56b5f4d23
  languageName: node
  linkType: hard

"npmlog@npm:^6.0.0":
  version: 6.0.2
  resolution: "npmlog@npm:6.0.2"
  dependencies:
    are-we-there-yet: ^3.0.0
    console-control-strings: ^1.1.0
    gauge: ^4.0.3
    set-blocking: ^2.0.0
  checksum: ae238cd264a1c3f22091cdd9e2b106f684297d3c184f1146984ecbe18aaa86343953f26b9520dedd1b1372bc0316905b736c1932d778dbeb1fcf5a1001390e2a
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:~1.4.0":
  version: 1.4.1
  resolution: "object-inspect@npm:1.4.1"
  checksum: dd92deb565dc93471e395a9c7510f988961e5c292dcc7eda3bd3160a98e069b0769bb72b11882173fbaee131902be1f3aa3a4764a791c5895610acc2dc992b0b
  languageName: node
  linkType: hard

"once@npm:^1.3.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: 2478859ef817fc5d4e9c2f9e5728512ddd1dbc9fb7829ad263765bb6d3b91ce699d6e2332eef6b7dff183c2f490bd3349f1666427eaba4469fba0ac38dfd0d34
  languageName: node
  linkType: hard

"optionator@npm:^0.8.1":
  version: 0.8.3
  resolution: "optionator@npm:0.8.3"
  dependencies:
    deep-is: ~0.1.3
    fast-levenshtein: ~2.0.6
    levn: ~0.3.0
    prelude-ls: ~1.1.2
    type-check: ~0.3.2
    word-wrap: ~1.2.3
  checksum: b8695ddf3d593203e25ab0900e265d860038486c943ff8b774f596a310f8ceebdb30c6832407a8198ba3ec9debe1abe1f51d4aad94843612db3b76d690c61d34
  languageName: node
  linkType: hard

"os-browserify@npm:~0.3.0":
  version: 0.3.0
  resolution: "os-browserify@npm:0.3.0"
  checksum: 16e37ba3c0e6a4c63443c7b55799ce4066d59104143cb637ecb9fce586d5da319cdca786ba1c867abbe3890d2cbf37953f2d51eea85e20dd6c4570d6c54bfebf
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-limit@npm:^3.1.0":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"pako@npm:~1.0.5":
  version: 1.0.11
  resolution: "pako@npm:1.0.11"
  checksum: 1be2bfa1f807608c7538afa15d6f25baa523c30ec870a3228a89579e474a4d992f4293859524e46d5d87fd30fa17c5edf34dbef0671251d9749820b488660b16
  languageName: node
  linkType: hard

"parents@npm:^1.0.0, parents@npm:^1.0.1":
  version: 1.0.1
  resolution: "parents@npm:1.0.1"
  dependencies:
    path-platform: ~0.11.15
  checksum: 094fc817d5e8d94e9f9d38c2618a2822f2960b7a268183a36326c5d1cf6ff32f97b1158b0f9b32ab126573996dfe6db104feda6d26e8531d762d178ef4488fc8
  languageName: node
  linkType: hard

"parse-asn1@npm:^5.0.0, parse-asn1@npm:^5.1.6":
  version: 5.1.6
  resolution: "parse-asn1@npm:5.1.6"
  dependencies:
    asn1.js: ^5.2.0
    browserify-aes: ^1.0.0
    evp_bytestokey: ^1.0.0
    pbkdf2: ^3.0.3
    safe-buffer: ^5.1.1
  checksum: 9243311d1f88089bc9f2158972aa38d1abd5452f7b7cabf84954ed766048fe574d434d82c6f5a39b988683e96fb84cd933071dda38927e03469dc8c8d14463c7
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"path-browserify@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-browserify@npm:1.0.1"
  checksum: c6d7fa376423fe35b95b2d67990060c3ee304fc815ff0a2dc1c6c3cfaff2bd0d572ee67e18f19d0ea3bbe32e8add2a05021132ac40509416459fffee35200699
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0, path-is-absolute@npm:^1.0.1":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-platform@npm:~0.11.15":
  version: 0.11.15
  resolution: "path-platform@npm:0.11.15"
  checksum: 239f2eae720531ff5a48837de68f94ebd7cf6cd2bf295b39beb97c5bafc34a34a683b62f9f5ad5ca5e78d71d7d44c29e7c56373c1c8473ab128a4e648bb898f0
  languageName: node
  linkType: hard

"pbkdf2@npm:^3.0.17, pbkdf2@npm:^3.0.3":
  version: 3.1.2
  resolution: "pbkdf2@npm:3.1.2"
  dependencies:
    create-hash: ^1.1.2
    create-hmac: ^1.1.4
    ripemd160: ^2.0.1
    safe-buffer: ^5.0.1
    sha.js: ^2.4.8
  checksum: 2c950a100b1da72123449208e231afc188d980177d021d7121e96a2de7f2abbc96ead2b87d03d8fe5c318face097f203270d7e27908af9f471c165a4e8e69c92
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: a2e8092dd86c8396bdba9f2b5481032848525b3dc295ce9b57896f931e63fc16f79805144321f72976383fc249584672a75cc18d6777c6b757603f372f745981
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.3, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"pirates@npm:^4.0.4":
  version: 4.0.5
  resolution: "pirates@npm:4.0.5"
  checksum: c9994e61b85260bec6c4fc0307016340d9b0c4f4b6550a957afaaff0c9b1ad58fbbea5cfcf083860a25cb27a375442e2b0edf52e2e1e40e69934e08dcc52d227
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: ^4.0.0
  checksum: 9863e3f35132bf99ae1636d31ff1e1e3501251d480336edb1c211133c8d58906bed80f154a1d723652df1fda91e01c7442c2eeaf9dc83157c7ae89087e43c8d6
  languageName: node
  linkType: hard

"pony-cause@npm:^2.1.10":
  version: 2.1.10
  resolution: "pony-cause@npm:2.1.10"
  checksum: 8b61378f213e61056312dc274a1c79980154e9d864f6ad86e0c8b91a50d3ce900d430995ee24147c9f3caa440dfe7d51c274b488d7f033b65b206522536d7217
  languageName: node
  linkType: hard

"prelude-ls@npm:~1.1.2":
  version: 1.1.2
  resolution: "prelude-ls@npm:1.1.2"
  checksum: c4867c87488e4a0c233e158e4d0d5565b609b105d75e4c05dc760840475f06b731332eb93cc8c9cecb840aa8ec323ca3c9a56ad7820ad2e63f0261dadcb154e4
  languageName: node
  linkType: hard

"pretty-format@npm:^29.4.3":
  version: 29.4.3
  resolution: "pretty-format@npm:29.4.3"
  dependencies:
    "@jest/schemas": ^29.4.3
    ansi-styles: ^5.0.0
    react-is: ^18.0.0
  checksum: 3258b9a010bd79b3cf73783ad1e4592b6326fc981b6e31b742f316f14e7fbac09b48a9dbf274d092d9bde404db9fe16f518370e121837dc078a597392e6e5cc5
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"process@npm:~0.11.0":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: bfcce49814f7d172a6e6a14d5fa3ac92cc3d0c3b9feb1279774708a719e19acd673995226351a082a9ae99978254e320ccda4240ddc474ba31a76c79491ca7c3
  languageName: node
  linkType: hard

"promise-inflight@npm:^1.0.1":
  version: 1.0.1
  resolution: "promise-inflight@npm:1.0.1"
  checksum: 22749483091d2c594261517f4f80e05226d4d5ecc1fc917e1886929da56e22b5718b7f2a75f3807e7a7d471bc3be2907fe92e6e8f373ddf5c64bae35b5af3981
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"prompts@npm:^2.0.1":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: ^3.0.3
    sisteransi: ^1.0.5
  checksum: d8fd1fe63820be2412c13bfc5d0a01909acc1f0367e32396962e737cb2fc52d004f3302475d5ce7d18a1e8a79985f93ff04ee03007d091029c3f9104bffc007d
  languageName: node
  linkType: hard

"public-encrypt@npm:^4.0.0":
  version: 4.0.3
  resolution: "public-encrypt@npm:4.0.3"
  dependencies:
    bn.js: ^4.1.0
    browserify-rsa: ^4.0.0
    create-hash: ^1.1.0
    parse-asn1: ^5.0.0
    randombytes: ^2.0.1
    safe-buffer: ^5.1.2
  checksum: 215d446e43cef021a20b67c1df455e5eea134af0b1f9b8a35f9e850abf32991b0c307327bc5b9bc07162c288d5cdb3d4a783ea6c6640979ed7b5017e3e0c9935
  languageName: node
  linkType: hard

"punycode@npm:1.3.2":
  version: 1.3.2
  resolution: "punycode@npm:1.3.2"
  checksum: b8807fd594b1db33335692d1f03e8beeddde6fda7fbb4a2e32925d88d20a3aa4cd8dcc0c109ccaccbd2ba761c208dfaaada83007087ea8bfb0129c9ef1b99ed6
  languageName: node
  linkType: hard

"punycode@npm:^1.3.2":
  version: 1.4.1
  resolution: "punycode@npm:1.4.1"
  checksum: fa6e698cb53db45e4628559e557ddaf554103d2a96a1d62892c8f4032cd3bc8871796cae9eabc1bc700e2b6677611521ce5bb1d9a27700086039965d0cf34518
  languageName: node
  linkType: hard

"querystring-es3@npm:~0.2.0":
  version: 0.2.1
  resolution: "querystring-es3@npm:0.2.1"
  checksum: 691e8d6b8b157e7cd49ae8e83fcf86de39ab3ba948c25abaa94fba84c0986c641aa2f597770848c64abce290ed17a39c9df6df737dfa7e87c3b63acc7d225d61
  languageName: node
  linkType: hard

"querystring@npm:0.2.0":
  version: 0.2.0
  resolution: "querystring@npm:0.2.0"
  checksum: 8258d6734f19be27e93f601758858c299bdebe71147909e367101ba459b95446fbe5b975bf9beb76390156a592b6f4ac3a68b6087cea165c259705b8b4e56a69
  languageName: node
  linkType: hard

"quote-stream@npm:^1.0.1, quote-stream@npm:~1.0.2":
  version: 1.0.2
  resolution: "quote-stream@npm:1.0.2"
  dependencies:
    buffer-equal: 0.0.1
    minimist: ^1.1.3
    through2: ^2.0.0
  bin:
    quote-stream: bin/cmd.js
  checksum: 73a45ad41000eb23579ecf57d8e3a19795716e988d9279dc4de412f4cf90dd0c5dba792d8d0e65f6c627d3da4e7781104b41af44975614a0f5c94f666bf86468
  languageName: node
  linkType: hard

"randombytes@npm:^2.0.0, randombytes@npm:^2.0.1, randombytes@npm:^2.0.5, randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: ^5.1.0
  checksum: d779499376bd4cbb435ef3ab9a957006c8682f343f14089ed5f27764e4645114196e75b7f6abf1cbd84fd247c0cb0651698444df8c9bf30e62120fbbc52269d6
  languageName: node
  linkType: hard

"randomfill@npm:^1.0.3":
  version: 1.0.4
  resolution: "randomfill@npm:1.0.4"
  dependencies:
    randombytes: ^2.0.5
    safe-buffer: ^5.1.0
  checksum: 33734bb578a868d29ee1b8555e21a36711db084065d94e019a6d03caa67debef8d6a1bfd06a2b597e32901ddc761ab483a85393f0d9a75838f1912461d4dbfc7
  languageName: node
  linkType: hard

"react-dom@npm:^18.0.0":
  version: 18.2.0
  resolution: "react-dom@npm:18.2.0"
  dependencies:
    loose-envify: ^1.1.0
    scheduler: ^0.23.0
  peerDependencies:
    react: ^18.2.0
  checksum: 7d323310bea3a91be2965f9468d552f201b1c27891e45ddc2d6b8f717680c95a75ae0bc1e3f5cf41472446a2589a75aed4483aee8169287909fcd59ad149e8cc
  languageName: node
  linkType: hard

"react-hyperscript@npm:^3.2.0":
  version: 3.2.0
  resolution: "react-hyperscript@npm:3.2.0"
  peerDependencies:
    react: ">= 0.12.0 < 17.0.0"
  checksum: 90ca70d6a0aa90769db7c92140421746be70c1757bd735e56c2d5f16549de93201d8204d480eeb499f4dcbdd335028ba32dbc7340ab333ae9fd459d3f49564c6
  languageName: node
  linkType: hard

"react-is@npm:^16.7.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0":
  version: 18.2.0
  resolution: "react-is@npm:18.2.0"
  checksum: e72d0ba81b5922759e4aff17e0252bd29988f9642ed817f56b25a3e217e13eea8a7f2322af99a06edb779da12d5d636e9fda473d620df9a3da0df2a74141d53e
  languageName: node
  linkType: hard

"react-redux@npm:^8.0.1":
  version: 8.0.5
  resolution: "react-redux@npm:8.0.5"
  dependencies:
    "@babel/runtime": ^7.12.1
    "@types/hoist-non-react-statics": ^3.3.1
    "@types/use-sync-external-store": ^0.0.3
    hoist-non-react-statics: ^3.3.2
    react-is: ^18.0.0
    use-sync-external-store: ^1.0.0
  peerDependencies:
    "@types/react": ^16.8 || ^17.0 || ^18.0
    "@types/react-dom": ^16.8 || ^17.0 || ^18.0
    react: ^16.8 || ^17.0 || ^18.0
    react-dom: ^16.8 || ^17.0 || ^18.0
    react-native: ">=0.59"
    redux: ^4
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
    react-dom:
      optional: true
    react-native:
      optional: true
    redux:
      optional: true
  checksum: a108f4f7ead6ac005e656d46051474a2bbdb31ede481bbbb3d8d779c1a35e1940b8655577cc5021313411864d305f67fc719aa48d6e5ed8288cf9cbe8b7042e4
  languageName: node
  linkType: hard

"react@npm:^18.0.0":
  version: 18.2.0
  resolution: "react@npm:18.2.0"
  dependencies:
    loose-envify: ^1.1.0
  checksum: 88e38092da8839b830cda6feef2e8505dec8ace60579e46aa5490fc3dc9bba0bd50336507dc166f43e3afc1c42939c09fe33b25fae889d6f402721dcd78fca1b
  languageName: node
  linkType: hard

"read-only-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "read-only-stream@npm:2.0.0"
  dependencies:
    readable-stream: ^2.0.2
  checksum: aa48979d1f0e8a83522e60698cf3375dca7b284dd066758ded7c3539613ac08275f94dfe0503d2bdfe964ef3cb65facb87a4b3a8250e5a7e89d07af4451019d8
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.2, readable-stream@npm:^2.2.2, readable-stream@npm:~2.3.3, readable-stream@npm:~2.3.6":
  version: 2.3.7
  resolution: "readable-stream@npm:2.3.7"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: e4920cf7549a60f8aaf694d483a0e61b2a878b969d224f89b3bc788b8d920075132c4b55a7494ee944c7b6a9a0eada28a7f6220d80b0312ece70bbf08eeca755
  languageName: node
  linkType: hard

"readable-stream@npm:^3.5.0, readable-stream@npm:^3.6.0, readable-stream@npm:^3.6.2":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"redux-logger@npm:^3.0.6":
  version: 3.0.6
  resolution: "redux-logger@npm:3.0.6"
  dependencies:
    deep-diff: ^0.3.5
  checksum: c40f63c44c6475cf6374ae0eaa810d913f142614cb80692a0beacaf135c5dc3eb3e2cdd4296f01446ba48cb69b82e81363b84d829f1f6659382c991022a814ac
  languageName: node
  linkType: hard

"redux-thunk@npm:^2.4.1":
  version: 2.4.2
  resolution: "redux-thunk@npm:2.4.2"
  peerDependencies:
    redux: ^4
  checksum: c7f757f6c383b8ec26152c113e20087818d18ed3edf438aaad43539e9a6b77b427ade755c9595c4a163b6ad3063adf3497e5fe6a36c68884eb1f1cfb6f049a5c
  languageName: node
  linkType: hard

"redux@npm:^4.2.0":
  version: 4.2.1
  resolution: "redux@npm:4.2.1"
  dependencies:
    "@babel/runtime": ^7.9.2
  checksum: f63b9060c3a1d930ae775252bb6e579b42415aee7a23c4114e21a0b4ba7ec12f0ec76936c00f546893f06e139819f0e2855e0d55ebfce34ca9c026241a6950dd
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.1.0":
  version: 10.1.0
  resolution: "regenerate-unicode-properties@npm:10.1.0"
  dependencies:
    regenerate: ^1.4.2
  checksum: b1a8929588433ab8b9dc1a34cf3665b3b472f79f2af6ceae00d905fc496b332b9af09c6718fb28c730918f19a00dc1d7310adbaa9b72a2ec7ad2f435da8ace17
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 3317a09b2f802da8db09aa276e469b57a6c0dd818347e05b8862959c6193408242f150db5de83c12c3fa99091ad95fb42a6db2c3329bfaa12a0ea4cbbeb30cb0
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.11":
  version: 0.13.11
  resolution: "regenerator-runtime@npm:0.13.11"
  checksum: 27481628d22a1c4e3ff551096a683b424242a216fee44685467307f14d58020af1e19660bf2e26064de946bad7eff28950eae9f8209d55723e2d9351e632bbb4
  languageName: node
  linkType: hard

"regenerator-transform@npm:^0.15.1":
  version: 0.15.1
  resolution: "regenerator-transform@npm:0.15.1"
  dependencies:
    "@babel/runtime": ^7.8.4
  checksum: 2d15bdeadbbfb1d12c93f5775493d85874dbe1d405bec323da5c61ec6e701bc9eea36167483e1a5e752de9b2df59ab9a2dfff6bf3784f2b28af2279a673d29a4
  languageName: node
  linkType: hard

"regexpu-core@npm:^5.2.1":
  version: 5.3.0
  resolution: "regexpu-core@npm:5.3.0"
  dependencies:
    "@babel/regjsgen": ^0.8.0
    regenerate: ^1.4.2
    regenerate-unicode-properties: ^10.1.0
    regjsparser: ^0.9.1
    unicode-match-property-ecmascript: ^2.0.0
    unicode-match-property-value-ecmascript: ^2.1.0
  checksum: f3c7921543ebda919c53fdbbf3a9cebbecbf8ad65b30e423d7eaef35484e08cbc919f9e8334f4693a72206f583d4f2b48d4415483f6e6e8c81f0046e3a23c66f
  languageName: node
  linkType: hard

"regjsparser@npm:^0.9.1":
  version: 0.9.1
  resolution: "regjsparser@npm:0.9.1"
  dependencies:
    jsesc: ~0.5.0
  bin:
    regjsparser: bin/parser
  checksum: 5e1b76afe8f1d03c3beaf9e0d935dd467589c3625f6d65fb8ffa14f224d783a0fed4bf49c2c1b8211043ef92b6117313419edf055a098ed8342e340586741afc
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: ^5.0.0
  checksum: 546e0816012d65778e580ad62b29e975a642989108d9a3c5beabfb2304192fa3c9f9146fbdfe213563c6ff51975ae41bac1d3c6e047dd9572c94863a057b4d81
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 4ceeb9113e1b1372d0cd969f3468fa042daa1dd9527b1b6bb88acb6ab55d8b9cd65dbf18819f9f9ddf0db804990901dcdaade80a215e7b2c23daae38e64f5bdf
  languageName: node
  linkType: hard

"resolve.exports@npm:^2.0.0":
  version: 2.0.0
  resolution: "resolve.exports@npm:2.0.0"
  checksum: d8bee3b0cc0a0ae6c8323710983505bc6a3a2574f718e96f01e048a0f0af035941434b386cc9efc7eededc5e1199726185c306ec6f6a1aa55d5fbad926fd0634
  languageName: node
  linkType: hard

"resolve@npm:^1.1.4, resolve@npm:^1.1.5, resolve@npm:^1.14.2, resolve@npm:^1.17.0, resolve@npm:^1.20.0, resolve@npm:^1.4.0":
  version: 1.22.1
  resolution: "resolve@npm:1.22.1"
  dependencies:
    is-core-module: ^2.9.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 07af5fc1e81aa1d866cbc9e9460fbb67318a10fa3c4deadc35c3ad8a898ee9a71a86a65e4755ac3195e0ea0cfbe201eb323ebe655ce90526fd61917313a34e4e
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.1.4#~builtin<compat/resolve>, resolve@patch:resolve@^1.1.5#~builtin<compat/resolve>, resolve@patch:resolve@^1.14.2#~builtin<compat/resolve>, resolve@patch:resolve@^1.17.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.20.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.4.0#~builtin<compat/resolve>":
  version: 1.22.1
  resolution: "resolve@patch:resolve@npm%3A1.22.1#~builtin<compat/resolve>::version=1.22.1&hash=c3c19d"
  dependencies:
    is-core-module: ^2.9.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 5656f4d0bedcf8eb52685c1abdf8fbe73a1603bb1160a24d716e27a57f6cecbe2432ff9c89c2bd57542c3a7b9d14b1882b73bfe2e9d7849c9a4c0b8b39f02b8b
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"ripemd160@npm:^2.0.0, ripemd160@npm:^2.0.1":
  version: 2.0.2
  resolution: "ripemd160@npm:2.0.2"
  dependencies:
    hash-base: ^3.0.0
    inherits: ^2.0.1
  checksum: 006accc40578ee2beae382757c4ce2908a826b27e2b079efdcd2959ee544ddf210b7b5d7d5e80467807604244e7388427330f5c6d4cd61e6edaddc5773ccc393
  languageName: node
  linkType: hard

"rlp@npm:^2.2.4":
  version: 2.2.7
  resolution: "rlp@npm:2.2.7"
  dependencies:
    bn.js: ^5.2.0
  bin:
    rlp: bin/rlp
  checksum: 3db4dfe5c793f40ac7e0be689a1f75d05e6f2ca0c66189aeb62adab8c436b857ab4420a419251ee60370d41d957a55698fc5e23ab1e1b41715f33217bc4bb558
  languageName: node
  linkType: hard

"run-async@npm:^2.3.0":
  version: 2.4.1
  resolution: "run-async@npm:2.4.1"
  checksum: a2c88aa15df176f091a2878eb840e68d0bdee319d8d97bbb89112223259cebecb94bc0defd735662b83c2f7a30bed8cddb7d1674eb48ae7322dc602b22d03797
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:^5.1.1, safe-buffer@npm:^5.1.2, safe-buffer@npm:^5.2.0, safe-buffer@npm:^5.2.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0, safer-buffer@npm:^2.1.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.0":
  version: 0.23.0
  resolution: "scheduler@npm:0.23.0"
  dependencies:
    loose-envify: ^1.1.0
  checksum: d79192eeaa12abef860c195ea45d37cbf2bbf5f66e3c4dcd16f54a7da53b17788a70d109ee3d3dde1a0fd50e6a8fc171f4300356c5aee4fc0171de526bf35f8a
  languageName: node
  linkType: hard

"scrypt-js@npm:^3.0.0":
  version: 3.0.1
  resolution: "scrypt-js@npm:3.0.1"
  checksum: b7c7d1a68d6ca946f2fbb0778e0c4ec63c65501b54023b2af7d7e9f48fdb6c6580d6f7675cd53bda5944c5ebc057560d5a6365079752546865defb3b79dea454
  languageName: node
  linkType: hard

"secp256k1@npm:^4.0.1":
  version: 4.0.4
  resolution: "secp256k1@npm:4.0.4"
  dependencies:
    elliptic: ^6.5.7
    node-addon-api: ^5.0.0
    node-gyp: latest
    node-gyp-build: ^4.2.0
  checksum: 9314ddcd27506c5f8d9b21a2c131c62464762f597b82fe48ba89b50149ec95cd566d6ad2d4a922553dd0a8b4b14c1ccd83283f487229a941b6c7c02361ef5177
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.1.1, semver@npm:^6.1.2, semver@npm:^6.3.0":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.5.4":
  version: 7.5.4
  resolution: "semver@npm:7.5.4"
  dependencies:
    lru-cache: ^6.0.0
  bin:
    semver: bin/semver.js
  checksum: 12d8ad952fa353b0995bf180cdac205a4068b759a140e5d3c608317098b3575ac2f1e09182206bf2eb26120e1c0ed8fb92c48c592f6099680de56bb071423ca3
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 6e65a05f7cf7ebdf8b7c75b101e18c0b7e3dff4940d480efed8aad3a36a4005140b660fa1d804cb8bce911cac290441dc728084a30504d3516ac2ff7ad607b02
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: c9a6f2c5b51a2dabdc0247db9c46460152ffc62ee139f3157440bd48e7c59425093f42719ac1d7931f054f153e2d26cf37dfeb8da17a794a58198a2705e527fd
  languageName: node
  linkType: hard

"sha.js@npm:^2.4.0, sha.js@npm:^2.4.8":
  version: 2.4.11
  resolution: "sha.js@npm:2.4.11"
  dependencies:
    inherits: ^2.0.1
    safe-buffer: ^5.0.1
  bin:
    sha.js: ./bin.js
  checksum: ebd3f59d4b799000699097dadb831c8e3da3eb579144fd7eb7a19484cbcbb7aca3c68ba2bb362242eb09e33217de3b4ea56e4678184c334323eca24a58e3ad07
  languageName: node
  linkType: hard

"shallow-copy@npm:~0.0.1":
  version: 0.0.1
  resolution: "shallow-copy@npm:0.0.1"
  checksum: 2d249a5a57a160b439d84fbf9ed7c0a107a3d656d1bda0b73edf9476c6e6ea9d2afa79829bf33fce6677fae35b15c14e5c28f9902dc4d07a302637a225d00634
  languageName: node
  linkType: hard

"shasum-object@npm:^1.0.0":
  version: 1.0.0
  resolution: "shasum-object@npm:1.0.0"
  dependencies:
    fast-safe-stringify: ^2.0.7
  checksum: fc3531b7ae6ca1cc76138bec54896ee61ff4e7cc62e37ebd47963c8c92f867c6232332e21437dbca60c9109e077b38ece631b59b045e10e0502949363e337895
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shell-quote@npm:^1.6.1":
  version: 1.8.0
  resolution: "shell-quote@npm:1.8.0"
  checksum: 6ef7c5e308b9c77eedded882653a132214fa98b4a1512bb507588cf6cd2fc78bfee73e945d0c3211af028a1eabe09c6a19b96edd8977dc149810797e93809749
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"simple-concat@npm:^1.0.0":
  version: 1.0.1
  resolution: "simple-concat@npm:1.0.1"
  checksum: 4d211042cc3d73a718c21ac6c4e7d7a0363e184be6a5ad25c8a1502e49df6d0a0253979e3d50dbdd3f60ef6c6c58d756b5d66ac1e05cda9cacd2e9fc59e3876a
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: aba6438f46d2bfcef94cf112c835ab395172c75f67453fe05c340c770d3c402363018ae1ab4172a1026a90c47eaccf3af7b6ff6fa749a680c2929bd7fa2b37a4
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^7.0.0":
  version: 7.0.0
  resolution: "socks-proxy-agent@npm:7.0.0"
  dependencies:
    agent-base: ^6.0.2
    debug: ^4.3.3
    socks: ^2.6.2
  checksum: 720554370154cbc979e2e9ce6a6ec6ced205d02757d8f5d93fe95adae454fc187a5cbfc6b022afab850a5ce9b4c7d73e0f98e381879cf45f66317a4895953846
  languageName: node
  linkType: hard

"socks@npm:^2.6.2":
  version: 2.7.1
  resolution: "socks@npm:2.7.1"
  dependencies:
    ip: ^2.0.0
    smart-buffer: ^4.2.0
  checksum: 259d9e3e8e1c9809a7f5c32238c3d4d2a36b39b83851d0f573bfde5f21c4b1288417ce1af06af1452569cd1eb0841169afd4998f0e04ba04656f6b7f0e46d748
  languageName: node
  linkType: hard

"source-map-support@npm:0.5.13":
  version: 0.5.13
  resolution: "source-map-support@npm:0.5.13"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 933550047b6c1a2328599a21d8b7666507427c0f5ef5eaadd56b5da0fd9505e239053c66fe181bf1df469a3b7af9d775778eee283cbb7ae16b902ddc09e93a97
  languageName: node
  linkType: hard

"source-map@npm:^0.5.6, source-map@npm:~0.5.3":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 5dc2043b93d2f194142c7f38f74a24670cd7a0063acdaf4bf01d2964b402257ae843c2a8fa822ad5b71013b5fcafa55af7421383da919752f22ff488bc553f4d
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 19d79aec211f09b99ec3099b5b2ae2f6e9cdefe50bc91ac4c69144b6d3928a640bb6ae5b3def70c2e85a2c3d9f5ec2719921e3a59d3ca3ef4b2fd1a4656a0df3
  languageName: node
  linkType: hard

"ssri@npm:^9.0.0":
  version: 9.0.1
  resolution: "ssri@npm:9.0.1"
  dependencies:
    minipass: ^3.1.1
  checksum: fb58f5e46b6923ae67b87ad5ef1c5ab6d427a17db0bead84570c2df3cd50b4ceb880ebdba2d60726588272890bae842a744e1ecce5bd2a2a582fccd5068309eb
  languageName: node
  linkType: hard

"stack-utils@npm:^2.0.3":
  version: 2.0.6
  resolution: "stack-utils@npm:2.0.6"
  dependencies:
    escape-string-regexp: ^2.0.0
  checksum: 052bf4d25bbf5f78e06c1d5e67de2e088b06871fa04107ca8d3f0e9d9263326e2942c8bedee3545795fc77d787d443a538345eef74db2f8e35db3558c6f91ff7
  languageName: node
  linkType: hard

"static-eval@npm:^2.0.0":
  version: 2.1.0
  resolution: "static-eval@npm:2.1.0"
  dependencies:
    escodegen: ^1.11.1
  checksum: 21297ee9af37cd23ef92b3a4b1fd535073539b870d2bb83a4b92f6b668183f7fb552d3c791bbdcd460c62583a2c33d46e5d56e86a7f5851b65b29e19e5d28b41
  languageName: node
  linkType: hard

"static-module@npm:^2.2.0":
  version: 2.2.5
  resolution: "static-module@npm:2.2.5"
  dependencies:
    concat-stream: ~1.6.0
    convert-source-map: ^1.5.1
    duplexer2: ~0.1.4
    escodegen: ~1.9.0
    falafel: ^2.1.0
    has: ^1.0.1
    magic-string: ^0.22.4
    merge-source-map: 1.0.4
    object-inspect: ~1.4.0
    quote-stream: ~1.0.2
    readable-stream: ~2.3.3
    shallow-copy: ~0.0.1
    static-eval: ^2.0.0
    through2: ~2.0.3
  checksum: f49dfd543f1d975f8473840c0969154d79dc1f95b4ed743c51e63633ada4aceceb145b6a58fb3c26a8891c909d6ca951762fbe24a5de359b4531813d83be4bc9
  languageName: node
  linkType: hard

"stream-browserify@npm:^3.0.0":
  version: 3.0.0
  resolution: "stream-browserify@npm:3.0.0"
  dependencies:
    inherits: ~2.0.4
    readable-stream: ^3.5.0
  checksum: 4c47ef64d6f03815a9ca3874e2319805e8e8a85f3550776c47ce523b6f4c6cd57f40e46ec6a9ab8ad260fde61863c2718f250d3bedb3fe9052444eb9abfd9921
  languageName: node
  linkType: hard

"stream-combiner2@npm:^1.1.1":
  version: 1.1.1
  resolution: "stream-combiner2@npm:1.1.1"
  dependencies:
    duplexer2: ~0.1.0
    readable-stream: ^2.0.2
  checksum: dd32d179fa8926619c65471a7396fc638ec8866616c0b8747c4e05563ccdb0b694dd4e83cd799f1c52789c965a40a88195942b82b8cea2ee7a5536f1954060f9
  languageName: node
  linkType: hard

"stream-http@npm:^3.0.0":
  version: 3.2.0
  resolution: "stream-http@npm:3.2.0"
  dependencies:
    builtin-status-codes: ^3.0.0
    inherits: ^2.0.4
    readable-stream: ^3.6.0
    xtend: ^4.0.2
  checksum: c9b78453aeb0c84fcc59555518ac62bacab9fa98e323e7b7666e5f9f58af8f3155e34481078509b02929bd1268427f664d186604cdccee95abc446099b339f83
  languageName: node
  linkType: hard

"stream-splicer@npm:^2.0.0":
  version: 2.0.1
  resolution: "stream-splicer@npm:2.0.1"
  dependencies:
    inherits: ^2.0.1
    readable-stream: ^2.0.2
  checksum: 7bb3563961450e69183baa04272e042bdd7df44f6d75bf1cce0d6a628efd2d4b0a0d2a290bed0674ea7719c87e6cf6bf7406ca1d17413abf1484430d36d65580
  languageName: node
  linkType: hard

"string-length@npm:^4.0.1":
  version: 4.0.2
  resolution: "string-length@npm:4.0.2"
  dependencies:
    char-regex: ^1.0.2
    strip-ansi: ^6.0.0
  checksum: ce85533ef5113fcb7e522bcf9e62cb33871aa99b3729cec5595f4447f660b0cefd542ca6df4150c97a677d58b0cb727a3fe09ac1de94071d05526c73579bf505
  languageName: node
  linkType: hard

"string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-bom@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-bom@npm:4.0.0"
  checksum: 9dbcfbaf503c57c06af15fe2c8176fb1bf3af5ff65003851a102749f875a6dbe0ab3b30115eccf6e805e9d756830d3e40ec508b62b3f1ddf3761a20ebe29d3f3
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 69412b5e25731e1938184b5d489c32e340605bb611d6140344abc3421b7f3c6f9984b21dff296dfcf056681b82caa3bb4cc996a965ce37bcfad663e92eae9c64
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"subarg@npm:^1.0.0":
  version: 1.0.0
  resolution: "subarg@npm:1.0.0"
  dependencies:
    minimist: ^1.1.0
  checksum: 8359df72e9a2d03c35702ba58e49cac04daae8f27dff26837e12687c7d10cb800a036fd33fdc5eb0e8c24fb25d804f657fe8bde18dd3dd6ec7dab8eff7aac27e
  languageName: node
  linkType: hard

"superstruct@npm:^1.0.3":
  version: 1.0.3
  resolution: "superstruct@npm:1.0.3"
  checksum: 761790bb111e6e21ddd608299c252f3be35df543263a7ebbc004e840d01fcf8046794c274bcb351bdf3eae4600f79d317d085cdbb19ca05803a4361840cc9bb1
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: c052193a7e43c6cdc741eb7f378df605636e01ad434badf7324f17fb60c69a880d8d8fcdcb562cf94c2350e57b937d7425ab5b8326c67c2adc48f7c87c1db406
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"syntax-error@npm:^1.1.1":
  version: 1.4.0
  resolution: "syntax-error@npm:1.4.0"
  dependencies:
    acorn-node: ^1.2.0
  checksum: c1c3f048fed1948865fda5e79e11b02addb32da323c9c9fb214d3a933f9fda668e55c848f7c4082514ea4f1cf3dcfab0c7b9c762bfad1306271753c0fcc4b14f
  languageName: node
  linkType: hard

"tar@npm:^6.1.11, tar@npm:^6.1.2":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    minipass: ^5.0.0
    minizlib: ^2.1.1
    mkdirp: ^1.0.3
    yallist: ^4.0.0
  checksum: f1322768c9741a25356c11373bce918483f40fa9a25c69c59410c8a1247632487edef5fe76c5f12ac51a6356d2f1829e96d2bc34098668a2fc34d76050ac2b6c
  languageName: node
  linkType: hard

"test-exclude@npm:^6.0.0":
  version: 6.0.0
  resolution: "test-exclude@npm:6.0.0"
  dependencies:
    "@istanbuljs/schema": ^0.1.2
    glob: ^7.1.4
    minimatch: ^3.0.4
  checksum: 3b34a3d77165a2cb82b34014b3aba93b1c4637a5011807557dc2f3da826c59975a5ccad765721c4648b39817e3472789f9b0fa98fc854c5c1c7a1e632aacdc28
  languageName: node
  linkType: hard

"through2@npm:^2.0.0, through2@npm:~2.0.3":
  version: 2.0.5
  resolution: "through2@npm:2.0.5"
  dependencies:
    readable-stream: ~2.3.6
    xtend: ~4.0.1
  checksum: beb0f338aa2931e5660ec7bf3ad949e6d2e068c31f4737b9525e5201b824ac40cac6a337224856b56bd1ddd866334bbfb92a9f57cd6f66bc3f18d3d86fc0fe50
  languageName: node
  linkType: hard

"through@npm:>=2.2.7 <3":
  version: 2.3.8
  resolution: "through@npm:2.3.8"
  checksum: a38c3e059853c494af95d50c072b83f8b676a9ba2818dcc5b108ef252230735c54e0185437618596c790bbba8fcdaef5b290405981ffa09dce67b1f1bf190cbd
  languageName: node
  linkType: hard

"timers-browserify@npm:^1.0.1":
  version: 1.4.2
  resolution: "timers-browserify@npm:1.4.2"
  dependencies:
    process: ~0.11.0
  checksum: b7437e228684d8e6e193580d363ffdcd752396c0d1013503f50e412aa86e920248a8627450ad40557443e07ef6b9b602ffc940b3ba06db23774a7ab507e1911d
  languageName: node
  linkType: hard

"tmpl@npm:1.0.5":
  version: 1.0.5
  resolution: "tmpl@npm:1.0.5"
  checksum: cd922d9b853c00fe414c5a774817be65b058d54a2d01ebb415840960406c669a0fc632f66df885e24cb022ec812739199ccbdb8d1164c3e513f85bfca5ab2873
  languageName: node
  linkType: hard

"to-fast-properties@npm:^2.0.0":
  version: 2.0.0
  resolution: "to-fast-properties@npm:2.0.0"
  checksum: be2de62fe58ead94e3e592680052683b1ec986c72d589e7b21e5697f8744cdbf48c266fa72f6c15932894c10187b5f54573a3bcf7da0bfd964d5caf23d436168
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"tty-browserify@npm:0.0.1":
  version: 0.0.1
  resolution: "tty-browserify@npm:0.0.1"
  checksum: 93b745d43fa5a7d2b948fa23be8d313576d1d884b48acd957c07710bac1c0d8ac34c0556ad4c57c73d36e11741763ef66b3fb4fb97b06b7e4d525315a3cd45f5
  languageName: node
  linkType: hard

"type-check@npm:~0.3.2":
  version: 0.3.2
  resolution: "type-check@npm:0.3.2"
  dependencies:
    prelude-ls: ~1.1.2
  checksum: dd3b1495642731bc0e1fc40abe5e977e0263005551ac83342ecb6f4f89551d106b368ec32ad3fb2da19b3bd7b2d1f64330da2ea9176d8ddbfe389fb286eb5124
  languageName: node
  linkType: hard

"type-detect@npm:4.0.8":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 62b5628bff67c0eb0b66afa371bd73e230399a8d2ad30d852716efcc4656a7516904570cd8631a49a3ce57c10225adf5d0cbdcb47f6b0255fe6557c453925a15
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: e6b32a3b3877f04339bae01c193b273c62ba7bfc9e325b8703c4ee1b32dc8fe4ef5dfa54bf78265e069f7667d058e360ae0f37be5af9f153b22382cd55a9afe0
  languageName: node
  linkType: hard

"typedarray@npm:^0.0.6":
  version: 0.0.6
  resolution: "typedarray@npm:0.0.6"
  checksum: 33b39f3d0e8463985eeaeeacc3cb2e28bc3dfaf2a5ed219628c0b629d5d7b810b0eb2165f9f607c34871d5daa92ba1dc69f49051cf7d578b4cbd26c340b9d1b1
  languageName: node
  linkType: hard

"umd@npm:^3.0.0":
  version: 3.0.3
  resolution: "umd@npm:3.0.3"
  bin:
    umd: ./bin/cli.js
  checksum: 264302acabbc71ef279cfb832d6bb53096a12618e9ef8465b274c5a3fffa5f4da6cf7b8d024fec53a7114742c132bba9f6a6d4d4b5eca2bb55d556d0c57a9f15
  languageName: node
  linkType: hard

"undeclared-identifiers@npm:^1.1.2":
  version: 1.1.3
  resolution: "undeclared-identifiers@npm:1.1.3"
  dependencies:
    acorn-node: ^1.3.0
    dash-ast: ^1.0.0
    get-assigned-identifiers: ^1.2.0
    simple-concat: ^1.0.0
    xtend: ^4.0.1
  bin:
    undeclared-identifiers: bin.js
  checksum: e1f2a18d7bf735ec2b9ee464a621d8db72768e75e59334d34d1f7085e21558c621cc105dfd4cc7a0a219b91c43b71fbdea0508cdbe3b3396ed96902c6d5d590e
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.0"
  checksum: 39be078afd014c14dcd957a7a46a60061bc37c4508ba146517f85f60361acf4c7539552645ece25de840e17e293baa5556268d091ca6762747fdd0c705001a45
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: ^2.0.0
    unicode-property-aliases-ecmascript: ^2.0.0
  checksum: 1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.1.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.1.0"
  checksum: 8d6f5f586b9ce1ed0e84a37df6b42fdba1317a05b5df0c249962bd5da89528771e2d149837cad11aa26bcb84c35355cb9f58a10c3d41fa3b899181ece6c85220
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 243524431893649b62cc674d877bd64ef292d6071dd2fd01ab4d5ad26efbc104ffcd064f93f8a06b7e4ec54c172bf03f6417921a0d8c3a9994161fe1f88f815b
  languageName: node
  linkType: hard

"unique-filename@npm:^2.0.0":
  version: 2.0.1
  resolution: "unique-filename@npm:2.0.1"
  dependencies:
    unique-slug: ^3.0.0
  checksum: 807acf3381aff319086b64dc7125a9a37c09c44af7620bd4f7f3247fcd5565660ac12d8b80534dcbfd067e6fe88a67e621386dd796a8af828d1337a8420a255f
  languageName: node
  linkType: hard

"unique-slug@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-slug@npm:3.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 49f8d915ba7f0101801b922062ee46b7953256c93ceca74303bd8e6413ae10aa7e8216556b54dc5382895e8221d04f1efaf75f945c2e4a515b4139f77aa6640c
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.0.10":
  version: 1.0.10
  resolution: "update-browserslist-db@npm:1.0.10"
  dependencies:
    escalade: ^3.1.1
    picocolors: ^1.0.0
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    browserslist-lint: cli.js
  checksum: 12db73b4f63029ac407b153732e7cd69a1ea8206c9100b482b7d12859cd3cd0bc59c602d7ae31e652706189f1acb90d42c53ab24a5ba563ed13aebdddc5561a0
  languageName: node
  linkType: hard

"url@npm:~0.11.0":
  version: 0.11.0
  resolution: "url@npm:0.11.0"
  dependencies:
    punycode: 1.3.2
    querystring: 0.2.0
  checksum: 50d100d3dd2d98b9fe3ada48cadb0b08aa6be6d3ac64112b867b56b19be4bfcba03c2a9a0d7922bfd7ac17d4834e88537749fe182430dfd9b68e520175900d90
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.0.0":
  version: 1.2.0
  resolution: "use-sync-external-store@npm:1.2.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: 5c639e0f8da3521d605f59ce5be9e094ca772bd44a4ce7322b055a6f58eeed8dda3c94cabd90c7a41fb6fa852210092008afe48f7038792fd47501f33299116a
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"util@npm:0.10.3":
  version: 0.10.3
  resolution: "util@npm:0.10.3"
  dependencies:
    inherits: 2.0.1
  checksum: bd800f5d237a82caddb61723a6cbe45297d25dd258651a31335a4d5d981fd033cb4771f82db3d5d59b582b187cb69cfe727dc6f4d8d7826f686ee6c07ce611e0
  languageName: node
  linkType: hard

"util@npm:~0.12.0":
  version: 0.12.5
  resolution: "util@npm:0.12.5"
  dependencies:
    inherits: ^2.0.3
    is-arguments: ^1.0.4
    is-generator-function: ^1.0.7
    is-typed-array: ^1.1.3
    which-typed-array: ^1.1.2
  checksum: 705e51f0de5b446f4edec10739752ac25856541e0254ea1e7e45e5b9f9b0cb105bc4bd415736a6210edc68245a7f903bf085ffb08dd7deb8a0e847f60538a38a
  languageName: node
  linkType: hard

"v8-to-istanbul@npm:^9.0.1":
  version: 9.1.0
  resolution: "v8-to-istanbul@npm:9.1.0"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.12
    "@types/istanbul-lib-coverage": ^2.0.1
    convert-source-map: ^1.6.0
  checksum: 2069d59ee46cf8d83b4adfd8a5c1a90834caffa9f675e4360f1157ffc8578ef0f763c8f32d128334424159bb6b01f3876acd39cd13297b2769405a9da241f8d1
  languageName: node
  linkType: hard

"vlq@npm:^0.2.2":
  version: 0.2.3
  resolution: "vlq@npm:0.2.3"
  checksum: 2231d8caeb5b2c1a438677ab029e9a94aa6fb61ab05819c72691b792aea0456dab29576aff5ae29309ee45bad0a309e832dc45173119bca1393f3b87709d8f8d
  languageName: node
  linkType: hard

"vm-browserify@npm:^1.0.0":
  version: 1.1.2
  resolution: "vm-browserify@npm:1.1.2"
  checksum: 10a1c50aab54ff8b4c9042c15fc64aefccce8d2fb90c0640403242db0ee7fb269f9b102bdb69cfb435d7ef3180d61fd4fb004a043a12709abaf9056cfd7e039d
  languageName: node
  linkType: hard

"walker@npm:^1.0.8":
  version: 1.0.8
  resolution: "walker@npm:1.0.8"
  dependencies:
    makeerror: 1.0.12
  checksum: ad7a257ea1e662e57ef2e018f97b3c02a7240ad5093c392186ce0bcf1f1a60bbadd520d073b9beb921ed99f64f065efb63dfc8eec689a80e569f93c1c5d5e16c
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.2":
  version: 1.1.9
  resolution: "which-typed-array@npm:1.1.9"
  dependencies:
    available-typed-arrays: ^1.0.5
    call-bind: ^1.0.2
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-tostringtag: ^1.0.0
    is-typed-array: ^1.1.10
  checksum: fe0178ca44c57699ca2c0e657b64eaa8d2db2372a4e2851184f568f98c478ae3dc3fdb5f7e46c384487046b0cf9e23241423242b277e03e8ba3dabc7c84c98ef
  languageName: node
  linkType: hard

"which@npm:^2.0.1, which@npm:^2.0.2":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.5":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: ^1.0.2 || 2 || 3 || 4
  checksum: d5fc37cd561f9daee3c80e03b92ed3e84d80dde3365a8767263d03dacfc8fa06b065ffe1df00d8c2a09f731482fcacae745abfbb478d4af36d0a891fad4834d3
  languageName: node
  linkType: hard

"word-wrap@npm:~1.2.3":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: f93ba3586fc181f94afdaff3a6fef27920b4b6d9eaefed0f428f8e07adea2a7f54a5f2830ce59406c8416f033f86902b91eb824072354645eea687dff3691ccb
  languageName: node
  linkType: hard

"wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"write-file-atomic@npm:^4.0.2":
  version: 4.0.2
  resolution: "write-file-atomic@npm:4.0.2"
  dependencies:
    imurmurhash: ^0.1.4
    signal-exit: ^3.0.7
  checksum: 5da60bd4eeeb935eec97ead3df6e28e5917a6bd317478e4a85a5285e8480b8ed96032bbcc6ecd07b236142a24f3ca871c924ec4a6575e623ec1b11bf8c1c253c
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0, xtend@npm:^4.0.1, xtend@npm:^4.0.2, xtend@npm:~4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 54f0fb95621ee60898a38c572c515659e51cc9d9f787fb109cef6fde4befbe1c4602dc999d30110feee37456ad0f1660fa2edcfde6a9a740f86a290999550d30
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: ed2d96a616a9e3e1cc7d204c62ecc61f7aaab633dcbfab2c6df50f7f87b393993fe6640d017759fe112d0cb1e0119f2b4150a87305cc873fd90831c6a58ccf1c
  languageName: node
  linkType: hard

"yargs@npm:^17.3.1":
  version: 17.6.2
  resolution: "yargs@npm:17.6.2"
  dependencies:
    cliui: ^8.0.1
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.3
    y18n: ^5.0.5
    yargs-parser: ^21.1.1
  checksum: 47da1b0d854fa16d45a3ded57b716b013b2179022352a5f7467409da5a04a1eef5b3b3d97a2dfc13e8bbe5f2ffc0afe3bc6a4a72f8254e60f5a4bd7947138643
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard
