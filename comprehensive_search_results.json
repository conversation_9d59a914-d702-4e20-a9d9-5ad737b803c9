{"findings": [], "meaningful_strings": [["000005 1.ldb", ")XdefaultHomeActiveTabNam5;lhadAdvancedGasFeesSetPriorTo"], ["000005 1.ldb", "T-npm:@metamask/institu<al-wallet-snap-0IAl\":\"2025-07-18T19:34:05.000Z\"=\\"], ["000005 1.ldb", "_(),data:t.interface.encodeFunctionData(r,o)}))},s=async f"], ["000005 1.ldb", "Xr=\\\"AEEUdwmgDS8BxQKKAP4BOgDjATAAngDUAIMAoABoAOAAagCOAEQAhABMAHIAOwA9ACsANgAmAGIAHgAuACgAJwAXAC0AGgAjAB8ALwAUACkAEgAeAAkAGwARABkAFgA5ACgALQArADcAFQApABAAHgAiABAAGgAeABMAGAUhBe8BFxREN8sF2wC5AK5HAW8ArQkDzQCuhzc3NzcBP68NEfMABQdHBuw5BV8FYAA9MzkI9r4ZBg7QyQAWA9CeOwLNCjcCjqkChuA/lm+RAsXTAoP6ASfnEQDytQFJAjWVCkeXAOsA6godAB/cwdAUE0WlBCN/AQUCQRjFD/MRBjHxDQSJbw0jBzUAswBxme+tnIcAYwabAysG8QAjAEMMmxcDqgPKQyDXCMMxA7kUQwD3NXOrAKmFIAAfBC0D3x4BJQDBGdUFAhEgVD8JnwmQJiNWYUzrg0oAGwAUAB0AFnNcACkAFgBP9h3gPfsDOWDKneY2Chg"], ["000005 1.ldb", "=32)Ctlet r=0;r\\u003Ct.length;r+=e){edF i=t=>{(0,n.assert)(t>=0,\\\"Cannot skip a negative number of bytes.\\\"),(2?"], ["000005 1.ldb", "\"mod\\\",!0).mod},o.prototype.divRound=function(t){var e=this"], ["000005 1.ldb", ".decode(h.<PERSON><PERSON><PERSON><PERSON>,\\\"der\\\"),{type:\\\"dsa\\\","], ["000005 1.ldb", "c.imAUDKey(\\\"raw\\\",t,{nam>4PBKDF2\\\"},!1,[]DiveBits\\\"]).then(("], ["000005 1.ldb", ")]#_#_#_#_#_#_[Received: \\\"${t}\\\".`)};e.inMilliseconds=function(t,e){return r(t,\\\"count\\\"),t*e},e.timeSince"], ["000005 1.ldb", "$.set(\\\"metamask\\\",es),Oo.forEach((t=> t.allowedM0$ingDomainsɶ<pr.dev&&!t.produEb"], ["000005 1.ldb", "o; n of this._def.checks)\\\"min\\\"===n.kind?e.data.getTime()\\u003Cn.value&&(r=this._getOrReturnCtx(e,r),ye(r,{code:de.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.v"], ["000005 1.ldb", "i{CURVE:t,ProjectivePoint:f,normPrivateKeyToScalar:a,weierstrassEquation:o,isWithinCurveOrder:function(e){r@ti(e,fs,t.n)}}}fu"], ["000005 1.ldb", "ddiv(10**t).toFixed()}(o,i?!}Pts[0]?.decimals??9);sa"], ["000005 1.ldb", "BgURF1UQMUFRkpPSEiFSohYgIjBAYaPiBzLBEyRUYnCRFBVDRYGhstH/2gAIAQEAAT8A/qXdF0XRd8ou9a4R3aOz8AO+KUo95Uc1SyoyGcs+3OIvvPhQDrMYUWPmbzhRGFFj5m74URhRY2Zu+FEYT2NmbvhRGE1jZm84URhNY2ZvOFEYTWNmjzhRGEti5o84URhLYuaPP7IjCWxc0ecKIwksXM3nCiMJLFzN5wojCSxczd/2RGEliZm8/siKt/DthYNkTLRbWlNUqWtIKJwA7Xa8JHwFK0w6qN6EJvQ1lkGfO2AeEb1HZDRq2YNpLVrJTLkyk9lCAO4D9TtJ2nQIv0C/QDAOgGL9Lp22Yt5rl1NTLky03rWrUB+p+W2KyqubUr72SpDOSSJEonzKHiPv6dsB7UT1LduLkJuM2afyy0bzvO4RZdmtbIZSGTRHZlS069qlbVK3k6AYGkQDA0DQDonTpTaVMnTpqUSkJKlrUbkhI1kmK3rJ"], ["000005 1.ldb", ")XdefaultHomeActiveTabNam5;lhadAdvancedGasFeesSetPriorTo"], ["000005 1.ldb", "T-npm:@metamask/institu<al-wallet-snap-0-Al\":\"2025-07-18T19:34:05.000Z\"!\\"], ["000005 1.ldb", "}(t.runner,r.inputs,e);return Object.assign({},s,await i({to:t.getAddress(),data:t.interface.encodeFunctionData(r,o)}))},s=async function(...t){const e=await c(...t)"], ["000005 1.ldb", "Xr=\\\"AEEUdwmgDS8BxQKKAP4BOgDjATAAngDUAIMAoABoAOAAagCOAEQAhABMAHIAOwA9ACsANgAmAGIAHgAuACgAJwAXAC0AGgAjAB8ALwAUACkAEgAeAAkAGwARABkAFgA5ACgALQArADcAFQApABAAHgAiABAAGgAeABMAGAUhBe8BFxREN8sF2wC5AK5HAW8ArQkDzQCuhzc3NzcBP68NEfMABQdHBuw5BV8FYAA9MzkI9r4ZBg7QyQAWA9CeOwLNCjcCjqkChuA/lm+RAsXTAoP6ASfnEQDytQFJAjWVCkeXAOsA6godAB/cwdAUE0WlBCN/AQUCQRjFD/MRBjHxDQSJbw0jBzUAswBxme+tnIcAYwabAysG8QAjAEMMmxcDqgPKQyDXCMMxA7kUQwD3NXOrAKmFIAAfBC0D3x4BJQDBGdUFAhEgVD8JnwmQJiNWYUzrg0oAGwAUAB0AFnNcACkAFgBP9h3gPfsDOWDKneY2Chg"], ["000005 1.ldb", "=32)Cplet r=0;r\\u003Ct.length;r+=e)idF i=t=>{(0,n.assert)(t>=0,\\\"Cannot skip a negative number of bytes.\\\"),(2?"], ["000005 1.ldb", "ive%l:g,normPrivateKeyToScalar:y,=Equation:b,isWithinCurveOrder:v}=p({...r,to%LA"]]}