#!/usr/bin/env python3
"""
Browser Storage Recovery for MetaMask
Find MetaMask data in browser storage locations
"""

import os
import json
import shutil
from pathlib import Path

def find_browser_storage_locations():
    """Find all possible browser storage locations"""
    username = os.getenv('USERNAME')
    
    locations = {
        'Chrome': [
            f'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn',
            f'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Profile 1\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn',
            f'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Profile 2\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn',
        ],
        'Edge': [
            f'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn',
            f'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data\\Profile 1\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn',
        ],
        'Firefox': [
            f'C:\\Users\\<USER>\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles',
        ],
        'Brave': [
            f'C:\\Users\\<USER>\\AppData\\Local\\BraveSoftware\\Brave-Browser\\User Data\\Default\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn',
        ]
    }
    
    return locations

def backup_current_metamask():
    """Backup current MetaMask data before recovery attempts"""
    print("🔄 Backing up current MetaMask data...")
    
    locations = find_browser_storage_locations()
    backup_dir = "metamask_backup_current"
    
    if os.path.exists(backup_dir):
        shutil.rmtree(backup_dir)
    os.makedirs(backup_dir)
    
    for browser, paths in locations.items():
        for path in paths:
            if os.path.exists(path):
                backup_path = os.path.join(backup_dir, f"{browser}_{os.path.basename(path)}")
                try:
                    shutil.copytree(path, backup_path)
                    print(f"  ✅ Backed up {browser}: {path}")
                except Exception as e:
                    print(f"  ❌ Failed to backup {browser}: {e}")
    
    print(f"Current MetaMask data backed up to: {backup_dir}")

def search_for_metamask_data():
    """Search for MetaMask data in all browser locations"""
    print("🔍 Searching for MetaMask data in browser storage...")
    
    locations = find_browser_storage_locations()
    found_locations = []
    
    for browser, paths in locations.items():
        for path in paths:
            if os.path.exists(path):
                print(f"  ✅ Found {browser} MetaMask storage: {path}")
                found_locations.append((browser, path))
                
                # Check if it contains .ldb files
                ldb_files = list(Path(path).glob("*.ldb"))
                if ldb_files:
                    print(f"    📁 Contains {len(ldb_files)} .ldb files")
                    for ldb in ldb_files[:3]:  # Show first 3
                        size = ldb.stat().st_size
                        print(f"      - {ldb.name} ({size:,} bytes)")
                else:
                    print(f"    ❌ No .ldb files found")
            else:
                print(f"  ❌ Not found: {browser} - {path}")
    
    return found_locations

def check_browser_history_backups():
    """Check for browser backup/history files"""
    print("\n🕒 Checking for browser backup/history files...")
    
    username = os.getenv('USERNAME')
    
    backup_locations = [
        f'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\History',
        f'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Preferences',
        f'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\History',
        f'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\Preferences',
    ]
    
    for location in backup_locations:
        if os.path.exists(location):
            stat = os.stat(location)
            print(f"  ✅ Found: {location}")
            print(f"    Modified: {stat.st_mtime}")
        else:
            print(f"  ❌ Not found: {location}")

def search_system_wide_ldb():
    """Search for .ldb files system-wide (this might take a while)"""
    print("\n🌐 Searching for .ldb files system-wide...")
    print("⚠️  This may take several minutes...")
    
    username = os.getenv('USERNAME')
    search_paths = [
        f'C:\\Users\\<USER>\\AppData',
        f'C:\\Users\\<USER>\\Documents',
        f'C:\\Users\\<USER>\\Downloads',
    ]
    
    ldb_files = []
    
    for search_path in search_paths:
        if os.path.exists(search_path):
            print(f"  🔍 Searching in: {search_path}")
            try:
                for root, dirs, files in os.walk(search_path):
                    for file in files:
                        if file.endswith('.ldb'):
                            full_path = os.path.join(root, file)
                            size = os.path.getsize(full_path)
                            if size > 1000:  # Only files larger than 1KB
                                ldb_files.append((full_path, size))
                                print(f"    📁 Found: {full_path} ({size:,} bytes)")
            except PermissionError:
                print(f"    ❌ Permission denied: {search_path}")
            except Exception as e:
                print(f"    ❌ Error searching {search_path}: {e}")
    
    return ldb_files

def main():
    """Main recovery function"""
    print("MetaMask Browser Storage Recovery")
    print("=" * 50)
    
    print("🎯 RECOVERY STRATEGY:")
    print("1. Backup current MetaMask data")
    print("2. Search browser storage locations")
    print("3. Check for browser backups")
    print("4. System-wide .ldb search")
    print()
    
    # Step 1: Backup current data
    backup_current_metamask()
    print()
    
    # Step 2: Search browser locations
    found_locations = search_for_metamask_data()
    print()
    
    # Step 3: Check browser backups
    check_browser_history_backups()
    print()
    
    # Step 4: Ask user if they want system-wide search
    response = input("🤔 Do you want to perform a system-wide .ldb search? (y/n): ").lower()
    if response == 'y':
        ldb_files = search_system_wide_ldb()
        
        if ldb_files:
            print(f"\n📋 FOUND {len(ldb_files)} .LDB FILES:")
            for i, (path, size) in enumerate(ldb_files[:20]):  # Show first 20
                print(f"  {i+1:2d}. {path} ({size:,} bytes)")
            
            if len(ldb_files) > 20:
                print(f"  ... and {len(ldb_files) - 20} more files")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"1. Check the browser storage locations we found")
    print(f"2. Look for .ldb files from 2-3 days ago (before you signed out)")
    print(f"3. Copy those .ldb files to this directory")
    print(f"4. Run our vault extraction scripts on them")
    
    if found_locations:
        print(f"\n📁 PRIORITY LOCATIONS TO CHECK:")
        for browser, path in found_locations:
            print(f"  - {browser}: {path}")
        
        print(f"\n💡 MANUAL STEPS:")
        print(f"1. Navigate to these folders in File Explorer")
        print(f"2. Look for .ldb files modified 2-3 days ago")
        print(f"3. Copy them to this directory: {os.getcwd()}")
        print(f"4. Rename them to something like 'old_metamask_001.ldb'")
        print(f"5. Run our extraction scripts on the old files")

if __name__ == "__main__":
    main()
