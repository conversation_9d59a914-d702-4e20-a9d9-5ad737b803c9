#!/usr/bin/env python3
"""
MetaMask Vault Data Extractor
Specifically looks for and extracts vault data from LevelDB files
"""

import os
import json
import re
import base64
from typing import Dict, List, Optional, Any

def extract_vault_data(filepath: str) -> List[Dict[str, Any]]:
    """Extract potential vault data from a file"""
    print(f"\n=== Extracting vault data from {filepath} ===")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    vault_entries = []
    
    # Look for vault-related patterns
    patterns = [
        # Look for "vault" followed by JSON-like data
        rb'"vault"\s*:\s*"[^"]*"',
        rb'"vault"\s*:\s*\{[^}]*\}',
        
        # Look for encrypted data patterns
        rb'"data"\s*:\s*"[A-Za-z0-9+/=]{50,}"',
        rb'"salt"\s*:\s*"[A-Za-z0-9+/=]{20,}"',
        rb'"iv"\s*:\s*"[A-Za-z0-9+/=]{20,}"',
        
        # Look for KeyringController data
        rb'"KeyringController"\s*:\s*\{[^}]*"vault"[^}]*\}',
        
        # Look for complete vault objects
        rb'\{[^}]*"vault"[^}]*"data"[^}]*\}',
        rb'\{[^}]*"data"[^}]*"salt"[^}]*"iv"[^}]*\}',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, data, re.DOTALL | re.IGNORECASE)
        for match in matches:
            try:
                decoded = match.decode('utf-8', errors='ignore')
                if len(decoded) > 20:  # Only meaningful matches
                    vault_entries.append({
                        'pattern': pattern.decode('utf-8', errors='ignore'),
                        'data': decoded,
                        'length': len(decoded)
                    })
            except:
                continue
    
    # Also look for larger JSON structures that might contain vault data
    json_patterns = [
        rb'\{[^{}]*"vault"[^{}]*\}',
        rb'\{[^{}]*"KeyringController"[^{}]*\}',
    ]
    
    for pattern in json_patterns:
        matches = re.findall(pattern, data, re.DOTALL)
        for match in matches:
            try:
                decoded = match.decode('utf-8', errors='ignore')
                # Try to parse as JSON
                try:
                    parsed = json.loads(decoded)
                    vault_entries.append({
                        'pattern': 'JSON_VAULT',
                        'data': decoded,
                        'parsed_json': parsed,
                        'length': len(decoded),
                        'is_valid_json': True
                    })
                except json.JSONDecodeError:
                    # Still keep it as potential data
                    vault_entries.append({
                        'pattern': 'POTENTIAL_JSON_VAULT',
                        'data': decoded,
                        'length': len(decoded),
                        'is_valid_json': False
                    })
            except:
                continue
    
    print(f"Found {len(vault_entries)} potential vault entries")
    return vault_entries

def search_for_complete_vault(filepath: str) -> List[Dict[str, Any]]:
    """Search for complete vault structures"""
    print(f"\n=== Searching for complete vault structures in {filepath} ===")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    # Convert to string for easier searching
    try:
        text = data.decode('utf-8', errors='ignore')
    except:
        return []
    
    vault_structures = []
    
    # Look for vault with encrypted data structure
    vault_pattern = r'"vault"\s*:\s*"([A-Za-z0-9+/=]+)"'
    vault_matches = re.findall(vault_pattern, text, re.IGNORECASE)
    
    for match in vault_matches:
        if len(match) > 50:  # Encrypted data should be substantial
            vault_structures.append({
                'type': 'ENCRYPTED_VAULT',
                'vault_data': match,
                'length': len(match)
            })
    
    # Look for KeyringController structures
    keyring_pattern = r'"KeyringController"\s*:\s*(\{[^}]*"vault"[^}]*\})'
    keyring_matches = re.findall(keyring_pattern, text, re.DOTALL | re.IGNORECASE)
    
    for match in keyring_matches:
        try:
            parsed = json.loads(match)
            vault_structures.append({
                'type': 'KEYRING_CONTROLLER',
                'raw_data': match,
                'parsed_data': parsed,
                'length': len(match)
            })
        except:
            vault_structures.append({
                'type': 'KEYRING_CONTROLLER_RAW',
                'raw_data': match,
                'length': len(match)
            })
    
    print(f"Found {len(vault_structures)} complete vault structures")
    return vault_structures

def analyze_vault_candidates():
    """Analyze the most promising files for vault data"""
    print("MetaMask Vault Data Extractor")
    print("=" * 40)
    
    # Focus on the most promising files based on previous analysis
    candidate_files = [
        "000005 1.ldb",  # Had vault and salt patterns
        "000005.ldb",
        "000005_1.ldb",
        "000005_2.ldb",
        "000005_3.ldb"
    ]
    
    all_vault_data = []
    
    for filepath in candidate_files:
        if os.path.exists(filepath):
            print(f"\n{'='*60}")
            print(f"ANALYZING: {filepath}")
            print(f"{'='*60}")
            
            # Extract vault data
            vault_entries = extract_vault_data(filepath)
            
            # Search for complete structures
            vault_structures = search_for_complete_vault(filepath)
            
            file_data = {
                'file': filepath,
                'vault_entries': vault_entries,
                'vault_structures': vault_structures,
                'total_findings': len(vault_entries) + len(vault_structures)
            }
            
            all_vault_data.append(file_data)
            
            # Show summary for this file
            if vault_entries or vault_structures:
                print(f"\nSUMMARY for {filepath}:")
                print(f"  - Vault entries: {len(vault_entries)}")
                print(f"  - Complete structures: {len(vault_structures)}")
                
                # Show first few entries
                for i, entry in enumerate(vault_entries[:3]):
                    print(f"  - Entry {i+1}: {entry['data'][:100]}...")
                
                for i, structure in enumerate(vault_structures[:3]):
                    print(f"  - Structure {i+1} ({structure['type']}): {str(structure)[:100]}...")
    
    # Overall summary
    print(f"\n{'='*60}")
    print("OVERALL SUMMARY")
    print(f"{'='*60}")
    
    total_findings = sum(data['total_findings'] for data in all_vault_data)
    files_with_data = [data['file'] for data in all_vault_data if data['total_findings'] > 0]
    
    print(f"Total findings across all files: {total_findings}")
    print(f"Files with vault data: {len(files_with_data)}")
    
    if files_with_data:
        print(f"\nFiles to focus on:")
        for file_data in all_vault_data:
            if file_data['total_findings'] > 0:
                print(f"  - {file_data['file']}: {file_data['total_findings']} findings")
    
    # Save detailed results
    output_file = 'vault_extraction_results.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_vault_data, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\nDetailed results saved to: {output_file}")
    
    # Find the best candidate
    best_file = None
    best_score = 0
    
    for file_data in all_vault_data:
        score = len(file_data['vault_structures']) * 10 + len(file_data['vault_entries'])
        if score > best_score:
            best_score = score
            best_file = file_data
    
    if best_file:
        print(f"\nBest candidate file: {best_file['file']}")
        print(f"Next step: Extract specific vault data for decryption")
        
        # Show the most promising vault data
        if best_file['vault_structures']:
            print(f"\nMost promising vault structure:")
            structure = best_file['vault_structures'][0]
            print(f"Type: {structure['type']}")
            if 'vault_data' in structure:
                print(f"Vault data length: {len(structure['vault_data'])}")
                print(f"Vault data preview: {structure['vault_data'][:100]}...")
    
    return all_vault_data

if __name__ == "__main__":
    analyze_vault_candidates()
