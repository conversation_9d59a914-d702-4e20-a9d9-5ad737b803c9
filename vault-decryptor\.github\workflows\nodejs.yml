name: CI
on: [push, pull_request]

jobs:
  prepare:
    name: Prepare
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version-file: .nvmrc
          cache: npm
  lint:
    name: Test
    runs-on: ubuntu-latest
    needs:
      - prepare
    strategy:
      matrix:
        node-version: [18.x]
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: npm
      - name: Install NPM dependencies
        run: npm install --legacy-peer-deps
      - name: Build
        run: npm run build
      - name: Test
        run: npm run test
