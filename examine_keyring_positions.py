#!/usr/bin/env python3
"""
Examine Keyring Positions
Detailed examination of the areas around 'keyring' occurrences
"""

import os
import re
import json

def examine_keyring_context(filepath: str, positions: list, context_size: int = 2000):
    """Examine the context around keyring positions"""
    print(f"\n=== Examining keyring contexts in {filepath} ===")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return
    
    # Try different encodings
    text = None
    for encoding in ['utf-8', 'latin-1', 'cp1252']:
        try:
            text = data.decode(encoding, errors='ignore')
            break
        except:
            continue
    
    if not text:
        print(f"Could not decode {filepath}")
        return
    
    for i, pos in enumerate(positions):
        print(f"\n--- Keyring Position {i+1}: {pos} ---")
        
        # Extract context before and after
        start = max(0, pos - context_size//2)
        end = min(len(text), pos + context_size//2)
        context = text[start:end]
        
        # Find the exact keyring position within this context
        keyring_in_context = pos - start
        
        print(f"Context from {start} to {end}")
        print(f"Keyring at position {keyring_in_context} within context")
        
        # Show context before keyring
        before = context[:keyring_in_context]
        after = context[keyring_in_context:]
        
        print(f"\nBEFORE keyring (last 200 chars):")
        print(repr(before[-200:]))
        
        print(f"\nAFTER keyring (first 500 chars):")
        print(repr(after[:500]))
        
        # Look for specific patterns after keyring
        patterns_to_check = [
            r'\{[^}]*data[^}]*\}',
            r'\{[^}]*"data"[^}]*\}',
            r'\{.*?"data".*?\}',
            r'data.*?salt.*?iv',
            r'"data".*?"salt".*?"iv"',
            r'\{.*?salt.*?\}',
        ]
        
        print(f"\nPattern matches in the 1000 chars after keyring:")
        after_chunk = after[:1000]
        for pattern in patterns_to_check:
            matches = re.findall(pattern, after_chunk, re.DOTALL | re.IGNORECASE)
            if matches:
                print(f"  Pattern '{pattern}': {len(matches)} matches")
                for j, match in enumerate(matches[:2]):  # Show first 2 matches
                    print(f"    Match {j+1}: {repr(match[:100])}...")
        
        # Look for base64-like strings
        base64_pattern = r'[A-Za-z0-9+/]{50,}={0,2}'
        base64_matches = re.findall(base64_pattern, after_chunk)
        if base64_matches:
            print(f"\nBase64-like strings found: {len(base64_matches)}")
            for j, match in enumerate(base64_matches[:3]):
                print(f"  Base64 {j+1}: {match[:50]}...")
        
        # Save this context to a file for manual inspection
        context_filename = f"keyring_context_{i+1}.txt"
        with open(context_filename, 'w', encoding='utf-8', errors='ignore') as f:
            f.write(f"Keyring position {i+1} at file position {pos}\n")
            f.write(f"Context from {start} to {end}\n")
            f.write("="*60 + "\n")
            f.write(context)
        
        print(f"\nContext saved to: {context_filename}")

def search_for_vault_patterns_around_keyring(filepath: str):
    """Search for vault patterns specifically around keyring positions"""
    print(f"\n=== Searching for vault patterns around keyring in {filepath} ===")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    # Decode text
    text = data.decode('utf-8', errors='ignore')
    
    # Find all keyring positions
    keyring_positions = [m.start() for m in re.finditer(r'keyring', text, re.IGNORECASE)]
    
    vault_candidates = []
    
    for pos in keyring_positions:
        # Look in a larger window around keyring
        window_start = max(0, pos - 1000)
        window_end = min(len(text), pos + 3000)
        window = text[window_start:window_end]
        
        # Look for various vault-like patterns
        vault_patterns = [
            # Standard patterns
            r'\{"data":"([A-Za-z0-9+/=]{100,})","iv":"([A-Za-z0-9+/=]{20,})","salt":"([A-Za-z0-9+/=]{40,})"\}',
            r'\{[^}]*"data"[^}]*"([A-Za-z0-9+/=]{100,})"[^}]*"iv"[^}]*"([A-Za-z0-9+/=]{20,})"[^}]*"salt"[^}]*"([A-Za-z0-9+/=]{40,})"[^}]*\}',
            
            # With escape characters
            r'\{\\"data\\":\\"([A-Za-z0-9+/=]{100,})\\",\\"iv\\":\\"([A-Za-z0-9+/=]{20,})\\",\\"salt\\":\\"([A-Za-z0-9+/=]{40,})\\"\}',
            
            # Partial patterns - just look for the structure
            r'\{[^}]*data[^}]*iv[^}]*salt[^}]*\}',
            r'\{[^}]*salt[^}]*iv[^}]*data[^}]*\}',
        ]
        
        for pattern in vault_patterns:
            matches = re.finditer(pattern, window, re.DOTALL | re.IGNORECASE)
            for match in matches:
                vault_candidates.append({
                    'keyring_position': pos,
                    'match_position': window_start + match.start(),
                    'pattern': pattern,
                    'match_text': match.group(),
                    'groups': match.groups() if match.groups() else None
                })
                print(f"✅ Found vault pattern near keyring at position {pos}")
                print(f"   Match: {match.group()[:100]}...")
    
    return vault_candidates

def main():
    """Main examination function"""
    print("Detailed Keyring Position Examiner")
    print("=" * 50)
    
    target_file = "000005 1.ldb"
    
    if not os.path.exists(target_file):
        print(f"Target file {target_file} not found!")
        return
    
    # First, find keyring positions
    try:
        with open(target_file, 'rb') as f:
            data = f.read()
        text = data.decode('utf-8', errors='ignore')
        keyring_positions = [m.start() for m in re.finditer(r'keyring', text, re.IGNORECASE)]
    except Exception as e:
        print(f"Error reading file: {e}")
        return
    
    print(f"Found keyring at positions: {keyring_positions}")
    
    # Examine context around each keyring
    examine_keyring_context(target_file, keyring_positions)
    
    # Search for vault patterns around keyring
    vault_candidates = search_for_vault_patterns_around_keyring(target_file)
    
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    
    print(f"Keyring positions found: {len(keyring_positions)}")
    print(f"Vault candidates found: {len(vault_candidates)}")
    
    if vault_candidates:
        print(f"\n🎯 VAULT CANDIDATES:")
        for i, candidate in enumerate(vault_candidates):
            print(f"\nCandidate {i+1}:")
            print(f"  Keyring position: {candidate['keyring_position']}")
            print(f"  Match position: {candidate['match_position']}")
            print(f"  Pattern type: {candidate['pattern'][:50]}...")
            print(f"  Match text: {candidate['match_text'][:150]}...")
            
            if candidate['groups']:
                print(f"  Extracted groups: {len(candidate['groups'])}")
                for j, group in enumerate(candidate['groups']):
                    print(f"    Group {j+1}: {group[:50]}...")
        
        # Save candidates
        with open('vault_candidates_near_keyring.json', 'w') as f:
            json.dump(vault_candidates, f, indent=2, default=str)
        
        print(f"\nCandidates saved to: vault_candidates_near_keyring.json")
    
    else:
        print(f"\n⚠️  No vault patterns found near keyring positions")
        print(f"Check the keyring_context_*.txt files for manual inspection")
    
    print(f"\n💡 NEXT STEPS:")
    print(f"1. Open keyring_context_*.txt files in a text editor")
    print(f"2. Search for 'data', 'salt', 'iv' manually")
    print(f"3. Look for the pattern described in the official guide")
    print(f"4. The vault data should be a dense block of text after keyring")

if __name__ == "__main__":
    main()
