#!/usr/bin/env python3
"""
Comprehensive MetaMask Vault Search
Searches for any possible vault data patterns in the .ldb files
"""

import os
import re
import json
import base64
from typing import List, Dict, Any

def search_for_any_vault_patterns(filepath: str) -> List[Dict[str, Any]]:
    """Search for any possible vault-related patterns"""
    print(f"\n=== Comprehensive search in {filepath} ===")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    # Convert to text
    try:
        text = data.decode('utf-8', errors='ignore')
    except:
        return []
    
    findings = []
    
    # 1. Search for any JSON-like structures with "data", "iv", "salt"
    patterns = [
        # Standard vault format
        r'\{[^{}]*"data"\s*:\s*"([^"]+)"[^{}]*"iv"\s*:\s*"([^"]+)"[^{}]*"salt"\s*:\s*"([^"]+)"[^{}]*\}',
        r'\{[^{}]*"salt"\s*:\s*"([^"]+)"[^{}]*"iv"\s*:\s*"([^"]+)"[^{}]*"data"\s*:\s*"([^"]+)"[^{}]*\}',
        r'\{[^{}]*"iv"\s*:\s*"([^"]+)"[^{}]*"data"\s*:\s*"([^"]+)"[^{}]*"salt"\s*:\s*"([^"]+)"[^{}]*\}',
        
        # Variations with different quotes
        r"\{[^{}]*'data'\s*:\s*'([^']+)'[^{}]*'iv'\s*:\s*'([^']+)'[^{}]*'salt'\s*:\s*'([^']+)'[^{}]*\}",
        
        # Without quotes around keys
        r'\{[^{}]*data\s*:\s*"([^"]+)"[^{}]*iv\s*:\s*"([^"]+)"[^{}]*salt\s*:\s*"([^"]+)"[^{}]*\}',
    ]
    
    for i, pattern in enumerate(patterns):
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        for match in matches:
            if len(match) == 3:
                # Determine the order based on pattern
                if i == 0:  # data, iv, salt
                    data_val, iv_val, salt_val = match
                elif i == 1:  # salt, iv, data
                    salt_val, iv_val, data_val = match
                elif i == 2:  # iv, data, salt
                    iv_val, data_val, salt_val = match
                else:
                    data_val, iv_val, salt_val = match
                
                findings.append({
                    'type': f'VAULT_PATTERN_{i+1}',
                    'data': data_val,
                    'iv': iv_val,
                    'salt': salt_val,
                    'data_length': len(data_val),
                    'iv_length': len(iv_val),
                    'salt_length': len(salt_val),
                    'source_file': filepath
                })
                print(f"✅ Found vault pattern {i+1}")
    
    # 2. Search for KeyringController or vault mentions with surrounding context
    context_patterns = [
        r'KeyringController[^{]*(\{[^}]*\})',
        r'vault[^{]*(\{[^}]*\})',
        r'keyring[^{]*(\{[^}]*\})',
    ]
    
    for pattern in context_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        for match in matches:
            if 'data' in match.lower() and ('iv' in match.lower() or 'salt' in match.lower()):
                findings.append({
                    'type': 'CONTEXT_MATCH',
                    'raw_data': match,
                    'source_file': filepath
                })
                print(f"✅ Found context match")
    
    # 3. Search for any long base64-like strings that could be encrypted data
    base64_pattern = r'"([A-Za-z0-9+/]{100,}={0,2})"'
    base64_matches = re.findall(base64_pattern, text)
    
    for match in base64_matches:
        try:
            decoded = base64.b64decode(match)
            if len(decoded) > 50:  # Substantial data
                findings.append({
                    'type': 'POTENTIAL_ENCRYPTED_DATA',
                    'data': match,
                    'decoded_length': len(decoded),
                    'source_file': filepath
                })
                print(f"✅ Found potential encrypted data")
        except:
            continue
    
    # 4. Search for specific MetaMask storage keys
    metamask_keys = [
        r'"vault"\s*:\s*"([^"]+)"',
        r'"data"\s*:\s*"([A-Za-z0-9+/]{50,}={0,2})"',
        r'"encryptedData"\s*:\s*"([^"]+)"',
        r'"KeyringController"\s*:\s*(\{[^}]*\})',
    ]
    
    for pattern in metamask_keys:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            findings.append({
                'type': 'METAMASK_KEY_MATCH',
                'data': match,
                'source_file': filepath
            })
            print(f"✅ Found MetaMask key match")
    
    return findings

def extract_readable_strings(filepath: str, min_length: int = 50) -> List[str]:
    """Extract all readable strings that might contain vault data"""
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except:
        return []
    
    try:
        text = data.decode('utf-8', errors='ignore')
    except:
        return []
    
    # Split by common separators and extract meaningful strings
    parts = re.split(r'[\x00-\x1f\x7f-\x9f]+', text)
    
    meaningful_strings = []
    for part in parts:
        clean_part = part.strip()
        if len(clean_part) >= min_length:
            # Check if it contains vault-related keywords
            if any(keyword in clean_part.lower() for keyword in ['vault', 'data', 'salt', 'iv', 'keyring', 'metamask']):
                meaningful_strings.append(clean_part)
    
    return meaningful_strings

def main():
    """Main comprehensive search"""
    print("Comprehensive MetaMask Vault Search")
    print("=" * 50)
    
    # Get all .ldb files
    ldb_files = [f for f in os.listdir('.') if f.endswith('.ldb')]
    
    if not ldb_files:
        print("No .ldb files found")
        return
    
    print(f"Searching {len(ldb_files)} .ldb files...")
    
    all_findings = []
    all_strings = []
    
    for ldb_file in ldb_files:
        # Search for vault patterns
        findings = search_for_any_vault_patterns(ldb_file)
        all_findings.extend(findings)
        
        # Extract meaningful strings
        strings = extract_readable_strings(ldb_file)
        if strings:
            all_strings.extend([(ldb_file, s) for s in strings])
            print(f"Found {len(strings)} meaningful strings in {ldb_file}")
    
    # Summary
    print(f"\n{'='*60}")
    print("COMPREHENSIVE SEARCH RESULTS")
    print(f"{'='*60}")
    
    print(f"Total findings: {len(all_findings)}")
    print(f"Total meaningful strings: {len(all_strings)}")
    
    # Show findings
    if all_findings:
        print(f"\n🎯 VAULT PATTERN FINDINGS:")
        for i, finding in enumerate(all_findings):
            print(f"\nFinding {i+1}:")
            print(f"  Type: {finding['type']}")
            print(f"  Source: {finding['source_file']}")
            
            if finding['type'].startswith('VAULT_PATTERN'):
                print(f"  Data length: {finding['data_length']}")
                print(f"  IV length: {finding['iv_length']}")
                print(f"  Salt length: {finding['salt_length']}")
                print(f"  Data preview: {finding['data'][:50]}...")
                
                # Create vault JSON for this finding
                vault_json = {
                    "data": finding['data'],
                    "iv": finding['iv'],
                    "salt": finding['salt']
                }
                
                vault_filename = f"found_vault_{i+1}.json"
                with open(vault_filename, 'w') as f:
                    json.dump(vault_json, f, indent=2)
                
                vault_text_filename = f"found_vault_{i+1}.txt"
                with open(vault_text_filename, 'w') as f:
                    f.write(json.dumps(vault_json))
                
                print(f"  ✅ Saved to: {vault_filename} and {vault_text_filename}")
            
            elif 'data' in finding:
                print(f"  Data preview: {str(finding['data'])[:100]}...")
    
    # Show meaningful strings
    if all_strings:
        print(f"\n📋 MEANINGFUL STRINGS (first 5):")
        for i, (filename, string) in enumerate(all_strings[:5]):
            print(f"\n{i+1}. From {filename}:")
            print(f"   {string[:200]}...")
    
    # Save all results
    results = {
        'findings': all_findings,
        'meaningful_strings': [(f, s[:500]) for f, s in all_strings[:20]]  # Limit for file size
    }
    
    with open('comprehensive_search_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\nDetailed results saved to: comprehensive_search_results.json")
    
    # Instructions
    if all_findings:
        vault_findings = [f for f in all_findings if f['type'].startswith('VAULT_PATTERN')]
        if vault_findings:
            print(f"\n🎉 SUCCESS! Found {len(vault_findings)} potential vault structure(s)!")
            print(f"\n🎯 NEXT STEPS:")
            print(f"1. Go to: https://metamask.github.io/vault-decryptor/")
            print(f"2. Copy content from found_vault_1.txt")
            print(f"3. Paste into the vault decryptor")
            print(f"4. Enter your MetaMask password")
            print(f"5. Click 'Decrypt'")
            
            # Show the first vault for copy-paste
            if vault_findings:
                first_vault = vault_findings[0]
                vault_json = {
                    "data": first_vault['data'],
                    "iv": first_vault['iv'],
                    "salt": first_vault['salt']
                }
                print(f"\n📋 VAULT DATA TO COPY-PASTE:")
                print(f"{'='*60}")
                print(json.dumps(vault_json))
                print(f"{'='*60}")
        else:
            print(f"\n⚠️  Found some data but no complete vault structures")
    else:
        print(f"\n❌ No vault patterns found")
        print(f"The .ldb files might not contain MetaMask data or might be corrupted")

if __name__ == "__main__":
    main()
