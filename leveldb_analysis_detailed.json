[{"file": "000005 1.ldb", "size": 2119008, "strings_count": 7900, "metamask_data": [{"raw_string": "E8{exclusive:n}=r", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "h Js({programAddress:e,seeds/!5", "keywords_found": ["seed"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "1547568aIVv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "K$m=p.modn(d=F4t);r=(p=p.idiv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": ",toPrimitive]", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "0,salt:e,itera!B s:r,hash:", "keywords_found": ["salt"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "t;$egative=0,", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "(iv(f,p,l);m", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "@8,\\\"DIVIDE_BY_ZER!", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "'i (', received}&", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "$.negative?", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "iV0{px:n,py:i,pz!", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "T-npm:@metamask/institu<al-wallet-snap-0-Al\":\"2025-07-18T19:34:05.000Z\"!\\", "keywords_found": ["metamask"], "json_data": null, "is_json": false, "length": 73}, {"raw_string": "$DataSize=2%F", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "yp),ng. <PERSON>", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "@data:D(H),stack:D!1", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "t;e(negative=0,", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "$ was given:G", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "t;!,.negative=0,", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "d} but received one with a !", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "<YV(MetadataPoi,", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "$ was given", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": ";HECP256K1_ORDER_DIV_", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "c$privateKey", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "$=(p=p.idiv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "4_o=\\\"metamask:!R", "keywords_found": ["metamask"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "V,,received:t.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": ":e,derivaa", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 10}, {"raw_string": "$metamask\\\"", "keywords_found": ["metamask"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "ddiv(10**t).toFixed()}(o,i?!}Pts[0]?.decimals??9);sa", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 52}, {"raw_string": "$TWO_POW256=e.SECP256K1_ORDER_DIV_", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "8`Received an in%", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "$:c,keyringQ", "keywords_found": ["keyring"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "+(<PERSON>.Receive}Y", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "D0PrivilegeEsca", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "eA (FeeData();h", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "\\>i.SECP256K1_ORDER_DIV_2j", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "$data&&(r.mE", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": ",BoxedPrimitiVh", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "4is a deceptivej3", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "Dhild data too shorI", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "M4P=fo(...E),x=y-4{seed:P,k2sig:F", "keywords_found": ["seed"], "json_data": null, "is_json": false, "length": 33}, {"raw_string": "%<\\\"universal\\\":\\\"", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "H extraData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "sA:,er than data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "\"mod\\\",!0).mod},o.prototype.divRound=function(t){var e=this", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 59}, {"raw_string": "$Projective", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "8ctivatedEIP(386", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "D,data:Se(xe),stack", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "\" received:", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "qn=t=>AN(DataView(t.)v", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "K(m=f.modrn(c=G4t);r=(f=f.idiv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "8\\\"min\\\"===n.kind?e.data.getTime()\\u003Cn.value&&(r=this._getOrReturnCtx(e,r),ye(r,{code:de.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n", "keywords_found": ["iv", "data"], "json_data": null, "is_json": false, "length": 151}, {"raw_string": "_),S=bm4{seed:A,k2sig:NS", "keywords_found": ["seed"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": ",4 but received:", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "\\, but received: ${R(e)}`.d", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "(,\\\"DataViewJ", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "{\"t\u0010\u0014Fragme\u000e\u0012\u0004),\u001eh\u000f\u0001\b:e,\t>\u0004:n:[\u0010\u0000n\u001e\b\u0004)}\u0015̥R\u0005\u0000{\u0000c\u001a\u0014*\r\u0004t)\bt){\u001e\t\u0011(%B\u0005\bswiA6\u0017\base\u001aJ\u0016\u0016\u001b\u0012\b\\\":m\u0000d\r\bt);\t&\u0000e\u000em\u0012\u0019 \u0000hB \u0000\u000ew\u0012\u0019 \u0000f> \u0000\u0000f\u001ae\u0012\b\\\":\tQ\u0018receive\u00193\u0000pB3\u0000\u001a\u0012\u0019#\u0000g>#\u0000\u0016,\u0012\u0019!\u0000m\u0015!&\u001a\u0000\\\u0016\u0004ec\u0012a\u0014!\u00119\u0016\u0001\u0013\u0004){\t\u0012&\b\u000b\u0001\u000b\u0001\u000b\u0001F\u000b\u0001\u0000h,`unsupported\u000e\u0019\u0016/\u0011t}", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 218}, {"raw_string": "n(d=F4t);r=(p=p.idiv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "c.imAUDKey(\\\"raw\\\",t,{nam>4PBKDF2\\\"},!1,[]DiveBits\\\"]).then((", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 61}, {"raw_string": "<a+=\\\". Received", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "<\\\",e.Divider=\\\"d", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "{\"c\t\u001fT-npm:@metamask/institu<al-wallet-snap-0IAl\":\"2025-07-18T19:34:05.000Z\"=\\\u0000E\u0014urringQ<\u0014reques<method\":\"execute\u0000sA\u0003\u0004ul\u0001\u00145/15 *\u0011\u0002\u0004\",\u0015\u001c%>\u0000\u001c3:59.521\u0001\u0001\u0000\"\u0006\u0001\u0001y9\u0001\u00001f9\u0001\f5:009\u0001\u000019\u0001$manageSlee6\u0001=B=\u0001\u00006=\u00016=\u0001\u0014solanao\u0002\u001429.538\u0001Z\u0001RU\u0000h\u0002(refreshSend\faram\u000e-\baW=6\u0010PT30So\u0002\u000437\u0001Zo\u0002F\u0000!Vh\u0002F,\u0000na\u0002\b4:1+\u0001Z\u0002-+\u0018Confirm\fEsti\t\nr=\u0001\u00002=\u0001%=\u0001V=\u0001\u00002f\u0003\u00006\u0003J\u0001\u000e\t\u0004re\u0004QM\u0000RIpJ\u0004,\"^t\u0002\b*/2\u0004\u000439z\u0002A\u0000C\u0012\f\u0004cy6=\u0006\u0001\u0014encyRaP\u0010{\"ETH!?\fconv\u0016\n\u0016\b\u00000\u0012S\n\r\u0013\u0001-\u0000\"\u0001\u0013\fusdC\u0015)\t\u0016\u000e\t\u0001o\tY\u0000t\u0011v\u0004\":\u0001-a\u0004\u001cDeFiPosi6\u0006 }", "keywords_found": ["metamask"], "json_data": null, "is_json": false, "length": 437}, {"raw_string": "{\"c\t\u001fT-npm:@metamask/institu<al-wallet-snap-0-Al\":\"2025-07-18T19:34:05.000Z\"!\\\u0000E\u0014urringQ<\u0014reques<method\":\"execute\u0000sA\u0003\u0004ul\u0001\u00145/15 *\u0011\u0002\u0004\",\u0015\u001c%>\u0000\u001c3:59.521\u0001\u0001\u0000\"\u0006\u0001\u0001y9\u0001\u00001f9\u0001\f5:009\u0001\u000019\u0001$manageSlee\u001a\u0001=B=\u0001\u00006=\u00016=\u0001\u0014solanao\u0002\u001429.538\u0001Z\u0001RU\u0000h\u0002(refreshSendm\faram\u000e\u0011\baW=6\u0010PT30So\u0002\u000437\u0001Zo\u0002F\u0000!Vh\u0002F,\u0000na\u0002\b4:1+\u0001Z\u0002-+\u0018Confirm\fEsti\t\nr=\u0001\u00002=\u0001%=\u0001V=\u0001\u00002f\u0003\u00006\u0003J\u0001\u000e\t\u0004re\u0004QM\u0000RIpJ\u0004,\"^t\u0002\b*/2\u0004\u000439z\u0002A\u0000C\u0012\f\u0004cy6=\u0006\u0001\u0014encyRaP\u0010{\"ETH!?\fconv\u0016\n\u0016\b\u00000\u00127\n\r\u0013\u0001-\u0000\"\u0001\u0013\fusdC\u0015)\t\u0016\u000eq\t\u0001o\tY\u0000t\u0011v\u0004\":\u0001-a\u0004\u001cDeFiPosi6\u0006 }", "keywords_found": ["metamask"], "json_data": null, "is_json": false, "length": 439}, {"raw_string": "\\8`Received an in%", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "$m=p.modn(d=F4t);r=(p=p.idiv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "H, but received: \\\"+", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "r?u:(!M u=h.div(u6", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "#WHot enough data to d", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "GŬ( Initiativez", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "hrithm.params.priv_key=n.DSA", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "*<too low. given $", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "Symbols,IV[", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "T long, but received ${", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": ".decode(h.<PERSON><PERSON><PERSON><PERSON>,\\\"der\\\"),{type:\\\"dsa\\\",", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 51}, {"raw_string": "X\\\"Private accessor was mQ", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "i=b[r]IV9UX", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "2(,t.negative", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "=32)Cplet r=0;r\\u003Ct.length;r+=e)idF i=t=>{(0,n.assert)(t>=0,\\\"Cannot skip a negative number of bytes.\\\"),(2?", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 111}, {"raw_string": "=0ed: 2, receiv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "ive%l:g,normPrivateKeyToScalar:y,=", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "PRIVATE-6CDI", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "M4A=Qn(...E),k=mMK0{seed:A,k2sig2`", "keywords_found": ["seed"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "0MetadataServi", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": ",(\\\"data\\\",r)e2zm", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "CdivRound=function(t){var e=this.divmod(t);if(e.mod.isZero())return e", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 69}, {"raw_string": ">P34,\\\"BAD_STORAGE_DATA", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "$t fee data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "=32)Ctlet r=0;r\\u003Ct.length;r+=e){edF i=t=>{(0,n.assert)(t>=0,\\\"Cannot skip a negative number of bytes.\\\"),(2?", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 112}, {"raw_string": "1P,{key:a,iv:u}}},77847", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "$. Received", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "Xr=\\\"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\\\";const $r=new Map([[8217,\\\"apostrophe\\\"],[8260,\\\"fraction slash\\\"],[12539,\\\"middle dot\\\"]]),ts=4;function es(t){return function(t){let e=0;return()=>t[e++]}(function(t){let e=0;function n(){return t[e++]\\u003C\\u003C8|t[e++]}let r=n(),s=1,i=[0,1];for(let t=1;t\\u003Cr;t++)i.push(s+=n());let o=n(),a=e;e+=o;let c=0,l=0;function u(){return 0==c&&(l=l\\u003C\\u003C8|t[e++],c=8),l>>--c&1}const h=2**31,f=h>>>1,d=h-1;let p=0;for(let t=0;t\\u003C31;t++)p=p\\u003C\\u003C1|u();let g=[],m=0,y=h;for(;;){let t=Math.floor(((p-m+1)*s-1)/y),e=0,n=r;for(;n-e>1;){let r=e+n>>>1;t\\u003Ci[r]?n=r:e=r}if(0==e)break;g.push(e);let o=m+Math.floor(y*i[e]/s),a=m+Math.floor(y*i[e+1]/s)-1;for(;0==((o^a)&f);)p=p\\u003C\\u003C1&d|u(),o=o\\u003C\\u003C1&d,a=a\\u003C\\u003C1&d|1;for(;o&~a&536870912;)p=p&f|p\\u003C\\u003C1&d>>>1|u(),o=o\\u003C\\u003C1^f,a=(a^f)\\u003C\\u003C1|f|1;m=o,y=1+a-o}let w=r-4;return g.map((e=>{switch(e-w){case 3:return w+65792+(t[a++]\\u003C\\u003C16|t[a++]\\u003C\\u003C8|t[a++]);case 2:return w+256+(t[a++]\\u003C\\u003C8|t[a++]);case 1:return w+t[a++];default:return e-1}}))}(function(t){let e=[];[...\\\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\\\"].forEach(((t,n)=>e[t.charCodeAt(0)]=n));let n=t.length,r=new Uint8Array(6*n>>3);for(let s=0,i=0,o=0,a=0;s\\u003Cn;s++)a=a\\u003C\\u003C6|e[t.charCodeAt(s)],o+=6,o>=8&&(r[i++]=a>>(o-=8));return r}(t)))}function ns(t){return 1&t?~t>>1:t>>1}function rs(t,e){let n=Array(t);for(let r=0,s=0;r\\u003Ct;r++)n[r]=s+=ns(e());return n}function ss(t,e=0){let n=[];for(;;){let r=t(),s=t();if(!s)break;e+=r;for(let t=0;t\\u003Cs;t++)n.push(e+t);e+=s+1}return n}function is(t){return as((()=>{let e=ss(t);if(e.length)return e}))}function os(t){let e=[];for(;;){let n=t();if(0==n)break;e.push(ls(n,t))}for(;;){let n=t()-1;if(n\\u003C0)break;e.push(us(n,t))}return e.flat()}function as(t){let e=[];for(;;){let n=t(e.length);if(!n)break;e.push(n)}return e}function cs(t,e,n){let r=Array(t).fill().map((()=>[]));for(let s=0;s\\u003Ce;s++)rs(t,n).forEach(((t,e)=>r[e].push(t)));return r}function ls(t,e){let n=1+e(),r=e(),s=as(e);return cs(s.length,1+t,e).flatMap(((t,e)=>{let[i,...o]=t;return Array(s[e]).fill().map(((t,e)=>{let s=e*r;return[i+e*n,o.map((t=>t+s))]}))}))}function us(t,e){return cs(1+e(),1+t,e).map((t=>[t[0],t.slice(1)]))}function hs(t){return`{${function(t){return t.toString(16).toUpperCase().padStart(2,\\\"0\\\")}(t)}}`}function fs(t){let e=[];for(let n=0,r=t.length;n\\u003Cr;){let r=t.codePointAt(n);n+=r\\u003C65536?1:2,e.push(r)}return e}function ds(t){let e=t.length;if(e\\u003C4096)return String.fromCodePoint(...t);let n=[];for(let r=0;r\\u003Ce;)n.push(String.fromCodePoint(...t.slice(r,r+=4096)));return n.join(\\\"\\\")}function ps(t,e){let n=t.length,r=n-e.length;for(let s=0;0==r&&s\\u003Cn;s++)r=t[s]-e[s];return r}var gs=\\\"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\\\";const ms=44032,ys=4352,ws=4449,bs=4519,As=28,vs=21*As,Es=ms+19*vs,ks=ys+19,Ps=ws+21,xs=bs+As;function Ns(t){return t>>24&255}function Bs(t){return 16777215&t}let Is,Cs,Os,Rs;function Ts(t){return t>=ms&&t\\u003CEs}function Ss(t,e){if(t>=ys&&t\\u003Cks&&e>=ws&&e\\u003CPs)return ms+(t-ys)*vs+(e-ws)*As;if(Ts(t)&&e>bs&&e\\u003Cxs&&(t-ms)%As==0)return t+(e-bs);{let n=Rs.get(t);return n&&(n=n.get(e),n)?n:-1}}function Fs(t){Is||function(){let t=es(gs);Is=new Map(is(t).flatMap(((t,e)=>t.map((t=>[t,e+1\\u003C\\u003C24]))))),Cs=new Set(ss(t)),Os=new Map,Rs=new Map;for(let[e,n]of os(t)){if(!Cs.has(e)&&2==n.length){let[t,r]=n,s=Rs.get(t);s||(s=new Map,Rs.set(t,s)),s.set(r,e)}Os.set(e,n.reverse())}}();let e=[],n=[],r=!1;function s(t){let n=Is.get(t);n&&(r=!0,t|=n),e.push(t)}for(let r of t)for(;;){if(r\\u003C128)e.push(r);else if(Ts(r)){let t=r-ms,e=t%vs/As|0,n=t%As;s(ys+(t/vs|0)),s(ws+e),n>0&&s(bs+n)}else{let t=Os.get(r);t?n.push(...t):s(r)}if(!n.length)break;r=n.pop()}if(r&&e.length>1){let t=Ns(e[0]);for(let n=1;n\\u003Ce.length;n++){let r=Ns(e[n]);if(0==r||t\\u003C=r){t=r;continue}let s=n-1;for(;;){let n=e[s+1];if(e[s+1]=e[s],e[s]=n,!s)break;if(t=Ns(e[--s]),t\\u003C=r)break}t=Ns(e[n])}}return e}function Us(t){return Fs(t).map(Bs)}function Ds(t){return function(t){let e=[],n=[],r=-1,s=0;for(let i of t){let t=Ns(i),o=Bs(i);if(-1==r)0==t?r=o:e.push(o);else if(s>0&&s>=t)0==t?(e.push(r,...n),n.length=0,r=o):n.push(o),s=t;else{let i=Ss(r,o);i>=0?r=i:0==s&&0==t?(e.push(r),r=o):(n.push(o),s=t)}}return r>=0&&e.push(r,...n),e}(Fs(t))}const Ls=45,Ms=\\\".\\\",Gs=65039,Hs=1,Qs=t=>Array.from(t);function js(t,e){return t.P.has(e)||t.Q.has(e)}class Vs extends Array{get is_emoji(){return!0}}let Js,zs,Ks,qs,_s,Zs,Ws,Ys,Xs,$s,ti,ei;function ni(){if(Js)return;let t=es(Xr);const e=()=>ss(t),n=()=>new Set(e()),r=(t,e)=>e.forEach((e=>t.add(e)));Js=new Map(os(t)),zs=n(),Ks=e(),qs=new Set(e().map((t=>Ks[t]))),Ks=new Set(Ks),_s=n(),Zs=n();let s=is(t),i=t();const o=()=>{let t=new Set;return e().forEach((e=>r(t,s[e]))),r(t,e()),t};Ws=as((e=>{let n=as(t).map((t=>t+96));if(n.length){let r=e>=i;return n[0]-=32,n=ds(n),r&&(n=`Restricted[${n}]`),{N:n,P:o(),Q:o(),M:!t(),R:r}}})),Ys=n(),Xs=new Map;let a=e().concat(Qs(Ys)).sort(((t,e)=>t-e));a.forEach(((e,n)=>{let r=t(),s=a[n]=r?a[n-r]:{V:[],M:new Map};s.V.push(e),Ys.has(e)||Xs.set(e,s)}));for(let{V:t,M:e}of new Set(Xs.values())){let n=[];for(let e of t){let t=Ws.filter((t=>js(t,e))),s=n.find((({G:e})=>t.some((t=>e.has(t)))));s||(s={G:new Set,V:[]},n.push(s)),s.V.push(e),r(s.G,t)}let s=n.flatMap((t=>Qs(t.G)));for(let{G:t,V:r}of n){let n=new Set(s.filter((e=>!t.has(e))));for(let t of r)e.set(t,n)}}$s=new Set;let c=new Set;const l=t=>$s.has(t)?c.add(t):$s.add(t);for(let t of Ws){for(let e of t.P)l(e);for(let e of t.Q)l(e)}for(let t of $s)Xs.has(t)||c.has(t)||Xs.set(t,Hs);r($s,Us($s)),ti=function(t){let e=[],n=ss(t);return function t({S:n,B:r},s,i){if(!(4&n&&i===s[s.length-1])){2&n&&(i=s[s.length-1]),1&n&&e.push(s);for(let e of r)for(let n of e.Q)t(e,[...s,n],i)}}(function e(r){let s=t(),i=as((()=>{let r=ss(t).map((t=>n[t]));if(r.length)return e(r)}));return{S:s,B:i,Q:r}}([]),[]),e}(t).map((t=>Vs.from(t))).sort(ps),ei=new Map;for(let t of ti){let e=[ei];for(let n of t){let t=e.map((t=>{let e=t.get(n);return e||(e=new Map,t.set(n,e)),e}));n===Gs?e.push(...t):e=t}for(let n of e)n.V=t}}function ri(t){return(oi(t)?\\\"\\\":`${si(ii([t]))} `)+hs(t)}function si(t){return`\\\"${t}\\\"‎`}function ii(t,e=1/0,n=hs){let r=[];var s;s=t[0],ni(),Ks.has(s)&&r.push(\\\"◌\\\"),t.length>e&&(e>>=1,t=[...t.slice(0,e),8230,...t.slice(-e)]);let i=0,o=t.length;for(let e=0;e\\u003Co;e++){let s=t[e];oi(s)&&(r.push(ds(t.slice(i,e))),r.push(n(s)),i=e+1)}return r.push(ds(t.slice(i,o))),r.join(\\\"\\\")}function oi(t){return ni(),_s.has(t)}function ai(t,e,n){if(!t)return[];ni();let r=0;return t.split(Ms).map((t=>{let s=fs(t),i={input:s,offset:r};r+=s.length+1;try{let t,r=i.tokens=fi(s,e,n),o=r.length;if(!o)throw new Error(\\\"empty label\\\");let a=i.output=r.flat();if(function(t){for(let e=t.lastIndexOf(95);e>0;)if(95!==t[--e])throw new Error(\\\"underscore allowed only at start\\\")}(a),!(i.emoji=o>1||r[0].is_emoji)&&a.every((t=>t\\u003C128)))!function(t){if(t.length>=4&&t[2]==Ls&&t[3]==Ls)throw new Error(`invalid label extension: \\\"${ds(t.slice(0,4))}\\\"`)}(a),t=\\\"ASCII\\\";else{let e=r.flatMap((t=>t.is_emoji?[]:t));if(e.length){if(Ks.has(a[0]))throw hi(\\\"leading combining mark\\\");for(let t=1;t\\u003Co;t++){let e=r[t];if(!e.is_emoji&&Ks.has(e[0]))throw hi(`emoji + combining mark: \\\"${ds(r[t-1])} + ${ii([e[0]])}\\\"`)}!function(t){let e=t[0],n=$r.get(e);if(n)throw hi(`leading ${n}`);let r=t.length,s=-1;for(let i=1;i\\u003Cr;i++){e=t[i];let r=$r.get(e);if(r){if(s==i)throw hi(`${n} + ${r}`);s=i+1,n=r}}if(s==r)throw hi(`trailing ${n}`)}(a);let n=Qs(new Set(e)),[s]=function(t){let e=Ws;for(let n of t){let t=e.filter((t=>js(t,n)));if(!t.length)throw Ws.some((t=>js(t,n)))?ui(e[0],n):li(n);if(e=t,1==t.length)break}return e}(n);!function(t,e){for(let n of e)if(!js(t,n))throw ui(t,n);if(t.M){let t=Us(e);for(let e=1,n=t.length;e\\u003Cn;e++)if(qs.has(t[e])){let r=e+1;for(let s;r\\u003Cn&&qs.has(s=t[r]);r++)for(let n=e;n\\u003Cr;n++)if(t[n]==s)throw new Error(`duplicate non-spacing marks: ${ri(s)}`);if(r-e>ts)throw new Error(`excessive non-spacing marks: ${si(ii(t.slice(e-1,r)))} (${r-e}/${ts})`);e=r}}}(s,e),function(t,e){let n,r=[];for(let t of e){let e=Xs.get(t);if(e===Hs)return;if(e){let r=e.M.get(t);if(n=n?n.filter((t=>r.has(t))):Qs(r),!n.length)return}else r.push(t)}if(n)for(let e of n)if(r.every((t=>js(e,t))))throw new Error(`whole-script confusable: ${t.N}/${e.N}`)}(s,n),t=s.N}else t=\\\"Emoji\\\"}i.type=t}catch(t){i.error=t}return i}))}function ci(t){return t.map((({input:e,error:n,output:r})=>{if(n){let r=n.message;throw new Error(1==t.length?r:`Invalid label ${si(ii(e,63))}: ${r}`)}return ds(r)})).join(Ms)}function li(t){return new Error(`disallowed character: ${ri(t)}`)}function ui(t,e){let n=ri(e),r=Ws.find((t=>t.P.has(e)));return r&&(n=`${r.N} ${n}`),new Error(`illegal mixture: ${t.N} + ${n}`)}function hi(t){return new Error(`illegal placement: ${t}`)}function fi(t,e,n){let r=[],s=[];for(t=t.slice().reverse();t.length;){let i=pi(t);if(i)s.length&&(r.push(e(s)),s=[]),r.push(n(i))", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36018}, {"raw_string": "r.U4,),ciphertext>'", "keywords_found": ["cipher"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "4riv_key=n.DSApIaram.decode(h.subject<PERSON>ri<PERSON><PERSON><PERSON>,\\\"der\\\"),{type:\\\"dsa\\\",params:h.algorithm.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 90}, {"raw_string": "0Received an i", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "8Metadata:o}=e,i!C", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "ive%l:g,normPrivateKeyToScalar:y,=Equation:b,isWithinCurveOrder:v}=p({...r,to%LA", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 80}, {"raw_string": "&Lsensitivity:\\\"varian", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "__data__mj0 r?void 0!==t", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "nL: must be positive`)M", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "i{CURVE:t,ProjectivePoint:f,normPrivateKeyToScalar:a,weierstrassEquation:o,isWithinCurveOrder:function(e){rB@ti(e,fs,t.n)}}}fu", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 126}, {"raw_string": "<$given as a", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "0oEq (`positiv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": ":u$cumulative1", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "_),S=y-4{seed:A,k2sig:F", "keywords_found": ["seed"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "@h){const{privateKeyBytes:n}=", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "Ppositive 32-bit integ", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "$ was given:t", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": ")]#_#_#_#_#_#_[Received: \\\"${t}\\\".`)};e.inMilliseconds=function(t,e){return r(t,\\\"count\\\"),t*e},e.timeSince", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 107}, {"raw_string": "$TWO_POW256=e.SECP256K1_ORDER_DIV_2=B", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 37}, {"raw_string": "P},{allowedPrivateKeyLE-", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "_(),data:t.interface.encodeFunctionData(r,o)}))},s=async f", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 58}, {"raw_string": "90enticate data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "$.set(\\\"metamask\\\",es),Oo.forEach((t=> t.allowedM0$ingDomainsɶ<pr.dev&&!t.produEb", "keywords_found": ["metamask"], "json_data": null, "is_json": false, "length": 81}, {"raw_string": "k$ or a Data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "Dhild data too shorA`\\\"BUFFER_OVERRUN\\\",{buffe", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 46}, {"raw_string": "edDA,t,privateKey", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "*8ciphertextValid4", "keywords_found": ["cipher"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "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\\\" />\\u003C/svg>';function oy(e){return Object.fromEntries(Object.entries(e).filter((([,e])=>void 0!==e)))}function iy(e){return t=>{const{key:n=null,...r}=t;return{type:e,props:oy(r),key:n}}}const ay=iy(\\\"Container\\\"),sy=iy(\\\"Box\\\"),cy=iy(\\\"Heading\\\"),uy=iy(\\\"Text\\\"),ly=iy(\\\"Section\\\"),dy=iy(\\\"Row\\\"),fy=iy(\\\"Address\\\"),hy=iy(\\\"Image\\\"),py=iy(\\\"Footer\\\"),gy=iy(\\\"Button\\\");function my(e,t){return`${e}:${t}`}const yy=JSON.parse('{\\\"D\\\":{\\\"send.title\\\":{\\\"message\\\":\\\"Send\\\"},\\\"send.balance\\\":{\\\"message\\\":\\\"Balance\\\"},\\\"send.maxButton\\\":{\\\"message\\\":\\\"Max\\\"},\\\"send.cancelButton\\\":{\\\"message\\\":\\\"Cancel\\\"},\\\"send.continueBu)", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 4298}, {"raw_string": "(removeAllLiV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "00{exclusive:r}&1", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "P},{allowedPrivateKeyL%", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "4,SyncNative=1%", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "$(\\\"Private", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "$r),{div:a|", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "+<(o,o.isNegative(", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": ":$n}-Pqn=t=>new DataView(t.)v", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "4DataView(o),t,", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "D(\\\"RSAPrivateKey\\\"*", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "8;e.negative=0,r", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "$MetaMask.\"|", "keywords_found": ["metamask"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "(cx.metamask", "keywords_found": ["metamask"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "i{CURVE:t,ProjectivePoint:f,normPrivateKeyToScalar:a,weierstrassEquation:o,isWithinCurveOrder:function(e){r@ti(e,fs,t.n)}}}fu", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 125}, {"raw_string": "FPt=this.__data__;if(r)", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "{n.equal\"9<,8,\\\"Invalid IV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "e-+$%2==0?e:riv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "xic[e].nativeToken.caip19Id,unitJ", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 33}, {"raw_string": ":(i)}catch(e){if(l(e)&&e.data)throw t.5", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "<,3:\\\"private\\\"},", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "0i,h=r&&o&&!(\\$$i)?Iy(iv(r", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "o; n of this._def.checks)\\\"min\\\"===n.kind?e.data.getTime()\\u003Cn.value&&(r=this._getOrReturnCtx(e,r),ye(r,{code:de.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.v", "keywords_found": ["iv", "data"], "json_data": null, "is_json": false, "length": 177}, {"raw_string": "E+,ng. Received", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "(.toPrimitiv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "$receiveRpc", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "4:s.receive||s.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "K[(\":\"metamask", "keywords_found": ["metamask"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "#8PrivateFieldGet6K", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "0n({key:e,iv:r", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "Re#,ancyNotAllow-5 MaxSeedLe", "keywords_found": ["seed"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "j3,t?{salt:t}:{", "keywords_found": ["salt"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "9C,{extraData:f*5", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "4{privateKey:o,", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "}(t.runner,r.inputs,e);return Object.assign({},s,await i({to:t.getAddress(),data:t.interface.encodeFunctionData(r,o)}))},s=async function(...t){const e=await c(...t)", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 165}, {"raw_string": "$Metadata:o", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "W8.splice(o+1,0,i!0}runExclusive!i", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "$getDivisor]z96", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": ")XdefaultHomeActiveTabNam5;lhadAdvancedGasFeesSetPriorTo", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 56}, {"raw_string": "tnewPrivacyPolicyToastClickedOr", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "T-npm:@metamask/institu<al-wallet-snap-0IAl\":\"2025-07-18T19:34:05.000Z\"=\\", "keywords_found": ["metamask"], "json_data": null, "is_json": false, "length": 73}], "pattern_counts": {"vault": 2, "KeyringController": 0, "data": 67, "salt": 10, "iv": 461, "cipher": 6, "mnemonic": 0, "seed": 16}, "total_patterns": 562}, {"file": "000005 2.ldb", "size": 113339, "strings_count": 540, "metamask_data": [{"raw_string": "ReleaseSRWLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "RoGetActivationFactory", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": ".rdata$r$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": ".rdata$zETW1", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "ActivityError", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "IsWdagContainerCheckActivity", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "ActivityStoppedAutomatically", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "PartA_PrivTags", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": ".rdata$zETW9", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "ReceiveCompleted", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "NtUpdateWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": ".data$r$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "PostMessageToReceiver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": ".rdata$zETW0", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "wilActivity", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "NtQueryWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "api-ms-win-crt-private-l1-1-0.dll", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 33}, {"raw_string": "UnregisterMessageReceiver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": ".rdata$sxdata", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "DllGetActivationFactory", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": ".rdata$zETW2", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "ActivityIntermediateStop", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "__x_Windows_CSecurity_CIsolation_CIHostMessageReceivedCallback", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 62}, {"raw_string": ".rdata$zzzdbg", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "AcquireSRWLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "EventActivityIdControl", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "RegisterMessageReceiver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}], "pattern_counts": {"vault": 0, "KeyringController": 0, "data": 22, "salt": 0, "iv": 83, "cipher": 0, "mnemonic": 0, "seed": 0}, "total_patterns": 105}, {"file": "000005.ldb", "size": 3668724, "strings_count": 13224, "metamask_data": [{"raw_string": "RtlTraceDatabaseLock", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "SeFreePrivileges", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "IoLoadCrashDumpDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": ".data$pr12$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "WheaAddErrorSourceDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "ExReleaseFastResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "Driver Verifier: Failed to delete VerifyDriversSuppress key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 83}, {"raw_string": "SeAppendPrivileges", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "MmProtectDriverSection", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "WerLiveKernelSubmitReport", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "SdbGetDatabaseID", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "ExEnterCriticalRegionAndAcquireSharedWaitForExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 53}, {"raw_string": ".rdata$.text$lp01ntkrnlmp.exe!20_pri7", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 37}, {"raw_string": ".data$dk01$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "IHD$HM~8MV MF(MN0Iv8C", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "ExReleaseCacheAwarePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "VfQueryDriverContext", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "Failed to open database", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "SdbGetPathCustomSdb failed to get the database path.", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 52}, {"raw_string": "ExAcquireSpinLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "E3IIVHhHD$xHH", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "MmIsDriverVerifying", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "IoSetActivityIdThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "PsGetEffectiveServerSilo", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "Can't read database header", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "WheaHwErrorReportSetSeverityDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 40}, {"raw_string": "Failed to read database header", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "PopCoalescing: Coalescing timer activated", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 41}, {"raw_string": "ExEnterCriticalRegionAndAcquireResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 48}, {"raw_string": "IoDriverObjectType", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "ExTryAcquireSpinLockExclusiveAtDpcLevel", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "IoGetDeviceInterfacePropertyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "IoWMIQueryAllDataMultiple", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "KsrCleanupPageDatabase", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "Driver Verifier: Failed to delete VerifyDrivers key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 75}, {"raw_string": "IoGetDriverObjectExtension", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "CcUnpinData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "RtlTraceDatabaseValidate", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "Driver Verifier: Failed to delete VerifierTipLimitNumerator key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 87}, {"raw_string": "KsrPersistMemoryWithMetadata", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "RtlValidRelativeSecurityDescriptor", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "IoIsActivityTracingEnabled", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "ExAcquireFastResourceSharedStarveExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 42}, {"raw_string": "PsGetEffectiveContainerId", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "ZwLockProductActivationKeys", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "Failed to get TAG_REG_VALUE_DATA_QWORD", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "ExAcquireSharedWaitForExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "WheaReportHwErrorDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "Failed to get the database id", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "ZwEnumerateDriverEntries", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "Unstructered_Data_Too_Soon", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "ZwSetDriverEntryOrder", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "RtlImageDirectoryEntryToData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "Driver Verifier: Failed to delete VerifierTipSparseness key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 83}, {"raw_string": "ZwQueryDriverEntryOrder", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ZwAlpcSendWaitReceivePort", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "ExActivationObjectType", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "WheaAddErrorSourceDeviceDriverV1", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "RtlSelfRelativeToAbsoluteSD2", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": ".rdata$01$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "IoOpenDriverRegistryKey", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "KeGetRecommendedSharedDataAlignment", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "CcGetFlushedValidData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "KeAddTriageDumpDataBlock", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "WheaHwErrorReportSetSectionNameDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 43}, {"raw_string": "LpcSendWaitReceivePort", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "WheaHwErrorReportSubmitDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "Driver Verifier: Failed to set VerifierOptionFlags key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 78}, {"raw_string": "NtAdjustPrivilegesToken", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "SdbQueryDataExTagID", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "HalSetBusDataByOffset", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "Error reading data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": ".rdata$12$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "Driver Verifier: Failed to open Memory Management key with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 71}, {"raw_string": "ZwUpdateWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "RtlTraceDatabaseCreate", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "FsRtlCreateSectionForDataScan", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "SdbGetBinaryTagData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "Unknown registry value data type", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "KeQueryEffectivePriorityThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "Attempt to read past the end of the database offset 0x%lx size 0x%lx (0x%lx)", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 76}, {"raw_string": "ExAcquireCacheAwarePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "AslpFileHasActiveMarkWrapper", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "WheaHwErrorReportGetLogDataBufferDeviceDriver", "keywords_found": ["iv", "data"], "json_data": null, "is_json": false, "length": 45}, {"raw_string": "Driver Verifier: Failed to delete VerifierRandomTargets key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 83}, {"raw_string": "ZwQueryWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "RtlTraceDatabaseAdd", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "DBGK: IoCaptureLiveDump failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 43}, {"raw_string": "SkAcquirePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": ".data$00$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "ExTryAcquireAutoExpandPushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "ExTryConvertPushLockSharedToExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": ".data$01$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "PoSetThermalPassiveCooling", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "CcIsThereDirtyDataEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "IoGetActivityIdThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "WheaReportFatalHwErrorDeviceDriverEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "TtmNotifyDeviceArrival", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "RtlGetActiveConsoleId", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "ZwDeleteWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "SdbGetTagDataSize", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "SdbpOpenLocalDatabaseEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "PoCallDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "ExTryAcquireCacheAwarePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 41}, {"raw_string": ".data$12$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "MmIsDriverVerifyingByAddress", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "Cannot resolve database, the path length is 0x%lx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 49}, {"raw_string": "WheaRemoveErrorSourceDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 33}, {"raw_string": "Database \"%ws\" is not of the same type as the main EXE", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 54}, {"raw_string": "KeInitializeTriageDumpDataArray", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "LKMDTEL: LkmdTelSubmitReport: WerLiveKernelCancelReport failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 75}, {"raw_string": "IoTransferActivityId", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "M4IV HMHURH", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "Failed to get pointer to the index data tagid x%lx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 50}, {"raw_string": "ExAcquireCacheAwarePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "CcPinMappedData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "Passive-level ISR watchdog timeout! Interrupt: %p", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 49}, {"raw_string": "Failed to read GUID of the database", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "IoQueryFullDriverPath", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "ExTryConvertSharedSpinLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "RtlLockBootStatusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "LKMDTEL: LkmdTelSubmitReport: WerLiveKernelSubmitReport failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 75}, {"raw_string": "L\\$PIv@Ik@I[8IA_A^A]_^L$", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "Failed to get TAG_REG_VALUE_DATA_BINARY", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "ExReleaseSpinLockExclusiveFromDpcLevel", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "IoSetDevicePropertyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "Driver Verifier: Failed to delete VerifierTipLimitDenominator key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 89}, {"raw_string": "IoClearActivityIdThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "IoReportInterruptInactive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": ".data$dk00$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "AslpFileHasActiveMarkWrapper failed [%x]", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 40}, {"raw_string": "IoRegisterDriverReinitialization", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "Failed to get database tag, db is corrupt", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 41}, {"raw_string": "WheaReportHwErrorDeviceDriverEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "HalSetBusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "KeQueryActiveGroupCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "Error reading size data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ExReleasePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "ExIsResourceAcquiredExclusiveLite", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 33}, {"raw_string": "Failed to get the database ID", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": ".rdata$00$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "SdbpOpenCompressedDatabase failed to open compressed database.", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 62}, {"raw_string": "DŋH`H0H+uIv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "ZwDeleteDriverEntry", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "DBGK: Could not allocate IoLivedumpControl", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 42}, {"raw_string": "WheaAddHwErrorReportSectionDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "H\\$XHl$`Ht$hH A_A^A]A\\_I^0Iv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "KseQueryDeviceData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "IofCallDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "CcWaitForCurrentLazyWriterActivity", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "IoUnregisterBootDriverCallback", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "LKMDTEL: WerPolicy is WerLiveKernelPolicyNoDump, no dump is allowed.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 68}, {"raw_string": "RtlDeriveCapabilitySidsFromName", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "CcZeroDataOnDisk", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "Iv3DL}M;sLL+uoI0I", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "ExFetchLicenseData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "IoRegisterBootDriverCallback", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "ZwAddDriverEntry", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "SdbResolveDatabaseEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "ExConvertPushLockExclusiveToShared", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "Unable to attach to active session", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "Failed to read value data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "No DATABASE tag found", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "IoSetDeviceInterfacePropertyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "IoAllocateDriverObjectExtension", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": ".rdata$09$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "ExRaiseDatatypeMisalignment", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "Failed to read database id 0x%lx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "ExAcquirePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "KiCheckForKernelApcDelivery", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "WheaCreateHwErrorReportDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "SdbGetDatabaseMatch", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "SdbInitDatabaseInMemory", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "Flags:%d; DatabasePath:%ws", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "LKMDTEL: LkmdTelpWriteDumpFile: WerLiveKernelOpenDumpFile failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 77}, {"raw_string": "KeQueryActiveProcessorCountEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "ExAcquireResourceExclusiveLite", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "KeQueryActiveProcessorAffinity", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "KseQueryDeviceDataList", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "SdbpGetMappedTagData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "RtlTraceDatabaseFind", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "LKMDTEL: WerLiveKernelCreateReport failed with status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 58}, {"raw_string": "SdbpOpenCompressedDatabase", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "KeQueryNodeActiveAffinity", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": ".rdata$zETW9", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "HalGetBusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "IoGetDriverDirectory", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "ZwLoadDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "RtlAbsoluteToSelfRelativeSD", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "I?HI#HHH3I#H3IV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "ExTryToConvertFastResourceSharedToExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 43}, {"raw_string": "InbvEnableBootDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "ExAcquireSpinLockExclusiveAtDpcLevel", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "SdbpReadTagData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "IoRegisterBootDriverReinitialization", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "HtbIvH4M<II", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": ".rdata$03$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": ".data$pr00$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "MmIsRecursiveIoFault", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "RtlSelfRelativeToAbsoluteSD", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "WheaHwErrorReportAbandonDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "DBGK: Calling IoCaptureLiveDump", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "IoPropagateActivityIdToThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "Driver at fault: %s.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "KeQueryActiveProcessors", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "FsRtlAcquireFileExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "IoGetActivityIdIrp", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "RtlIsNtDdiVersionAvailable", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "ExConvertExclusiveToSharedLite", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": ".rdata$zETW1", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "MmIsDriverSuspectForVerifier", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "Driver Verifier: Failed to delete RuleClasses key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 73}, {"raw_string": "KsrQueryMetadata", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "IoGetDevicePropertyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ExUpdateLicenseData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "The database has more matches than SDB_MAX_EXES", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 47}, {"raw_string": "AslpFileHasActiveMarkWrapper failed (FileSize: %I64u) [%x]", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 58}, {"raw_string": "ZwAdjustPrivilegesToken", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "IoCallDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "Error reading tag data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "KiBugCheckData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "WerLiveKernelCloseHandle", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "IoDeleteDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "ExfReleasePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "RtlGetSetBootStatusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "HalGetBusDataByOffset", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "CcSetDirtyPinnedData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "ExReleaseDisownedFastResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "IvE3xBE3Hӹ$", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "ExReleaseSpinLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "SePrivilegeObjectAuditAlarm", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "TmIsTransactionActive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "Driver Verifier: Failed to delete XdvVerifierOptions key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 80}, {"raw_string": "Driver Verifier: Failed to delete VerifyDriverLevel key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 79}, {"raw_string": "IoDecrementKeepAliveCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "WerLiveKernelCancelReport", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "Unable to open main database", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "WerLiveKernelInitSystem", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "HalPrivateDispatchTable", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "Error getting ptr to tag data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "SdbGetDatabaseEdition", "keywords_found": ["seed", "data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "IoWMIQueryAllData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "PoSetThermalActiveCooling", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "SePrivilegeCheck", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "FsRtlPrivateLock", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "ExConvertFastResourceExclusiveToShared", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "CcIsThereDirtyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "MmIsIoSpaceActive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "EtwActivityIdControl", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "KeQueryActiveProcessorCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "r\"IV HMHURHUPI", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "D$0HD$(HT$ Iv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "IoSetActivityIdIrp", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": ".data$dk03$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "SkReleasePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "LKMDTEL: WerLiveCancelReport failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 48}, {"raw_string": "IoReportInterruptActive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "SeSinglePrivilegeCheck", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "ExRealTimeIsUniversal", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "Failed to get the pointer to index data, index tagid 0x%lx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 58}, {"raw_string": ".rdata$zETW0", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "Driver Verifier: Failed to delete VrfPersistThroughUpgrade key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 86}, {"raw_string": ".data$dk12$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "SdbpReadMappedData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "LKMDTEL: LkmdTelSubmitReport: WerLiveKernelCloseHandle failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 74}, {"raw_string": "MmPageEntireDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "DbgkWerCaptureLiveKernelDump", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "LHD$ L+IVLI", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": ".rdata$zz$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "ZwModifyDriverEntry", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "CcUnpinDataForThread", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "SdbpGetMappedData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "ExReleaseAutoExpandPushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "IoIncrementKeepAliveCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "VfFailDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "D$(fL$ MˋU$IV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "RtlTraceDatabaseEnumerate", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "\"AIV#Hu?Hl$pHH\\$hH0A_A^A\\_^H", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "ExGetExclusiveWaiterCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "CmUnregisterMachineHiveLoadedNotification", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 41}, {"raw_string": "WheaHwErrorReportMarkAsCriticalDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 43}, {"raw_string": "WheaHwErrorReportSetFatalSeverityDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 45}, {"raw_string": "SdbpOpenCompressedDatabase failed to allocate expanded buffer - out of memory", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 77}, {"raw_string": "Failed to get database type [%x]", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "ExEnterPriorityRegionAndAcquireResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 48}, {"raw_string": ".rdata$zzzdbg", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": ".data$pr01$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": ".data$dk04$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "ExReleaseCacheAwarePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "RtlTraceDatabaseDestroy", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ExIsFastResourceHeldExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "PoLatencySensitivityHint", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "HvlQueryActiveHypervisorProcessorCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "D$ HD$0HIIv(LL+I*IILI", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "CmRegisterMachineHiveLoadedNotification", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": ".data$pr03$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "HvlQueryActiveProcessors", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "HPH3H#H3H?IV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "Driver Verifier: Failed to delete VerifierLwspPoolTags key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 82}, {"raw_string": "ExAcquireAutoExpandPushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "SdbQueryDataEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "RtlTraceDatabaseUnlock", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "ExQueryWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": ".data$dk11$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "AslEnvGetSysNativeDirPathForGuestBuf", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "KsrInitPageDatabase", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "Invalid COR20 Metadata virtual address encountered", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 50}, {"raw_string": "ExAcquireSharedStarveExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "MmResetDriverPaging", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "Trying to read mapped data past the end of the database offset 0x%x size 0x%x", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 77}, {"raw_string": "Driver Verifier: Failed to delete VerifierTriageContext key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 83}, {"raw_string": "InitErrorReportDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "WerLiveKernelCreateReport", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "RtlSetActiveConsoleId", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "KeGetEffectiveIrql", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "CcSetLoggedDataThreshold", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "MmLockPagableDataSection", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "SdbpOpenDatabaseInMemory", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "ExAcquireFastResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "Failed to get TAG_REG_VALUE_DATA_DWORD", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "ZwUnloadDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": ".rdata$zETW2", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "DBGK: Full Live Kernel Dumps are disabled. Failing request.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 59}, {"raw_string": ".data$zz$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "IoSynchronousCallDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "E3E33IvE3I违", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "SdbOpenDatabaseEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "WerLiveKernelOpenDumpFile", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "IoCreateDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "FsRtlSetDriverBacking", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "RtlUnlockBootStatusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ExTryToAcquireResourceExclusiveLite", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "ExTryAcquirePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "Failed to get TAG_REG_VALUE_DATA_SZ", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "Invalid COR20 Metadata signature encountered", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 44}, {"raw_string": "PoFxActivateComponent", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "InbvIsBootDriverInstalled", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "Trace database: failing attempt to save biiiiig trace (size %u)", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 63}, {"raw_string": "ExfAcquirePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "KdReceivePacket", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "SdbGetDatabaseMatchEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "Driver Verifier: Clearing Verifier options from Registry for preventing recursive crash.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 88}], "pattern_counts": {"vault": 0, "KeyringController": 0, "data": 97, "salt": 0, "iv": 203, "cipher": 0, "mnemonic": 0, "seed": 0}, "total_patterns": 300}, {"file": "000005_1.ldb", "size": 3668724, "strings_count": 13224, "metamask_data": [{"raw_string": "RtlTraceDatabaseLock", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "SeFreePrivileges", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "IoLoadCrashDumpDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": ".data$pr12$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "WheaAddErrorSourceDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "ExReleaseFastResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "Driver Verifier: Failed to delete VerifyDriversSuppress key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 83}, {"raw_string": "SeAppendPrivileges", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "MmProtectDriverSection", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "WerLiveKernelSubmitReport", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "SdbGetDatabaseID", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "ExEnterCriticalRegionAndAcquireSharedWaitForExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 53}, {"raw_string": ".rdata$.text$lp01ntkrnlmp.exe!20_pri7", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 37}, {"raw_string": ".data$dk01$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "IHD$HM~8MV MF(MN0Iv8C", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "ExReleaseCacheAwarePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "VfQueryDriverContext", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "Failed to open database", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "SdbGetPathCustomSdb failed to get the database path.", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 52}, {"raw_string": "ExAcquireSpinLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "E3IIVHhHD$xHH", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "MmIsDriverVerifying", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "IoSetActivityIdThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "PsGetEffectiveServerSilo", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "Can't read database header", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "WheaHwErrorReportSetSeverityDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 40}, {"raw_string": "Failed to read database header", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "PopCoalescing: Coalescing timer activated", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 41}, {"raw_string": "ExEnterCriticalRegionAndAcquireResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 48}, {"raw_string": "IoDriverObjectType", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "ExTryAcquireSpinLockExclusiveAtDpcLevel", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "IoGetDeviceInterfacePropertyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "IoWMIQueryAllDataMultiple", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "KsrCleanupPageDatabase", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "Driver Verifier: Failed to delete VerifyDrivers key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 75}, {"raw_string": "IoGetDriverObjectExtension", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "CcUnpinData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "RtlTraceDatabaseValidate", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "Driver Verifier: Failed to delete VerifierTipLimitNumerator key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 87}, {"raw_string": "KsrPersistMemoryWithMetadata", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "RtlValidRelativeSecurityDescriptor", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "IoIsActivityTracingEnabled", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "ExAcquireFastResourceSharedStarveExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 42}, {"raw_string": "PsGetEffectiveContainerId", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "ZwLockProductActivationKeys", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "Failed to get TAG_REG_VALUE_DATA_QWORD", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "ExAcquireSharedWaitForExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "WheaReportHwErrorDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "Failed to get the database id", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "ZwEnumerateDriverEntries", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "Unstructered_Data_Too_Soon", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "ZwSetDriverEntryOrder", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "RtlImageDirectoryEntryToData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "Driver Verifier: Failed to delete VerifierTipSparseness key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 83}, {"raw_string": "ZwQueryDriverEntryOrder", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ZwAlpcSendWaitReceivePort", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "ExActivationObjectType", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "WheaAddErrorSourceDeviceDriverV1", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "RtlSelfRelativeToAbsoluteSD2", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": ".rdata$01$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "IoOpenDriverRegistryKey", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "KeGetRecommendedSharedDataAlignment", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "CcGetFlushedValidData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "KeAddTriageDumpDataBlock", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "WheaHwErrorReportSetSectionNameDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 43}, {"raw_string": "LpcSendWaitReceivePort", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "WheaHwErrorReportSubmitDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "Driver Verifier: Failed to set VerifierOptionFlags key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 78}, {"raw_string": "NtAdjustPrivilegesToken", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "SdbQueryDataExTagID", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "HalSetBusDataByOffset", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "Error reading data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": ".rdata$12$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "Driver Verifier: Failed to open Memory Management key with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 71}, {"raw_string": "ZwUpdateWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "RtlTraceDatabaseCreate", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "FsRtlCreateSectionForDataScan", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "SdbGetBinaryTagData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "Unknown registry value data type", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "KeQueryEffectivePriorityThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "Attempt to read past the end of the database offset 0x%lx size 0x%lx (0x%lx)", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 76}, {"raw_string": "ExAcquireCacheAwarePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "AslpFileHasActiveMarkWrapper", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "WheaHwErrorReportGetLogDataBufferDeviceDriver", "keywords_found": ["iv", "data"], "json_data": null, "is_json": false, "length": 45}, {"raw_string": "Driver Verifier: Failed to delete VerifierRandomTargets key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 83}, {"raw_string": "ZwQueryWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "RtlTraceDatabaseAdd", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "DBGK: IoCaptureLiveDump failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 43}, {"raw_string": "SkAcquirePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": ".data$00$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "ExTryAcquireAutoExpandPushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "ExTryConvertPushLockSharedToExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": ".data$01$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "PoSetThermalPassiveCooling", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "CcIsThereDirtyDataEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "IoGetActivityIdThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "WheaReportFatalHwErrorDeviceDriverEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "TtmNotifyDeviceArrival", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "RtlGetActiveConsoleId", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "ZwDeleteWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "SdbGetTagDataSize", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "SdbpOpenLocalDatabaseEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "PoCallDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "ExTryAcquireCacheAwarePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 41}, {"raw_string": ".data$12$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "MmIsDriverVerifyingByAddress", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "Cannot resolve database, the path length is 0x%lx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 49}, {"raw_string": "WheaRemoveErrorSourceDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 33}, {"raw_string": "Database \"%ws\" is not of the same type as the main EXE", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 54}, {"raw_string": "KeInitializeTriageDumpDataArray", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "LKMDTEL: LkmdTelSubmitReport: WerLiveKernelCancelReport failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 75}, {"raw_string": "IoTransferActivityId", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "M4IV HMHURH", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "Failed to get pointer to the index data tagid x%lx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 50}, {"raw_string": "ExAcquireCacheAwarePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "CcPinMappedData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "Passive-level ISR watchdog timeout! Interrupt: %p", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 49}, {"raw_string": "Failed to read GUID of the database", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "IoQueryFullDriverPath", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "ExTryConvertSharedSpinLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "RtlLockBootStatusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "LKMDTEL: LkmdTelSubmitReport: WerLiveKernelSubmitReport failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 75}, {"raw_string": "L\\$PIv@Ik@I[8IA_A^A]_^L$", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "Failed to get TAG_REG_VALUE_DATA_BINARY", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "ExReleaseSpinLockExclusiveFromDpcLevel", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "IoSetDevicePropertyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "Driver Verifier: Failed to delete VerifierTipLimitDenominator key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 89}, {"raw_string": "IoClearActivityIdThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "IoReportInterruptInactive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": ".data$dk00$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "AslpFileHasActiveMarkWrapper failed [%x]", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 40}, {"raw_string": "IoRegisterDriverReinitialization", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "Failed to get database tag, db is corrupt", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 41}, {"raw_string": "WheaReportHwErrorDeviceDriverEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "HalSetBusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "KeQueryActiveGroupCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "Error reading size data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ExReleasePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "ExIsResourceAcquiredExclusiveLite", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 33}, {"raw_string": "Failed to get the database ID", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": ".rdata$00$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "SdbpOpenCompressedDatabase failed to open compressed database.", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 62}, {"raw_string": "DŋH`H0H+uIv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "ZwDeleteDriverEntry", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "DBGK: Could not allocate IoLivedumpControl", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 42}, {"raw_string": "WheaAddHwErrorReportSectionDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "H\\$XHl$`Ht$hH A_A^A]A\\_I^0Iv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "KseQueryDeviceData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "IofCallDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "CcWaitForCurrentLazyWriterActivity", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "IoUnregisterBootDriverCallback", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "LKMDTEL: WerPolicy is WerLiveKernelPolicyNoDump, no dump is allowed.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 68}, {"raw_string": "RtlDeriveCapabilitySidsFromName", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "CcZeroDataOnDisk", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "Iv3DL}M;sLL+uoI0I", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "ExFetchLicenseData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "IoRegisterBootDriverCallback", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "ZwAddDriverEntry", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "SdbResolveDatabaseEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "ExConvertPushLockExclusiveToShared", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "Unable to attach to active session", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "Failed to read value data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "No DATABASE tag found", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "IoSetDeviceInterfacePropertyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "IoAllocateDriverObjectExtension", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": ".rdata$09$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "ExRaiseDatatypeMisalignment", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "Failed to read database id 0x%lx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "ExAcquirePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "KiCheckForKernelApcDelivery", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "WheaCreateHwErrorReportDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "SdbGetDatabaseMatch", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "SdbInitDatabaseInMemory", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "Flags:%d; DatabasePath:%ws", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "LKMDTEL: LkmdTelpWriteDumpFile: WerLiveKernelOpenDumpFile failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 77}, {"raw_string": "KeQueryActiveProcessorCountEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "ExAcquireResourceExclusiveLite", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "KeQueryActiveProcessorAffinity", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "KseQueryDeviceDataList", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "SdbpGetMappedTagData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "RtlTraceDatabaseFind", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "LKMDTEL: WerLiveKernelCreateReport failed with status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 58}, {"raw_string": "SdbpOpenCompressedDatabase", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "KeQueryNodeActiveAffinity", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": ".rdata$zETW9", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "HalGetBusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "IoGetDriverDirectory", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "ZwLoadDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "RtlAbsoluteToSelfRelativeSD", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "I?HI#HHH3I#H3IV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "ExTryToConvertFastResourceSharedToExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 43}, {"raw_string": "InbvEnableBootDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "ExAcquireSpinLockExclusiveAtDpcLevel", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "SdbpReadTagData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "IoRegisterBootDriverReinitialization", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "HtbIvH4M<II", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": ".rdata$03$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": ".data$pr00$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "MmIsRecursiveIoFault", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "RtlSelfRelativeToAbsoluteSD", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "WheaHwErrorReportAbandonDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "DBGK: Calling IoCaptureLiveDump", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "IoPropagateActivityIdToThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "Driver at fault: %s.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "KeQueryActiveProcessors", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "FsRtlAcquireFileExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "IoGetActivityIdIrp", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "RtlIsNtDdiVersionAvailable", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "ExConvertExclusiveToSharedLite", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": ".rdata$zETW1", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "MmIsDriverSuspectForVerifier", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "Driver Verifier: Failed to delete RuleClasses key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 73}, {"raw_string": "KsrQueryMetadata", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "IoGetDevicePropertyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ExUpdateLicenseData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "The database has more matches than SDB_MAX_EXES", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 47}, {"raw_string": "AslpFileHasActiveMarkWrapper failed (FileSize: %I64u) [%x]", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 58}, {"raw_string": "ZwAdjustPrivilegesToken", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "IoCallDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "Error reading tag data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "KiBugCheckData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "WerLiveKernelCloseHandle", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "IoDeleteDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "ExfReleasePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "RtlGetSetBootStatusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "HalGetBusDataByOffset", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "CcSetDirtyPinnedData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "ExReleaseDisownedFastResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "IvE3xBE3Hӹ$", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "ExReleaseSpinLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "SePrivilegeObjectAuditAlarm", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "TmIsTransactionActive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "Driver Verifier: Failed to delete XdvVerifierOptions key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 80}, {"raw_string": "Driver Verifier: Failed to delete VerifyDriverLevel key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 79}, {"raw_string": "IoDecrementKeepAliveCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "WerLiveKernelCancelReport", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "Unable to open main database", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "WerLiveKernelInitSystem", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "HalPrivateDispatchTable", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "Error getting ptr to tag data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "SdbGetDatabaseEdition", "keywords_found": ["seed", "data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "IoWMIQueryAllData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "PoSetThermalActiveCooling", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "SePrivilegeCheck", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "FsRtlPrivateLock", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "ExConvertFastResourceExclusiveToShared", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "CcIsThereDirtyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "MmIsIoSpaceActive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "EtwActivityIdControl", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "KeQueryActiveProcessorCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "r\"IV HMHURHUPI", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "D$0HD$(HT$ Iv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "IoSetActivityIdIrp", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": ".data$dk03$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "SkReleasePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "LKMDTEL: WerLiveCancelReport failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 48}, {"raw_string": "IoReportInterruptActive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "SeSinglePrivilegeCheck", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "ExRealTimeIsUniversal", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "Failed to get the pointer to index data, index tagid 0x%lx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 58}, {"raw_string": ".rdata$zETW0", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "Driver Verifier: Failed to delete VrfPersistThroughUpgrade key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 86}, {"raw_string": ".data$dk12$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "SdbpReadMappedData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "LKMDTEL: LkmdTelSubmitReport: WerLiveKernelCloseHandle failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 74}, {"raw_string": "MmPageEntireDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "DbgkWerCaptureLiveKernelDump", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "LHD$ L+IVLI", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": ".rdata$zz$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "ZwModifyDriverEntry", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "CcUnpinDataForThread", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "SdbpGetMappedData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "ExReleaseAutoExpandPushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "IoIncrementKeepAliveCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "VfFailDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "D$(fL$ MˋU$IV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "RtlTraceDatabaseEnumerate", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "\"AIV#Hu?Hl$pHH\\$hH0A_A^A\\_^H", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "ExGetExclusiveWaiterCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "CmUnregisterMachineHiveLoadedNotification", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 41}, {"raw_string": "WheaHwErrorReportMarkAsCriticalDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 43}, {"raw_string": "WheaHwErrorReportSetFatalSeverityDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 45}, {"raw_string": "SdbpOpenCompressedDatabase failed to allocate expanded buffer - out of memory", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 77}, {"raw_string": "Failed to get database type [%x]", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "ExEnterPriorityRegionAndAcquireResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 48}, {"raw_string": ".rdata$zzzdbg", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": ".data$pr01$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": ".data$dk04$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "ExReleaseCacheAwarePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "RtlTraceDatabaseDestroy", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ExIsFastResourceHeldExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "PoLatencySensitivityHint", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "HvlQueryActiveHypervisorProcessorCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "D$ HD$0HIIv(LL+I*IILI", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "CmRegisterMachineHiveLoadedNotification", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": ".data$pr03$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "HvlQueryActiveProcessors", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "HPH3H#H3H?IV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "Driver Verifier: Failed to delete VerifierLwspPoolTags key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 82}, {"raw_string": "ExAcquireAutoExpandPushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "SdbQueryDataEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "RtlTraceDatabaseUnlock", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "ExQueryWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": ".data$dk11$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "AslEnvGetSysNativeDirPathForGuestBuf", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "KsrInitPageDatabase", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "Invalid COR20 Metadata virtual address encountered", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 50}, {"raw_string": "ExAcquireSharedStarveExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "MmResetDriverPaging", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "Trying to read mapped data past the end of the database offset 0x%x size 0x%x", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 77}, {"raw_string": "Driver Verifier: Failed to delete VerifierTriageContext key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 83}, {"raw_string": "InitErrorReportDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "WerLiveKernelCreateReport", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "RtlSetActiveConsoleId", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "KeGetEffectiveIrql", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "CcSetLoggedDataThreshold", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "MmLockPagableDataSection", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "SdbpOpenDatabaseInMemory", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "ExAcquireFastResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "Failed to get TAG_REG_VALUE_DATA_DWORD", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "ZwUnloadDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": ".rdata$zETW2", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "DBGK: Full Live Kernel Dumps are disabled. Failing request.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 59}, {"raw_string": ".data$zz$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "IoSynchronousCallDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "E3E33IvE3I违", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "SdbOpenDatabaseEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "WerLiveKernelOpenDumpFile", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "IoCreateDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "FsRtlSetDriverBacking", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "RtlUnlockBootStatusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ExTryToAcquireResourceExclusiveLite", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "ExTryAcquirePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "Failed to get TAG_REG_VALUE_DATA_SZ", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "Invalid COR20 Metadata signature encountered", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 44}, {"raw_string": "PoFxActivateComponent", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "InbvIsBootDriverInstalled", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "Trace database: failing attempt to save biiiiig trace (size %u)", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 63}, {"raw_string": "ExfAcquirePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "KdReceivePacket", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "SdbGetDatabaseMatchEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "Driver Verifier: Clearing Verifier options from Registry for preventing recursive crash.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 88}], "pattern_counts": {"vault": 0, "KeyringController": 0, "data": 97, "salt": 0, "iv": 203, "cipher": 0, "mnemonic": 0, "seed": 0}, "total_patterns": 300}, {"file": "000005_2.ldb", "size": 1100324, "strings_count": 3760, "metamask_data": [{"raw_string": ".rdata$voltmd", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "H!}IVhLEHM!", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": ".rdata$zETW9", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": ".rdata$zzzdbg", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": ".rdata$zETW1", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "RoActivateInstance", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": ".?AV<lambda_3>@Private@@", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": ".?AV<lambda_1>@?1???0?$DependencyProvider@VXamlRuntimeType@Private@@@DependencyLocator@@QEAA@V?$function@$$A6A?AV?$shared_ptr@VXamlRuntimeType@Private@@@std@@XZ@std@@W4StoragePolicyFlags@2@@Z@", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 192}, {"raw_string": "DllGetActivationFactory", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ucII.IVIIIZII\"", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "AcquireSRWLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": ".rdata$zETW2", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "ReleaseSRWLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "RoGetActivationFactory", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "TryAcquireSRWLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "PartA_PrivTags", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": ".rdata$zETW0", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}], "pattern_counts": {"vault": 0, "KeyringController": 0, "data": 30, "salt": 0, "iv": 12, "cipher": 0, "mnemonic": 0, "seed": 0}, "total_patterns": 42}, {"file": "000005_3.ldb", "size": 3668724, "strings_count": 13224, "metamask_data": [{"raw_string": "RtlTraceDatabaseLock", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "SeFreePrivileges", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "IoLoadCrashDumpDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": ".data$pr12$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "WheaAddErrorSourceDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "ExReleaseFastResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "Driver Verifier: Failed to delete VerifyDriversSuppress key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 83}, {"raw_string": "SeAppendPrivileges", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "MmProtectDriverSection", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "WerLiveKernelSubmitReport", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "SdbGetDatabaseID", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "ExEnterCriticalRegionAndAcquireSharedWaitForExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 53}, {"raw_string": ".rdata$.text$lp01ntkrnlmp.exe!20_pri7", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 37}, {"raw_string": ".data$dk01$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "IHD$HM~8MV MF(MN0Iv8C", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "ExReleaseCacheAwarePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "VfQueryDriverContext", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "Failed to open database", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "SdbGetPathCustomSdb failed to get the database path.", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 52}, {"raw_string": "ExAcquireSpinLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "E3IIVHhHD$xHH", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "MmIsDriverVerifying", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "IoSetActivityIdThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "PsGetEffectiveServerSilo", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "Can't read database header", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "WheaHwErrorReportSetSeverityDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 40}, {"raw_string": "Failed to read database header", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "PopCoalescing: Coalescing timer activated", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 41}, {"raw_string": "ExEnterCriticalRegionAndAcquireResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 48}, {"raw_string": "IoDriverObjectType", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "ExTryAcquireSpinLockExclusiveAtDpcLevel", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "IoGetDeviceInterfacePropertyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "IoWMIQueryAllDataMultiple", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "KsrCleanupPageDatabase", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "Driver Verifier: Failed to delete VerifyDrivers key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 75}, {"raw_string": "IoGetDriverObjectExtension", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "CcUnpinData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "RtlTraceDatabaseValidate", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "Driver Verifier: Failed to delete VerifierTipLimitNumerator key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 87}, {"raw_string": "KsrPersistMemoryWithMetadata", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "RtlValidRelativeSecurityDescriptor", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "IoIsActivityTracingEnabled", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "ExAcquireFastResourceSharedStarveExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 42}, {"raw_string": "PsGetEffectiveContainerId", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "ZwLockProductActivationKeys", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "Failed to get TAG_REG_VALUE_DATA_QWORD", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "ExAcquireSharedWaitForExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "WheaReportHwErrorDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "Failed to get the database id", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "ZwEnumerateDriverEntries", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "Unstructered_Data_Too_Soon", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "ZwSetDriverEntryOrder", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "RtlImageDirectoryEntryToData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "Driver Verifier: Failed to delete VerifierTipSparseness key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 83}, {"raw_string": "ZwQueryDriverEntryOrder", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ZwAlpcSendWaitReceivePort", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "ExActivationObjectType", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "WheaAddErrorSourceDeviceDriverV1", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "RtlSelfRelativeToAbsoluteSD2", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": ".rdata$01$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "IoOpenDriverRegistryKey", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "KeGetRecommendedSharedDataAlignment", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "CcGetFlushedValidData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "KeAddTriageDumpDataBlock", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "WheaHwErrorReportSetSectionNameDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 43}, {"raw_string": "LpcSendWaitReceivePort", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "WheaHwErrorReportSubmitDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "Driver Verifier: Failed to set VerifierOptionFlags key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 78}, {"raw_string": "NtAdjustPrivilegesToken", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "SdbQueryDataExTagID", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "HalSetBusDataByOffset", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "Error reading data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": ".rdata$12$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "Driver Verifier: Failed to open Memory Management key with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 71}, {"raw_string": "ZwUpdateWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "RtlTraceDatabaseCreate", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "FsRtlCreateSectionForDataScan", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "SdbGetBinaryTagData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "Unknown registry value data type", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "KeQueryEffectivePriorityThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "Attempt to read past the end of the database offset 0x%lx size 0x%lx (0x%lx)", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 76}, {"raw_string": "ExAcquireCacheAwarePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "AslpFileHasActiveMarkWrapper", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "WheaHwErrorReportGetLogDataBufferDeviceDriver", "keywords_found": ["iv", "data"], "json_data": null, "is_json": false, "length": 45}, {"raw_string": "Driver Verifier: Failed to delete VerifierRandomTargets key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 83}, {"raw_string": "ZwQueryWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "RtlTraceDatabaseAdd", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "DBGK: IoCaptureLiveDump failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 43}, {"raw_string": "SkAcquirePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": ".data$00$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "ExTryAcquireAutoExpandPushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "ExTryConvertPushLockSharedToExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": ".data$01$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "PoSetThermalPassiveCooling", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "CcIsThereDirtyDataEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "IoGetActivityIdThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "WheaReportFatalHwErrorDeviceDriverEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "TtmNotifyDeviceArrival", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "RtlGetActiveConsoleId", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "ZwDeleteWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "SdbGetTagDataSize", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "SdbpOpenLocalDatabaseEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "PoCallDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "ExTryAcquireCacheAwarePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 41}, {"raw_string": ".data$12$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "MmIsDriverVerifyingByAddress", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "Cannot resolve database, the path length is 0x%lx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 49}, {"raw_string": "WheaRemoveErrorSourceDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 33}, {"raw_string": "Database \"%ws\" is not of the same type as the main EXE", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 54}, {"raw_string": "KeInitializeTriageDumpDataArray", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "LKMDTEL: LkmdTelSubmitReport: WerLiveKernelCancelReport failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 75}, {"raw_string": "IoTransferActivityId", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "M4IV HMHURH", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "Failed to get pointer to the index data tagid x%lx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 50}, {"raw_string": "ExAcquireCacheAwarePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "CcPinMappedData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "Passive-level ISR watchdog timeout! Interrupt: %p", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 49}, {"raw_string": "Failed to read GUID of the database", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "IoQueryFullDriverPath", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "ExTryConvertSharedSpinLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "RtlLockBootStatusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "LKMDTEL: LkmdTelSubmitReport: WerLiveKernelSubmitReport failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 75}, {"raw_string": "L\\$PIv@Ik@I[8IA_A^A]_^L$", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "Failed to get TAG_REG_VALUE_DATA_BINARY", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "ExReleaseSpinLockExclusiveFromDpcLevel", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "IoSetDevicePropertyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "Driver Verifier: Failed to delete VerifierTipLimitDenominator key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 89}, {"raw_string": "IoClearActivityIdThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "IoReportInterruptInactive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": ".data$dk00$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "AslpFileHasActiveMarkWrapper failed [%x]", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 40}, {"raw_string": "IoRegisterDriverReinitialization", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "Failed to get database tag, db is corrupt", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 41}, {"raw_string": "WheaReportHwErrorDeviceDriverEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "HalSetBusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "KeQueryActiveGroupCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "Error reading size data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ExReleasePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "ExIsResourceAcquiredExclusiveLite", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 33}, {"raw_string": "Failed to get the database ID", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": ".rdata$00$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "SdbpOpenCompressedDatabase failed to open compressed database.", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 62}, {"raw_string": "DŋH`H0H+uIv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "ZwDeleteDriverEntry", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "DBGK: Could not allocate IoLivedumpControl", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 42}, {"raw_string": "WheaAddHwErrorReportSectionDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": "H\\$XHl$`Ht$hH A_A^A]A\\_I^0Iv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "KseQueryDeviceData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "IofCallDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "CcWaitForCurrentLazyWriterActivity", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "IoUnregisterBootDriverCallback", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "LKMDTEL: WerPolicy is WerLiveKernelPolicyNoDump, no dump is allowed.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 68}, {"raw_string": "RtlDeriveCapabilitySidsFromName", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "CcZeroDataOnDisk", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "Iv3DL}M;sLL+uoI0I", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "ExFetchLicenseData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "IoRegisterBootDriverCallback", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "ZwAddDriverEntry", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "SdbResolveDatabaseEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "ExConvertPushLockExclusiveToShared", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "Unable to attach to active session", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 34}, {"raw_string": "Failed to read value data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "No DATABASE tag found", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "IoSetDeviceInterfacePropertyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "IoAllocateDriverObjectExtension", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": ".rdata$09$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "ExRaiseDatatypeMisalignment", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "Failed to read database id 0x%lx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "ExAcquirePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "KiCheckForKernelApcDelivery", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "WheaCreateHwErrorReportDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "SdbGetDatabaseMatch", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "SdbInitDatabaseInMemory", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "Flags:%d; DatabasePath:%ws", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "LKMDTEL: LkmdTelpWriteDumpFile: WerLiveKernelOpenDumpFile failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 77}, {"raw_string": "KeQueryActiveProcessorCountEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "ExAcquireResourceExclusiveLite", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "KeQueryActiveProcessorAffinity", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "KseQueryDeviceDataList", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "SdbpGetMappedTagData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "RtlTraceDatabaseFind", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "LKMDTEL: WerLiveKernelCreateReport failed with status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 58}, {"raw_string": "SdbpOpenCompressedDatabase", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "KeQueryNodeActiveAffinity", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": ".rdata$zETW9", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "HalGetBusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "IoGetDriverDirectory", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "ZwLoadDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "RtlAbsoluteToSelfRelativeSD", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "I?HI#HHH3I#H3IV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "ExTryToConvertFastResourceSharedToExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 43}, {"raw_string": "InbvEnableBootDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "ExAcquireSpinLockExclusiveAtDpcLevel", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "SdbpReadTagData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "IoRegisterBootDriverReinitialization", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "HtbIvH4M<II", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": ".rdata$03$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": ".data$pr00$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "MmIsRecursiveIoFault", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "RtlSelfRelativeToAbsoluteSD", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "WheaHwErrorReportAbandonDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "DBGK: Calling IoCaptureLiveDump", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "IoPropagateActivityIdToThread", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "Driver at fault: %s.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "KeQueryActiveProcessors", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "FsRtlAcquireFileExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "IoGetActivityIdIrp", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "RtlIsNtDdiVersionAvailable", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "ExConvertExclusiveToSharedLite", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": ".rdata$zETW1", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "MmIsDriverSuspectForVerifier", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "Driver Verifier: Failed to delete RuleClasses key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 73}, {"raw_string": "KsrQueryMetadata", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "IoGetDevicePropertyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ExUpdateLicenseData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "The database has more matches than SDB_MAX_EXES", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 47}, {"raw_string": "AslpFileHasActiveMarkWrapper failed (FileSize: %I64u) [%x]", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 58}, {"raw_string": "ZwAdjustPrivilegesToken", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "IoCallDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "Error reading tag data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "KiBugCheckData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "WerLiveKernelCloseHandle", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "IoDeleteDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "ExfReleasePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "RtlGetSetBootStatusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "HalGetBusDataByOffset", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "CcSetDirtyPinnedData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "ExReleaseDisownedFastResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "IvE3xBE3Hӹ$", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "ExReleaseSpinLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "SePrivilegeObjectAuditAlarm", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "TmIsTransactionActive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "Driver Verifier: Failed to delete XdvVerifierOptions key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 80}, {"raw_string": "Driver Verifier: Failed to delete VerifyDriverLevel key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 79}, {"raw_string": "IoDecrementKeepAliveCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "WerLiveKernelCancelReport", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "Unable to open main database", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "WerLiveKernelInitSystem", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "HalPrivateDispatchTable", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "Error getting ptr to tag data", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "SdbGetDatabaseEdition", "keywords_found": ["seed", "data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "IoWMIQueryAllData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "PoSetThermalActiveCooling", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "SePrivilegeCheck", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "FsRtlPrivateLock", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 16}, {"raw_string": "ExConvertFastResourceExclusiveToShared", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "CcIsThereDirtyData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "MmIsIoSpaceActive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "EtwActivityIdControl", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "KeQueryActiveProcessorCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "r\"IV HMHURHUPI", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "D$0HD$(HT$ Iv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "IoSetActivityIdIrp", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": ".data$dk03$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "SkReleasePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 26}, {"raw_string": "LKMDTEL: WerLiveCancelReport failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 48}, {"raw_string": "IoReportInterruptActive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "SeSinglePrivilegeCheck", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "ExRealTimeIsUniversal", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "Failed to get the pointer to index data, index tagid 0x%lx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 58}, {"raw_string": ".rdata$zETW0", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "Driver Verifier: Failed to delete VrfPersistThroughUpgrade key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 86}, {"raw_string": ".data$dk12$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "SdbpReadMappedData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "LKMDTEL: LkmdTelSubmitReport: WerLiveKernelCloseHandle failed, status 0x%X", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 74}, {"raw_string": "MmPageEntireDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "DbgkWerCaptureLiveKernelDump", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "LHD$ L+IVLI", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": ".rdata$zz$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "ZwModifyDriverEntry", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "CcUnpinDataForThread", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 20}, {"raw_string": "SdbpGetMappedData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "ExReleaseAutoExpandPushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "IoIncrementKeepAliveCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "VfFailDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "D$(fL$ MˋU$IV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": "RtlTraceDatabaseEnumerate", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "\"AIV#Hu?Hl$pHH\\$hH0A_A^A\\_^H", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 28}, {"raw_string": "ExGetExclusiveWaiterCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "CmUnregisterMachineHiveLoadedNotification", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 41}, {"raw_string": "WheaHwErrorReportMarkAsCriticalDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 43}, {"raw_string": "WheaHwErrorReportSetFatalSeverityDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 45}, {"raw_string": "SdbpOpenCompressedDatabase failed to allocate expanded buffer - out of memory", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 77}, {"raw_string": "Failed to get database type [%x]", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 32}, {"raw_string": "ExEnterPriorityRegionAndAcquireResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 48}, {"raw_string": ".rdata$zzzdbg", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 13}, {"raw_string": ".data$pr01$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": ".data$dk04$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "ExReleaseCacheAwarePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "RtlTraceDatabaseDestroy", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ExIsFastResourceHeldExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 29}, {"raw_string": "PoLatencySensitivityHint", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "HvlQueryActiveHypervisorProcessorCount", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "D$ HD$0HIIv(LL+I*IILI", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "CmRegisterMachineHiveLoadedNotification", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 39}, {"raw_string": ".data$pr03$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "HvlQueryActiveProcessors", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "HPH3H#H3H?IV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "Driver Verifier: Failed to delete VerifierLwspPoolTags key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 82}, {"raw_string": "ExAcquireAutoExpandPushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "SdbQueryDataEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "RtlTraceDatabaseUnlock", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 22}, {"raw_string": "ExQueryWnfStateData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": ".data$dk11$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "AslEnvGetSysNativeDirPathForGuestBuf", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 36}, {"raw_string": "KsrInitPageDatabase", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "Invalid COR20 Metadata virtual address encountered", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 50}, {"raw_string": "ExAcquireSharedStarveExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "MmResetDriverPaging", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "Trying to read mapped data past the end of the database offset 0x%x size 0x%x", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 77}, {"raw_string": "Driver Verifier: Failed to delete VerifierTriageContext key value with status: 0x%x", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 83}, {"raw_string": "InitErrorReportDeviceDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "WerLiveKernelCreateReport", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "RtlSetActiveConsoleId", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "KeGetEffectiveIrql", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 18}, {"raw_string": "CcSetLoggedDataThreshold", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "MmLockPagableDataSection", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "SdbpOpenDatabaseInMemory", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 24}, {"raw_string": "ExAcquireFastResourceExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 30}, {"raw_string": "Failed to get TAG_REG_VALUE_DATA_DWORD", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 38}, {"raw_string": "ZwUnloadDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": ".rdata$zETW2", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "DBGK: Full Live Kernel Dumps are disabled. Failing request.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 59}, {"raw_string": ".data$zz$brc", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 12}, {"raw_string": "IoSynchronousCallDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "E3E33IvE3I违", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "SdbOpenDatabaseEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 17}, {"raw_string": "WerLiveKernelOpenDumpFile", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "IoCreateDriver", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 14}, {"raw_string": "FsRtlSetDriverBacking", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "RtlUnlockBootStatusData", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 23}, {"raw_string": "ExTryToAcquireResourceExclusiveLite", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "ExTryAcquirePushLockExclusiveEx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 31}, {"raw_string": "Failed to get TAG_REG_VALUE_DATA_SZ", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 35}, {"raw_string": "Invalid COR20 Metadata signature encountered", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 44}, {"raw_string": "PoFxActivateComponent", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "InbvIsBootDriverInstalled", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 25}, {"raw_string": "Trace database: failing attempt to save biiiiig trace (size %u)", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 63}, {"raw_string": "ExfAcquirePushLockExclusive", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 27}, {"raw_string": "KdReceivePacket", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 15}, {"raw_string": "SdbGetDatabaseMatchEx", "keywords_found": ["data"], "json_data": null, "is_json": false, "length": 21}, {"raw_string": "Driver Verifier: Clearing Verifier options from Registry for preventing recursive crash.", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 88}], "pattern_counts": {"vault": 0, "KeyringController": 0, "data": 97, "salt": 0, "iv": 203, "cipher": 0, "mnemonic": 0, "seed": 0}, "total_patterns": 300}, {"file": "000982.ldb", "size": 2120483, "strings_count": 118, "metamask_data": [], "pattern_counts": {"vault": 0, "KeyringController": 0, "data": 0, "salt": 0, "iv": 1, "cipher": 0, "mnemonic": 0, "seed": 0}, "total_patterns": 1}, {"file": "011784 (2).ldb", "size": 4067080, "strings_count": 10911, "metamask_data": [{"raw_string": "H0[^_A^]H H0[^_A^]UAVWVSH0Hi Hl$ HmPLuIv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 40}, {"raw_string": "AV(;P(uGIV H;P u=IV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "IV@HUu0HE H", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "HD$XIV@HT$PHx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}], "pattern_counts": {"vault": 0, "KeyringController": 0, "data": 0, "salt": 0, "iv": 19, "cipher": 0, "mnemonic": 0, "seed": 0}, "total_patterns": 19}, {"file": "011784.ldb", "size": 4067080, "strings_count": 10911, "metamask_data": [{"raw_string": "H0[^_A^]H H0[^_A^]UAVWVSH0Hi Hl$ HmPLuIv", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 40}, {"raw_string": "AV(;P(uGIV H;P u=IV", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 19}, {"raw_string": "IV@HUu0HE H", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 11}, {"raw_string": "HD$XIV@HT$PHx", "keywords_found": ["iv"], "json_data": null, "is_json": false, "length": 13}], "pattern_counts": {"vault": 0, "KeyringController": 0, "data": 0, "salt": 0, "iv": 19, "cipher": 0, "mnemonic": 0, "seed": 0}, "total_patterns": 19}, {"file": "157836 (2).ldb", "size": 11971938, "strings_count": 0, "metamask_data": [], "pattern_counts": {"vault": 0, "KeyringController": 0, "data": 0, "salt": 0, "iv": 0, "cipher": 0, "mnemonic": 0, "seed": 0}, "total_patterns": 0}, {"file": "157836.ldb", "size": 11971938, "strings_count": 0, "metamask_data": [], "pattern_counts": {"vault": 0, "KeyringController": 0, "data": 0, "salt": 0, "iv": 0, "cipher": 0, "mnemonic": 0, "seed": 0}, "total_patterns": 0}, {"file": "CURRENT", "size": 16, "strings_count": 1, "metamask_data": [], "pattern_counts": {"vault": 0, "KeyringController": 0, "data": 0, "salt": 0, "iv": 0, "cipher": 0, "mnemonic": 0, "seed": 0}, "total_patterns": 0}]