#!/usr/bin/env python3
"""
Try Alternative IV/Salt Combinations
Generate alternative vault combinations to try in the decryptor
"""

import json
import os

def load_candidates():
    """Load the IV and salt candidates we found"""
    try:
        with open('iv_salt_search_results.json', 'r') as f:
            data = json.load(f)
        return data['iv_candidates'], data['salt_candidates']
    except FileNotFoundError:
        print("iv_salt_search_results.json not found!")
        return [], []

def generate_alternative_vaults():
    """Generate alternative vault combinations"""
    
    # Our known good data field
    data_field = "2849236c3fab4d27c7026c1d4dcb2602646dec9751e763dba37bdf8ff9406ad9e530ee5db382f413001aeb06a53ed9027d831179727b0865a8918da3edbebcf9b14ed44ce6cbaced4bb1bdb7f1447e6cc254b332051512bd7af426fb8f401378cd2bf5983ca01c64b92ecf032ea15d1721d03f482d7ce6e74fef6d55e702f46980c82b5a84031900b1c9e59e7c97fbec7e8f323a97a7e36cc88be0f1d45b7ff585ac54bd407b22b4154aacc8f6d7ebf48e1d814cc5ed20f8037e0a79715eef29be32806a1d58bb7c5da76f550aa3d8a1fbff0eb19ccb1a313d55cda56c9ec2ef29632387fe8d76e3c0468043e8f663f4860ee12bf2d5b0b7474d6e694f91e6dcc4024fffffffffffffff"
    
    iv_candidates, salt_candidates = load_candidates()
    
    if not iv_candidates or not salt_candidates:
        print("No candidates found!")
        return
    
    print(f"Generating alternative vault combinations...")
    print(f"IV candidates: {len(iv_candidates)}")
    print(f"Salt candidates: {len(salt_candidates)}")
    
    # Sort candidates by distance (closest first)
    iv_candidates.sort(key=lambda x: x['distance_from_target'])
    salt_candidates.sort(key=lambda x: x['distance_from_target'])
    
    # Generate top combinations
    combinations = []
    
    # Try the top 10 IV candidates with top 10 salt candidates
    for i, iv_candidate in enumerate(iv_candidates[:10]):
        for j, salt_candidate in enumerate(salt_candidates[:10]):
            vault_data = {
                "data": data_field,
                "iv": iv_candidate['value'],
                "salt": salt_candidate['value']
            }
            
            combinations.append({
                'combination_id': f"IV{i+1}_SALT{j+1}",
                'iv_distance': iv_candidate['distance_from_target'],
                'salt_distance': salt_candidate['distance_from_target'],
                'total_distance': iv_candidate['distance_from_target'] + salt_candidate['distance_from_target'],
                'vault_data': vault_data
            })
    
    # Sort by total distance (closest combinations first)
    combinations.sort(key=lambda x: x['total_distance'])
    
    print(f"\nGenerated {len(combinations)} combinations")
    print(f"Saving top 20 combinations...")
    
    # Save top 20 combinations
    for i, combo in enumerate(combinations[:20]):
        # Save as JSON
        filename = f"vault_combo_{i+1:02d}.json"
        with open(filename, 'w') as f:
            json.dump(combo['vault_data'], f, indent=2)
        
        # Save as text for copy-paste
        text_filename = f"vault_combo_{i+1:02d}.txt"
        with open(text_filename, 'w') as f:
            f.write(json.dumps(combo['vault_data']))
        
        print(f"Combo {i+1:2d}: {combo['combination_id']} (total distance: {combo['total_distance']:4d}) -> {filename}")
    
    # Show the top 5 for immediate testing
    print(f"\n🎯 TOP 5 COMBINATIONS TO TRY:")
    print(f"=" * 60)
    
    for i, combo in enumerate(combinations[:5]):
        print(f"\nCombination {i+1} ({combo['combination_id']}):")
        print(f"IV distance: {combo['iv_distance']}, Salt distance: {combo['salt_distance']}")
        print(f"Total distance: {combo['total_distance']}")
        print(f"Vault data:")
        print(json.dumps(combo['vault_data']))
        print(f"-" * 60)
    
    # Save summary
    summary = {
        'total_combinations': len(combinations),
        'top_20_saved': True,
        'combinations_summary': [
            {
                'rank': i+1,
                'combination_id': combo['combination_id'],
                'total_distance': combo['total_distance'],
                'filename': f"vault_combo_{i+1:02d}.txt"
            }
            for i, combo in enumerate(combinations[:20])
        ]
    }
    
    with open('vault_combinations_summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n📋 INSTRUCTIONS:")
    print(f"1. Try combination 1 first (closest to our data)")
    print(f"2. If it doesn't work, try combination 2, then 3, etc.")
    print(f"3. Copy the vault data from vault_combo_01.txt, vault_combo_02.txt, etc.")
    print(f"4. Paste into the MetaMask vault decryptor")
    print(f"5. Use the same password you tried before")
    
    print(f"\n💡 TIPS:")
    print(f"- The first few combinations are most likely to work")
    print(f"- If none work, double-check your password")
    print(f"- The vault data structure is correct (no 'Invalid JSON' errors)")

def show_next_combinations():
    """Show the next few combinations to try"""
    try:
        with open('vault_combinations_summary.json', 'r') as f:
            summary = json.load(f)
        
        print(f"Available combinations:")
        for combo in summary['combinations_summary'][:10]:
            print(f"  {combo['rank']:2d}. {combo['combination_id']} (distance: {combo['total_distance']:4d}) -> {combo['filename']}")
        
        print(f"\nTo try combination N, copy content from vault_combo_N.txt")
        
    except FileNotFoundError:
        print("No combinations summary found. Run generate_alternative_vaults() first.")

def main():
    """Main function"""
    print("Alternative Vault Combination Generator")
    print("=" * 50)
    
    if not os.path.exists('iv_salt_search_results.json'):
        print("❌ iv_salt_search_results.json not found!")
        print("Please run the find_iv_salt.py script first.")
        return
    
    generate_alternative_vaults()
    
    print(f"\n🎯 IMMEDIATE NEXT STEPS:")
    print(f"1. Copy the content from vault_combo_01.txt")
    print(f"2. Paste it into the MetaMask vault decryptor")
    print(f"3. Try your password again")
    print(f"4. If it doesn't work, try vault_combo_02.txt, then 03.txt, etc.")

if __name__ == "__main__":
    main()
