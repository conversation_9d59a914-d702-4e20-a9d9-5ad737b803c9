#!/usr/bin/env python3
"""
Find and Backup MetaMask Data on Your Other Laptop
Run this script on the laptop where you originally had MetaMask
"""

import os
import shutil
import time
from datetime import datetime, timedelta
from pathlib import Path

def find_metamask_locations():
    """Find MetaMask storage locations"""
    username = os.getenv('USERNAME')
    
    locations = {
        'Chrome_Default': f'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn',
        'Chrome_Profile1': f'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Profile 1\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn',
        'Edge_Default': f'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn',
        'Edge_Profile1': f'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data\\Profile 1\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn',
        'Brave_Default': f'C:\\Users\\<USER>\\AppData\\Local\\BraveSoftware\\Brave-Browser\\User Data\\Default\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn',
    }
    
    return locations

def check_file_dates(file_path, days_ago=3):
    """Check if file was modified within the last N days"""
    try:
        file_time = os.path.getmtime(file_path)
        file_date = datetime.fromtimestamp(file_time)
        cutoff_date = datetime.now() - timedelta(days=days_ago)
        return file_date >= cutoff_date
    except:
        return False

def backup_metamask_data():
    """Find and backup MetaMask data"""
    print("🔍 MetaMask Data Recovery Tool")
    print("=" * 50)
    print("Looking for MetaMask data on this laptop...")
    print()
    
    locations = find_metamask_locations()
    found_data = []
    
    # Create backup directory
    backup_dir = f"MetaMask_Backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    for browser, path in locations.items():
        print(f"🔍 Checking {browser}...")
        print(f"   Path: {path}")
        
        if os.path.exists(path):
            print(f"   ✅ FOUND MetaMask data!")
            
            # Check for .ldb files
            ldb_files = list(Path(path).glob("*.ldb"))
            if ldb_files:
                print(f"   📁 Contains {len(ldb_files)} .ldb files")
                
                # Check dates
                recent_files = []
                for ldb_file in ldb_files:
                    file_time = os.path.getmtime(ldb_file)
                    file_date = datetime.fromtimestamp(file_time)
                    size = ldb_file.stat().st_size
                    
                    print(f"      - {ldb_file.name}: {size:,} bytes, modified {file_date.strftime('%Y-%m-%d %H:%M:%S')}")
                    
                    # Check if modified in last 7 days (to catch your wallet)
                    if check_file_dates(ldb_file, days_ago=7):
                        recent_files.append(ldb_file)
                
                if recent_files:
                    print(f"   🎯 Found {len(recent_files)} recent files!")
                    
                    # Backup this location
                    backup_path = os.path.join(backup_dir, browser)
                    try:
                        shutil.copytree(path, backup_path)
                        found_data.append((browser, path, backup_path))
                        print(f"   ✅ BACKED UP to: {backup_path}")
                    except Exception as e:
                        print(f"   ❌ Backup failed: {e}")
                else:
                    print(f"   ⚠️  No recent files found")
            else:
                print(f"   ❌ No .ldb files found")
        else:
            print(f"   ❌ Path does not exist")
        print()
    
    # Summary
    print("=" * 50)
    print("🎯 RECOVERY SUMMARY")
    print("=" * 50)
    
    if found_data:
        print(f"✅ SUCCESS! Found MetaMask data in {len(found_data)} location(s):")
        print()
        
        for browser, original_path, backup_path in found_data:
            print(f"📁 {browser}:")
            print(f"   Original: {original_path}")
            print(f"   Backup:   {backup_path}")
            print()
        
        print(f"🎯 NEXT STEPS:")
        print(f"1. Copy the entire '{backup_dir}' folder to a USB drive")
        print(f"2. Transfer it to your other laptop")
        print(f"3. Run the vault extraction scripts on the .ldb files")
        print()
        
        print(f"💾 BACKUP LOCATION: {os.path.abspath(backup_dir)}")
        print()
        
        print(f"🔧 EXTRACTION COMMANDS (run on other laptop):")
        print(f"   python search_keyring_in_ldb.py [ldb_file_name]")
        print(f"   python manual_vault_search.py")
        
    else:
        print("❌ No MetaMask data found on this laptop")
        print()
        print("🤔 POSSIBLE REASONS:")
        print("1. MetaMask was installed in a different browser")
        print("2. Different user profile was used")
        print("3. Data was already cleared")
        print()
        print("🔍 MANUAL SEARCH:")
        print("Try searching for 'nkbihfbeogaeaoehlefnkodbefgpgknn' in File Explorer")
        print("This is MetaMask's extension ID")
    
    return found_data

def show_manual_instructions():
    """Show manual recovery instructions"""
    print("\n" + "=" * 50)
    print("📋 MANUAL RECOVERY INSTRUCTIONS")
    print("=" * 50)
    
    username = os.getenv('USERNAME')
    
    print(f"If the automatic backup didn't work, try these manual steps:")
    print()
    print(f"1. Open File Explorer")
    print(f"2. Navigate to: C:\\Users\\<USER>\\AppData\\Local")
    print(f"3. Search for folder containing 'nkbihfbeogaeaoehlefnkodbefgpgknn'")
    print(f"4. Look in these specific locations:")
    print(f"   - Google\\Chrome\\User Data\\Default\\Local Extension Settings\\")
    print(f"   - Microsoft\\Edge\\User Data\\Default\\Local Extension Settings\\")
    print(f"   - BraveSoftware\\Brave-Browser\\User Data\\Default\\Local Extension Settings\\")
    print()
    print(f"5. Copy any folders named 'nkbihfbeogaeaoehlefnkodbefgpgknn' to USB drive")
    print(f"6. Transfer to your other laptop")
    print()
    print(f"💡 TIP: Look for .ldb files modified 2-3 days ago!")

def main():
    """Main function"""
    print("🚀 Starting MetaMask Recovery...")
    print(f"Current user: {os.getenv('USERNAME')}")
    print(f"Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Backup MetaMask data
    found_data = backup_metamask_data()
    
    # Show manual instructions
    show_manual_instructions()
    
    print(f"\n🎯 REMEMBER:")
    print(f"- Don't open MetaMask on this laptop until after recovery")
    print(f"- The .ldb files contain your encrypted wallet data")
    print(f"- Transfer the backup folder to your other laptop")
    print(f"- Run the extraction scripts there")

if __name__ == "__main__":
    main()
