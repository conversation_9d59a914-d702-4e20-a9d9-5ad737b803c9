#!/usr/bin/env python3
"""
MetaMask Vault Decryptor
Attempts to decrypt MetaMask vault data using password
Based on MetaMask's encryption methodology
"""

import json
import base64
import hashlib
import hmac
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend
import getpass
import sys

class MetaMaskDecryptor:
    def __init__(self):
        self.backend = default_backend()
    
    def decrypt_vault_data(self, encrypted_data: str, password: str) -> dict:
        """
        Attempt to decrypt MetaMask vault data
        MetaMask uses AES-GCM encryption with PBKDF2 key derivation
        """
        try:
            # Try to decode as base64
            try:
                decoded_data = base64.b64decode(encrypted_data)
            except:
                # If not base64, try as hex
                try:
                    decoded_data = bytes.fromhex(encrypted_data)
                except:
                    return {"error": "Invalid data format"}
            
            # Try different MetaMask vault formats
            results = []
            
            # Format 1: Try as direct encrypted data
            result1 = self._try_direct_decryption(decoded_data, password)
            if result1:
                results.append({"method": "direct", "result": result1})
            
            # Format 2: Try as JSON structure
            result2 = self._try_json_structure_decryption(encrypted_data, password)
            if result2:
                results.append({"method": "json_structure", "result": result2})
            
            # Format 3: Try different key derivation methods
            result3 = self._try_alternative_kdf(decoded_data, password)
            if result3:
                results.append({"method": "alternative_kdf", "result": result3})
            
            return {"results": results, "success": len(results) > 0}
            
        except Exception as e:
            return {"error": f"Decryption failed: {str(e)}"}
    
    def _try_direct_decryption(self, data: bytes, password: str) -> dict:
        """Try direct decryption assuming the data contains salt, iv, and encrypted content"""
        try:
            # MetaMask typically uses first 32 bytes as salt, next 16 as IV
            if len(data) < 48:  # Need at least salt + iv
                return None
            
            salt = data[:32]
            iv = data[32:48]
            encrypted_content = data[48:]
            
            # Derive key using PBKDF2 (MetaMask standard)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=10000,  # MetaMask default
                backend=self.backend
            )
            key = kdf.derive(password.encode('utf-8'))
            
            # Try AES-GCM decryption
            try:
                cipher = Cipher(algorithms.AES(key), modes.GCM(iv), backend=self.backend)
                decryptor = cipher.decryptor()
                decrypted = decryptor.finalize_with_tag(encrypted_content[-16:], encrypted_content[:-16])
                
                # Try to parse as JSON
                try:
                    result = json.loads(decrypted.decode('utf-8'))
                    return {"type": "AES-GCM", "data": result}
                except:
                    return {"type": "AES-GCM", "data": decrypted.decode('utf-8', errors='ignore')}
            except:
                pass
            
            # Try AES-CBC decryption
            try:
                cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=self.backend)
                decryptor = cipher.decryptor()
                decrypted = decryptor.update(encrypted_content) + decryptor.finalize()
                
                # Remove padding
                padding_length = decrypted[-1]
                decrypted = decrypted[:-padding_length]
                
                try:
                    result = json.loads(decrypted.decode('utf-8'))
                    return {"type": "AES-CBC", "data": result}
                except:
                    return {"type": "AES-CBC", "data": decrypted.decode('utf-8', errors='ignore')}
            except:
                pass
            
            return None
            
        except Exception as e:
            return None
    
    def _try_json_structure_decryption(self, data_str: str, password: str) -> dict:
        """Try to decrypt assuming data is in JSON format with separate salt, iv, data fields"""
        try:
            # Try to parse as JSON first
            try:
                data = json.loads(data_str)
                if not isinstance(data, dict):
                    return None
            except:
                return None
            
            # Look for MetaMask vault structure
            if 'data' in data and 'salt' in data and 'iv' in data:
                encrypted_data = base64.b64decode(data['data'])
                salt = base64.b64decode(data['salt'])
                iv = base64.b64decode(data['iv'])
                
                # Derive key
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=10000,
                    backend=self.backend
                )
                key = kdf.derive(password.encode('utf-8'))
                
                # Try AES-GCM
                try:
                    cipher = Cipher(algorithms.AES(key), modes.GCM(iv), backend=self.backend)
                    decryptor = cipher.decryptor()
                    decrypted = decryptor.finalize_with_tag(encrypted_data[-16:], encrypted_data[:-16])
                    
                    try:
                        result = json.loads(decrypted.decode('utf-8'))
                        return {"type": "JSON-AES-GCM", "data": result}
                    except:
                        return {"type": "JSON-AES-GCM", "data": decrypted.decode('utf-8', errors='ignore')}
                except:
                    pass
                
                # Try AES-CBC
                try:
                    cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=self.backend)
                    decryptor = cipher.decryptor()
                    decrypted = decryptor.update(encrypted_data) + decryptor.finalize()
                    
                    # Remove padding
                    padding_length = decrypted[-1]
                    decrypted = decrypted[:-padding_length]
                    
                    try:
                        result = json.loads(decrypted.decode('utf-8'))
                        return {"type": "JSON-AES-CBC", "data": result}
                    except:
                        return {"type": "JSON-AES-CBC", "data": decrypted.decode('utf-8', errors='ignore')}
                except:
                    pass
            
            return None
            
        except Exception as e:
            return None
    
    def _try_alternative_kdf(self, data: bytes, password: str) -> dict:
        """Try alternative key derivation methods"""
        try:
            if len(data) < 48:
                return None
            
            salt = data[:32]
            iv = data[32:48]
            encrypted_content = data[48:]
            
            # Try different iteration counts
            for iterations in [1000, 5000, 10000, 100000]:
                try:
                    kdf = PBKDF2HMAC(
                        algorithm=hashes.SHA256(),
                        length=32,
                        salt=salt,
                        iterations=iterations,
                        backend=self.backend
                    )
                    key = kdf.derive(password.encode('utf-8'))
                    
                    # Try AES-GCM
                    try:
                        cipher = Cipher(algorithms.AES(key), modes.GCM(iv), backend=self.backend)
                        decryptor = cipher.decryptor()
                        decrypted = decryptor.finalize_with_tag(encrypted_content[-16:], encrypted_content[:-16])
                        
                        try:
                            result = json.loads(decrypted.decode('utf-8'))
                            return {"type": f"ALT-KDF-{iterations}-GCM", "data": result}
                        except:
                            return {"type": f"ALT-KDF-{iterations}-GCM", "data": decrypted.decode('utf-8', errors='ignore')}
                    except:
                        pass
                except:
                    continue
            
            return None
            
        except Exception as e:
            return None

def main():
    """Main decryption function"""
    print("MetaMask Vault Decryptor")
    print("=" * 40)
    
    # Load the extracted data
    try:
        with open('focused_vault_extraction.json', 'r') as f:
            extraction_data = json.load(f)
    except FileNotFoundError:
        print("Error: focused_vault_extraction.json not found!")
        print("Please run the vault extractor first.")
        return
    
    encrypted_data_list = extraction_data.get('promising_data', {}).get('encrypted_data', [])
    
    if not encrypted_data_list:
        print("No encrypted data found to decrypt!")
        return
    
    print(f"Found {len(encrypted_data_list)} encrypted data entries to try")
    
    # Get password from user
    password = getpass.getpass("Enter your MetaMask password: ")
    
    if not password:
        print("No password provided!")
        return
    
    decryptor = MetaMaskDecryptor()
    
    print(f"\nAttempting decryption with password...")
    print("=" * 50)
    
    successful_decryptions = []
    
    for i, encrypted_entry in enumerate(encrypted_data_list):
        print(f"\nTrying encrypted data entry {i+1}/{len(encrypted_data_list)}")
        print(f"Data length: {encrypted_entry['encoded_length']} chars")
        print(f"Decoded length: {encrypted_entry['decoded_length']} bytes")
        
        result = decryptor.decrypt_vault_data(encrypted_entry['data'], password)
        
        if result.get('success'):
            print(f"✅ SUCCESS! Found {len(result['results'])} successful decryption(s)")
            successful_decryptions.append({
                'entry_index': i,
                'entry_data': encrypted_entry,
                'decryption_results': result['results']
            })
            
            for j, decrypt_result in enumerate(result['results']):
                print(f"  Method {j+1}: {decrypt_result['method']}")
                print(f"  Type: {decrypt_result['result']['type']}")
                
                # Check if we found seed phrases or mnemonic
                data = decrypt_result['result']['data']
                if isinstance(data, dict):
                    if 'mnemonic' in str(data).lower() or 'seed' in str(data).lower():
                        print(f"  🎯 CONTAINS SEED/MNEMONIC DATA!")
                    print(f"  Keys: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")
                elif isinstance(data, str):
                    if 'mnemonic' in data.lower() or 'seed' in data.lower():
                        print(f"  🎯 CONTAINS SEED/MNEMONIC DATA!")
                    print(f"  Preview: {data[:100]}...")
        else:
            print(f"❌ Failed to decrypt")
            if 'error' in result:
                print(f"  Error: {result['error']}")
    
    # Summary
    print(f"\n{'='*60}")
    print("DECRYPTION SUMMARY")
    print(f"{'='*60}")
    
    if successful_decryptions:
        print(f"✅ Successfully decrypted {len(successful_decryptions)} entries!")
        
        # Save results
        output_file = 'decryption_results.json'
        with open(output_file, 'w') as f:
            json.dump(successful_decryptions, f, indent=2, default=str)
        
        print(f"Results saved to: {output_file}")
        
        # Show the most promising results
        for success in successful_decryptions:
            for result in success['decryption_results']:
                data = result['result']['data']
                if isinstance(data, dict):
                    print(f"\n🔍 Decrypted data structure:")
                    for key, value in data.items():
                        if isinstance(value, str) and len(value) > 50:
                            print(f"  {key}: {value[:50]}...")
                        else:
                            print(f"  {key}: {value}")
                elif isinstance(data, str) and len(data) > 100:
                    print(f"\n🔍 Decrypted text preview:")
                    print(f"  {data[:200]}...")
        
        print(f"\n🎉 SUCCESS! Check the decryption_results.json file for complete data.")
        print(f"Look for 'mnemonic' or 'seed' fields which contain your recovery phrase.")
        
    else:
        print(f"❌ No successful decryptions found.")
        print(f"This could mean:")
        print(f"  1. The password is incorrect")
        print(f"  2. The data is not MetaMask vault data")
        print(f"  3. The data is corrupted")
        print(f"  4. Different encryption method is used")

if __name__ == "__main__":
    main()
