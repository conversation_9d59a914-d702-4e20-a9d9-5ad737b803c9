#!/usr/bin/env python3
"""
MetaMask Vault Data Extractor
Extracts vault data in the correct format for the official MetaMask vault decryptor
Based on the official MetaMask recovery guide
"""

import os
import re
import json
from typing import List, Dict, Any, Optional

def extract_vault_from_ldb(filepath: str) -> List[Dict[str, Any]]:
    """Extract vault data from .ldb file following MetaMask official format"""
    print(f"Analyzing {filepath} for MetaMask vault data...")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    # Convert to text for searching
    try:
        text = data.decode('utf-8', errors='ignore')
    except:
        return []
    
    vault_data_list = []
    
    # Look for "keyring" or "KeyringController" as mentioned in the official guide
    keyring_patterns = [
        r'keyring[^{]*(\{[^}]*"data"[^}]*"iv"[^}]*"salt"[^}]*\})',
        r'KeyringController[^{]*(\{[^}]*"data"[^}]*"iv"[^}]*"salt"[^}]*\})',
        r'vault[^{]*(\{[^}]*"data"[^}]*"iv"[^}]*"salt"[^}]*\})',
    ]
    
    for pattern in keyring_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        for match in matches:
            try:
                # Clean up the match - remove escape characters as per official guide
                cleaned_match = match.replace('\\', '')
                
                # Try to parse as JSON
                vault_obj = json.loads(cleaned_match)
                
                # Verify it has the required fields
                if all(key in vault_obj for key in ['data', 'iv', 'salt']):
                    vault_data_list.append({
                        'source_file': filepath,
                        'vault_data': vault_obj,
                        'raw_match': match,
                        'cleaned_match': cleaned_match
                    })
                    print(f"✅ Found valid vault structure in {filepath}")
                    
            except json.JSONDecodeError:
                # Try to fix common formatting issues
                try:
                    # Fix common issues mentioned in the official guide
                    fixed_match = fix_vault_format(match)
                    vault_obj = json.loads(fixed_match)
                    
                    if all(key in vault_obj for key in ['data', 'iv', 'salt']):
                        vault_data_list.append({
                            'source_file': filepath,
                            'vault_data': vault_obj,
                            'raw_match': match,
                            'cleaned_match': fixed_match,
                            'was_fixed': True
                        })
                        print(f"✅ Found and fixed vault structure in {filepath}")
                        
                except:
                    continue
    
    # Also look for the specific patterns mentioned in the official guide
    # Look for the pattern that starts with {"data" and contains vault data
    vault_pattern = r'\{"data"\s*:\s*"([^"]+)"\s*,\s*"iv"\s*:\s*"([^"]+)"\s*,\s*"salt"\s*:\s*"([^"]+)"\s*\}'
    vault_matches = re.findall(vault_pattern, text, re.IGNORECASE)
    
    for match in vault_matches:
        data_field, iv_field, salt_field = match
        vault_obj = {
            "data": data_field,
            "iv": iv_field,
            "salt": salt_field
        }
        
        vault_data_list.append({
            'source_file': filepath,
            'vault_data': vault_obj,
            'extraction_method': 'direct_pattern_match'
        })
        print(f"✅ Found vault data via direct pattern match in {filepath}")
    
    return vault_data_list

def fix_vault_format(vault_text: str) -> str:
    """Fix common formatting issues in vault data as per official guide"""
    # Remove escape characters
    fixed = vault_text.replace('\\', '')
    
    # Fix the data field if it starts with weird characters
    fixed = re.sub(r'\{"[^"]*"<[^>]*>"', '{"data"', fixed)
    
    # Ensure proper JSON format
    if not fixed.startswith('{"data"'):
        # Try to find where the actual data starts
        data_match = re.search(r'"data"\s*:', fixed)
        if data_match:
            start_pos = fixed.find('{', 0, data_match.start())
            if start_pos != -1:
                fixed = fixed[start_pos:]
    
    # Fix ending
    if fixed.endswith('=\\"}'):
        fixed = fixed[:-4] + '="}'
    elif fixed.endswith('=\\"}"}'):
        fixed = fixed[:-6] + '="}'
    
    return fixed

def search_all_ldb_files() -> List[Dict[str, Any]]:
    """Search all .ldb files for MetaMask vault data"""
    print("MetaMask Vault Data Extractor")
    print("=" * 50)
    
    # Get all .ldb files
    ldb_files = [f for f in os.listdir('.') if f.endswith('.ldb')]
    
    if not ldb_files:
        print("No .ldb files found in current directory")
        return []
    
    print(f"Found {len(ldb_files)} .ldb files to analyze:")
    for f in ldb_files:
        print(f"  - {f}")
    
    all_vault_data = []
    
    for ldb_file in ldb_files:
        vault_data = extract_vault_from_ldb(ldb_file)
        all_vault_data.extend(vault_data)
    
    return all_vault_data

def save_vault_data_for_decryptor(vault_data_list: List[Dict[str, Any]]):
    """Save vault data in format ready for MetaMask vault decryptor"""
    if not vault_data_list:
        print("❌ No vault data found to save")
        return
    
    print(f"\n{'='*60}")
    print("VAULT DATA EXTRACTION RESULTS")
    print(f"{'='*60}")
    
    for i, vault_entry in enumerate(vault_data_list):
        print(f"\nVault {i+1}:")
        print(f"  Source file: {vault_entry['source_file']}")
        print(f"  Data length: {len(vault_entry['vault_data']['data'])}")
        print(f"  IV length: {len(vault_entry['vault_data']['iv'])}")
        print(f"  Salt length: {len(vault_entry['vault_data']['salt'])}")
        
        # Save individual vault file
        vault_filename = f"vault_data_{i+1}.json"
        with open(vault_filename, 'w') as f:
            json.dump(vault_entry['vault_data'], f, indent=2)
        
        print(f"  ✅ Saved to: {vault_filename}")
        
        # Also save as text format for easy copy-paste
        vault_text_filename = f"vault_data_{i+1}.txt"
        with open(vault_text_filename, 'w') as f:
            f.write(json.dumps(vault_entry['vault_data']))
        
        print(f"  ✅ Text format saved to: {vault_text_filename}")
    
    # Save summary
    summary = {
        'total_vaults_found': len(vault_data_list),
        'vault_files': [f"vault_data_{i+1}.json" for i in range(len(vault_data_list))],
        'extraction_summary': vault_data_list
    }
    
    with open('vault_extraction_summary.json', 'w') as f:
        json.dump(summary, f, indent=2, default=str)
    
    print(f"\n📋 Summary saved to: vault_extraction_summary.json")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"1. Go to: https://metamask.github.io/vault-decryptor/")
    print(f"2. Copy the content from vault_data_1.txt (or other vault files)")
    print(f"3. Paste it into the vault decryptor")
    print(f"4. Enter your MetaMask password")
    print(f"5. Click 'Decrypt' to recover your seed phrase")
    
    # Show the vault data for easy copy-paste
    if vault_data_list:
        print(f"\n📋 VAULT DATA FOR COPY-PASTE:")
        print(f"Copy this exact text into the MetaMask vault decryptor:")
        print(f"{'='*60}")
        print(json.dumps(vault_data_list[0]['vault_data']))
        print(f"{'='*60}")

def main():
    """Main function"""
    vault_data = search_all_ldb_files()
    save_vault_data_for_decryptor(vault_data)
    
    if not vault_data:
        print(f"\n❌ No MetaMask vault data found in the .ldb files.")
        print(f"This could mean:")
        print(f"  1. The files are not from MetaMask extension storage")
        print(f"  2. The files are corrupted")
        print(f"  3. The vault data is in a different format")
        print(f"  4. These files are from a different browser extension")
        
        print(f"\n💡 Try:")
        print(f"  1. Check if you have the correct .ldb files from MetaMask")
        print(f"  2. Look for files with lower numbers (000003.ldb, 000004.ldb, etc.)")
        print(f"  3. Check different browser profiles if you used multiple")

if __name__ == "__main__":
    main()
