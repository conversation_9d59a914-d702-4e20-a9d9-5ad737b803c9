name: MetaMask Security Code Scanner

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
  workflow_dispatch:

jobs:
  run-security-scan:
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    steps:
      - name: MetaMask Security Code Scanner
        uses: MetaMask/Security-Code-Scanner@main
        with:
          repo: ${{ github.repository }}
          paths_ignored: |
            .storybook/
            '**/__snapshots__/'
            '**/*.snap'
            '**/*.stories.js'
            '**/*.stories.tsx'
            '**/*.test.browser.ts*'
            '**/*.test.js*'
            '**/*.test.ts*'
            '**/fixtures/'
            '**/jest.config.js'
            '**/jest.environment.js'
            '**/mocks/'
            '**/test*/'
            docs/
            e2e/
            merged-packages/
            node_modules
            storybook/
            test*/
          rules_excluded: example
          project_metrics_token: ${{ secrets.SECURITY_SCAN_METRICS_TOKEN }}
          slack_webhook: ${{ secrets.APPSEC_BOT_SLACK_WEBHOOK }}
