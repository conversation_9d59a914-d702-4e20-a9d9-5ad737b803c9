#!/usr/bin/env python3
"""
Official Guide Vault Extractor
Extracts vault data following the exact steps from MetaMask's official guide
"""

import os
import re
import json
from typing import List, Dict, Any, Optional

def search_for_keyring_data(filepath: str) -> List[Dict[str, Any]]:
    """Search for keyring data following the official guide steps"""
    print(f"\n=== Searching {filepath} for 'keyring' data ===")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    # Try different encodings as mentioned in the guide
    text = None
    encodings = ['utf-8', 'latin-1', 'cp1252']
    
    for encoding in encodings:
        try:
            text = data.decode(encoding, errors='ignore')
            break
        except:
            continue
    
    if not text:
        print(f"Could not decode {filepath}")
        return []
    
    vault_candidates = []
    
    # Step 1: Search for "keyring" (case insensitive)
    keyring_positions = []
    for match in re.finditer(r'keyring', text, re.IGNORECASE):
        keyring_positions.append(match.start())
        print(f"Found 'keyring' at position {match.start()}")
    
    if not keyring_positions:
        print(f"No 'keyring' found in {filepath}")
        return []
    
    # Step 2: For each keyring position, look for the vault data pattern
    for pos in keyring_positions:
        # Look for the pattern {\"???'<0x04>\" or similar after keyring
        # The guide mentions it starts with a curly bracket after "keyring"
        
        # Extract a reasonable chunk of text after "keyring"
        chunk = text[pos:pos+10000]  # Look at next 10KB
        
        # Look for the start pattern: {\" followed by some characters
        start_patterns = [
            r'\{\\?"[^"]*<0x[0-9a-fA-F]+>',  # {\"???'<0x04>
            r'\{\\"[^"]*<0x[0-9a-fA-F]+>',   # {\"???'<0x04> with escaped quotes
            r'\{[^}]*"data"[^}]*"iv"[^}]*"salt"[^}]*\}',  # Direct vault pattern
        ]
        
        for pattern in start_patterns:
            matches = re.finditer(pattern, chunk)
            for match in matches:
                start_pos = pos + match.start()
                
                # Now look for the end pattern: salt followed by =\"}
                end_pattern = r'"salt"[^"]*"[^"]*"[^}]*=\\"?\}'
                end_match = re.search(end_pattern, text[start_pos:start_pos+5000])
                
                if end_match:
                    end_pos = start_pos + end_match.end()
                    raw_vault_data = text[start_pos:end_pos]
                    
                    print(f"Found potential vault data from pos {start_pos} to {end_pos}")
                    print(f"Raw data preview: {raw_vault_data[:100]}...")
                    
                    # Try to clean and extract the vault data
                    cleaned_vault = clean_vault_data(raw_vault_data)
                    if cleaned_vault:
                        vault_candidates.append({
                            'source_file': filepath,
                            'keyring_position': pos,
                            'vault_start': start_pos,
                            'vault_end': end_pos,
                            'raw_data': raw_vault_data,
                            'cleaned_data': cleaned_vault,
                            'extraction_method': 'official_guide_pattern'
                        })
                        print(f"✅ Successfully extracted and cleaned vault data")
    
    # Step 3: Also try a more general approach - look for any JSON-like structure after keyring
    for pos in keyring_positions:
        chunk = text[pos:pos+5000]
        
        # Look for any structure that has data, iv, and salt
        general_pattern = r'\{[^}]*"data"[^}]*"iv"[^}]*"salt"[^}]*\}'
        matches = re.finditer(general_pattern, chunk, re.DOTALL)
        
        for match in matches:
            raw_data = match.group()
            cleaned_vault = clean_vault_data(raw_data)
            
            if cleaned_vault:
                vault_candidates.append({
                    'source_file': filepath,
                    'keyring_position': pos,
                    'raw_data': raw_data,
                    'cleaned_data': cleaned_vault,
                    'extraction_method': 'general_pattern_after_keyring'
                })
                print(f"✅ Found vault data with general pattern after keyring")
    
    return vault_candidates

def clean_vault_data(raw_data: str) -> Optional[Dict[str, str]]:
    """Clean vault data following the official guide steps"""
    
    # Step 1: Fix the beginning - change {\"???'<0x04>\" to {"data"
    cleaned = raw_data
    
    # Replace the start pattern
    start_replacements = [
        (r'\{\\?"[^"]*<0x[0-9a-fA-F]+>\\?"', '{"data"'),
        (r'\{\\"[^"]*<0x[0-9a-fA-F]+>\\"', '{"data"'),
        (r'\{[^}]*"data"', '{"data"'),
    ]
    
    for pattern, replacement in start_replacements:
        if re.search(pattern, cleaned):
            cleaned = re.sub(pattern, replacement, cleaned, count=1)
            break
    
    # Step 2: Remove escape characters as mentioned in guide
    # Replace all \ with nothing (removing them)
    cleaned = cleaned.replace('\\', '')
    
    # Step 3: Fix the ending - should end with ="} not =\\"}\"}
    end_replacements = [
        (r'=\\?"\}"\}$', '="}'),
        (r'=\\?"\}$', '="}'),
        (r'=\}$', '="}'),
    ]
    
    for pattern, replacement in end_replacements:
        cleaned = re.sub(pattern, replacement, cleaned)
    
    # Step 4: Try to parse as JSON
    try:
        vault_json = json.loads(cleaned)
        
        # Verify it has the required fields
        if all(key in vault_json for key in ['data', 'iv', 'salt']):
            return vault_json
    except json.JSONDecodeError as e:
        print(f"JSON parsing failed: {e}")
        print(f"Cleaned data: {cleaned[:200]}...")
        
        # Try some additional cleaning
        # Sometimes there are extra characters
        additional_cleaning = [
            # Remove any non-printable characters except quotes and braces
            (r'[^\x20-\x7E{}",:]', ''),
            # Fix double quotes
            (r'""', '"'),
            # Fix missing quotes around keys
            (r'(\{|,)\s*([a-zA-Z]+)\s*:', r'\1"\2":'),
        ]
        
        for pattern, replacement in additional_cleaning:
            cleaned = re.sub(pattern, replacement, cleaned)
        
        try:
            vault_json = json.loads(cleaned)
            if all(key in vault_json for key in ['data', 'iv', 'salt']):
                return vault_json
        except:
            pass
    
    return None

def main():
    """Main extraction function following official guide"""
    print("MetaMask Vault Extractor - Following Official Guide")
    print("=" * 60)
    
    # Get all .ldb files
    ldb_files = [f for f in os.listdir('.') if f.endswith('.ldb')]
    
    if not ldb_files:
        print("No .ldb files found")
        return
    
    print(f"Searching {len(ldb_files)} .ldb files for 'keyring' data...")
    print("Following official guide steps:")
    print("1. Search for 'keyring'")
    print("2. Look for vault data pattern after keyring")
    print("3. Clean and format the data")
    
    all_vault_candidates = []
    
    for ldb_file in ldb_files:
        candidates = search_for_keyring_data(ldb_file)
        all_vault_candidates.extend(candidates)
    
    print(f"\n{'='*60}")
    print("EXTRACTION RESULTS")
    print(f"{'='*60}")
    
    print(f"Total vault candidates found: {len(all_vault_candidates)}")
    
    if all_vault_candidates:
        valid_vaults = []
        
        for i, candidate in enumerate(all_vault_candidates):
            print(f"\nCandidate {i+1}:")
            print(f"  Source: {candidate['source_file']}")
            print(f"  Method: {candidate['extraction_method']}")
            print(f"  Keyring position: {candidate['keyring_position']}")
            
            vault_data = candidate['cleaned_data']
            if vault_data:
                print(f"  ✅ Successfully cleaned vault data")
                print(f"  Data length: {len(vault_data['data'])}")
                print(f"  IV length: {len(vault_data['iv'])}")
                print(f"  Salt length: {len(vault_data['salt'])}")
                
                valid_vaults.append((i+1, vault_data))
                
                # Save this vault
                vault_filename = f"extracted_vault_{i+1}.json"
                with open(vault_filename, 'w') as f:
                    json.dump(vault_data, f, indent=2)
                
                vault_text_filename = f"extracted_vault_{i+1}.txt"
                with open(vault_text_filename, 'w') as f:
                    f.write(json.dumps(vault_data))
                
                print(f"  💾 Saved to: {vault_filename}")
                print(f"  📄 Text format: {vault_text_filename}")
            else:
                print(f"  ❌ Failed to clean vault data")
                print(f"  Raw data preview: {candidate['raw_data'][:100]}...")
        
        if valid_vaults:
            print(f"\n🎉 SUCCESS! Found {len(valid_vaults)} valid vault(s)!")
            
            # Show the first vault for copy-paste
            first_vault = valid_vaults[0][1]
            print(f"\n📋 VAULT DATA FOR METAMASK DECRYPTOR:")
            print(f"Copy this exact text:")
            print(f"{'='*60}")
            print(json.dumps(first_vault))
            print(f"{'='*60}")
            
            print(f"\n🎯 NEXT STEPS:")
            print(f"1. Go to: https://metamask.github.io/vault-decryptor/")
            print(f"2. Paste the vault data above into the 'Vault Data' field")
            print(f"3. Enter your MetaMask password")
            print(f"4. Click 'Decrypt' to recover your seed phrase")
            
        else:
            print(f"\n⚠️  Found vault candidates but couldn't clean them properly")
            
            # Save raw data for manual inspection
            raw_data_file = 'raw_vault_candidates.json'
            with open(raw_data_file, 'w') as f:
                json.dump(all_vault_candidates, f, indent=2, default=str)
            
            print(f"Raw candidates saved to: {raw_data_file}")
            print(f"You may need to manually clean the data following the official guide")
    
    else:
        print(f"\n❌ No vault data found following the official guide method")
        print(f"\nThis could mean:")
        print(f"  1. The .ldb files don't contain MetaMask data")
        print(f"  2. You need to look for 000003.ldb specifically (as mentioned in guide)")
        print(f"  3. The files are from a different browser or profile")
        print(f"  4. The data is corrupted")
        
        print(f"\n💡 Try:")
        print(f"  1. Look for a file named '000003.ldb' specifically")
        print(f"  2. Open the .ldb files in a text editor and search for 'keyring' manually")
        print(f"  3. Check different browser profiles")

if __name__ == "__main__":
    main()
