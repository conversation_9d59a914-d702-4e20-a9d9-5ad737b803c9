#!/usr/bin/env python3
"""
Advanced LevelDB Reader for MetaMask Data Recovery
Reads LevelDB files using proper binary parsing
"""

import os
import json
import struct
import re
from typing import Dict, List, Optional, Tuple, Any

class LevelDBReader:
    def __init__(self):
        self.results = []
    
    def read_varint(self, data: bytes, offset: int) -> Tuple[int, int]:
        """Read a variable-length integer from LevelDB format"""
        result = 0
        shift = 0
        pos = offset
        
        while pos < len(data):
            byte = data[pos]
            result |= (byte & 0x7F) << shift
            pos += 1
            if (byte & 0x80) == 0:
                break
            shift += 7
            if shift >= 64:
                break
        
        return result, pos
    
    def extract_strings(self, data: bytes) -> List[str]:
        """Extract all readable strings from binary data"""
        strings = []
        
        # Look for UTF-8 strings
        try:
            # Find sequences of printable characters
            text = data.decode('utf-8', errors='ignore')
            # Split by null bytes and other separators
            parts = re.split(r'[\x00-\x1f\x7f-\x9f]+', text)
            for part in parts:
                if len(part) > 10 and part.strip():  # Only meaningful strings
                    strings.append(part.strip())
        except:
            pass
        
        # Also try to find JSON-like patterns in raw bytes
        json_patterns = [
            rb'\{"[^"]*":[^}]*\}',
            rb'\{[^}]*"vault"[^}]*\}',
            rb'\{[^}]*"data"[^}]*\}',
            rb'\{[^}]*"KeyringController"[^}]*\}',
            rb'\{[^}]*"salt"[^}]*\}',
            rb'\{[^}]*"iv"[^}]*\}',
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, data, re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    decoded = match.decode('utf-8', errors='ignore')
                    if decoded and len(decoded) > 10:
                        strings.append(decoded)
                except:
                    continue
        
        return list(set(strings))  # Remove duplicates
    
    def find_metamask_indicators(self, strings: List[str]) -> List[Dict[str, Any]]:
        """Find strings that might contain MetaMask data"""
        metamask_data = []
        
        metamask_keywords = [
            'vault', 'keyring', 'metamask', 'seed', 'mnemonic', 
            'encryptionkey', 'salt', 'iv', 'data', 'cipher',
            'KeyringController', 'PreferencesController'
        ]
        
        for string in strings:
            string_lower = string.lower()
            
            # Check for MetaMask keywords
            found_keywords = [kw for kw in metamask_keywords if kw in string_lower]
            
            if found_keywords:
                # Try to parse as JSON
                json_data = None
                try:
                    # Clean up the string for JSON parsing
                    cleaned = string.strip()
                    if cleaned.startswith('{') and cleaned.endswith('}'):
                        json_data = json.loads(cleaned)
                except json.JSONDecodeError:
                    # Try to extract JSON from within the string
                    json_match = re.search(r'\{.*\}', cleaned, re.DOTALL)
                    if json_match:
                        try:
                            json_data = json.loads(json_match.group())
                        except:
                            pass
                
                metamask_data.append({
                    'raw_string': string,
                    'keywords_found': found_keywords,
                    'json_data': json_data,
                    'is_json': json_data is not None,
                    'length': len(string)
                })
        
        return metamask_data
    
    def analyze_ldb_file(self, filepath: str) -> Dict[str, Any]:
        """Analyze a single LDB file"""
        print(f"\n=== Analyzing {filepath} ===")
        
        try:
            with open(filepath, 'rb') as f:
                data = f.read()
        except Exception as e:
            print(f"Error reading {filepath}: {e}")
            return {'file': filepath, 'error': str(e)}
        
        file_size = len(data)
        print(f"File size: {file_size:,} bytes")
        
        # Extract strings
        strings = self.extract_strings(data)
        print(f"Extracted {len(strings)} strings")
        
        # Find MetaMask indicators
        metamask_data = self.find_metamask_indicators(strings)
        print(f"Found {len(metamask_data)} potential MetaMask entries")
        
        # Look for specific patterns in raw bytes
        vault_patterns = {
            'vault': data.count(b'vault'),
            'KeyringController': data.count(b'KeyringController'),
            'data': data.count(b'data'),
            'salt': data.count(b'salt'),
            'iv': data.count(b'iv'),
            'cipher': data.count(b'cipher'),
            'mnemonic': data.count(b'mnemonic'),
            'seed': data.count(b'seed')
        }
        
        pattern_count = sum(vault_patterns.values())
        if pattern_count > 0:
            print(f"Pattern occurrences: {vault_patterns}")
        
        return {
            'file': filepath,
            'size': file_size,
            'strings_count': len(strings),
            'metamask_data': metamask_data,
            'pattern_counts': vault_patterns,
            'total_patterns': pattern_count
        }
    
    def analyze_all_files(self) -> List[Dict[str, Any]]:
        """Analyze all LDB files in current directory"""
        print("Advanced LevelDB MetaMask Data Analyzer")
        print("=" * 50)
        
        # Get all relevant files
        files = []
        for f in os.listdir('.'):
            if f.endswith('.ldb') or f in ['CURRENT', 'MANIFEST-000001']:
                files.append(f)
        
        if not files:
            print("No LevelDB files found")
            return []
        
        print(f"Found {len(files)} files to analyze:")
        for f in files:
            print(f"  - {f}")
        
        results = []
        for filepath in files:
            result = self.analyze_ldb_file(filepath)
            results.append(result)
        
        return results
    
    def generate_summary(self, results: List[Dict[str, Any]]):
        """Generate summary of findings"""
        print("\n" + "=" * 60)
        print("ANALYSIS SUMMARY")
        print("=" * 60)
        
        total_metamask_entries = 0
        files_with_data = []
        best_candidates = []
        
        for result in results:
            if 'error' in result:
                continue
                
            metamask_count = len(result.get('metamask_data', []))
            pattern_count = result.get('total_patterns', 0)
            
            if metamask_count > 0 or pattern_count > 0:
                total_metamask_entries += metamask_count
                files_with_data.append(result['file'])
                
                score = metamask_count * 10 + pattern_count
                best_candidates.append((result['file'], score, metamask_count, pattern_count))
        
        # Sort by score
        best_candidates.sort(key=lambda x: x[1], reverse=True)
        
        print(f"Total files analyzed: {len(results)}")
        print(f"Files with potential MetaMask data: {len(files_with_data)}")
        print(f"Total MetaMask entries found: {total_metamask_entries}")
        
        if best_candidates:
            print(f"\nBest candidate files (ranked by relevance):")
            for i, (filename, score, entries, patterns) in enumerate(best_candidates[:5]):
                print(f"  {i+1}. {filename}")
                print(f"     - Score: {score}")
                print(f"     - MetaMask entries: {entries}")
                print(f"     - Pattern matches: {patterns}")
        
        # Show detailed data for top candidates
        if best_candidates:
            print(f"\nDetailed analysis of top candidate:")
            top_file = best_candidates[0][0]
            
            for result in results:
                if result['file'] == top_file and 'metamask_data' in result:
                    print(f"\n{top_file}:")
                    for i, entry in enumerate(result['metamask_data'][:3]):
                        print(f"  Entry {i+1}:")
                        print(f"    Keywords: {entry['keywords_found']}")
                        print(f"    Is JSON: {entry['is_json']}")
                        print(f"    Length: {entry['length']}")
                        if entry['is_json'] and entry['json_data']:
                            print(f"    JSON keys: {list(entry['json_data'].keys())}")
                        print(f"    Preview: {entry['raw_string'][:200]}...")
                        print()
        
        return best_candidates

def main():
    reader = LevelDBReader()
    results = reader.analyze_all_files()
    best_candidates = reader.generate_summary(results)
    
    # Save results
    output_file = 'leveldb_analysis_detailed.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\nDetailed results saved to: {output_file}")
    
    if best_candidates:
        print(f"\nNext steps:")
        print(f"1. Focus on file: {best_candidates[0][0]}")
        print(f"2. Extract vault data from this file")
        print(f"3. Attempt decryption with your password")

if __name__ == "__main__":
    main()
