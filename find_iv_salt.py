#!/usr/bin/env python3
"""
Find IV and Salt for the promising data field
"""

import os
import re
import json
import base64

def search_around_position(filepath: str, position: int, search_window: int = 5000):
    """Search for IV and salt around a specific position"""
    print(f"Searching around position {position} in {filepath}")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    # Define search window
    start = max(0, position - search_window)
    end = min(len(data), position + search_window)
    window = data[start:end]
    
    # Convert to text for searching
    try:
        text = window.decode('utf-8', errors='ignore')
    except:
        return []
    
    findings = []
    
    # Look for IV patterns (typically 16 bytes = 24 base64 chars)
    iv_patterns = [
        r'"iv"\s*:\s*"([A-Za-z0-9+/=]{20,30})"',
        r'"iv":\s*"([A-Za-z0-9+/=]{20,30})"',
        r'iv["\']?\s*[:=]\s*["\']?([A-Za-z0-9+/=]{20,30})["\']?',
        r'([A-Za-z0-9+/=]{24})',  # Typical IV length
    ]
    
    for pattern in iv_patterns:
        matches = re.finditer(pattern, text, re.IGNORECASE)
        for match in matches:
            iv_candidate = match.group(1)
            try:
                # Verify it's valid base64
                decoded = base64.b64decode(iv_candidate)
                if 12 <= len(decoded) <= 20:  # Reasonable IV length
                    findings.append({
                        'type': 'IV_CANDIDATE',
                        'value': iv_candidate,
                        'decoded_length': len(decoded),
                        'position_in_window': match.start(),
                        'absolute_position': start + match.start(),
                        'distance_from_target': abs((start + match.start()) - position)
                    })
                    print(f"  Found IV candidate: {iv_candidate} (distance: {abs((start + match.start()) - position)})")
            except:
                continue
    
    # Look for Salt patterns (typically 32 bytes = 44 base64 chars)
    salt_patterns = [
        r'"salt"\s*:\s*"([A-Za-z0-9+/=]{40,50})"',
        r'"salt":\s*"([A-Za-z0-9+/=]{40,50})"',
        r'salt["\']?\s*[:=]\s*["\']?([A-Za-z0-9+/=]{40,50})["\']?',
        r'([A-Za-z0-9+/=]{44})',  # Typical salt length
    ]
    
    for pattern in salt_patterns:
        matches = re.finditer(pattern, text, re.IGNORECASE)
        for match in matches:
            salt_candidate = match.group(1)
            try:
                # Verify it's valid base64
                decoded = base64.b64decode(salt_candidate)
                if 28 <= len(decoded) <= 36:  # Reasonable salt length
                    findings.append({
                        'type': 'SALT_CANDIDATE',
                        'value': salt_candidate,
                        'decoded_length': len(decoded),
                        'position_in_window': match.start(),
                        'absolute_position': start + match.start(),
                        'distance_from_target': abs((start + match.start()) - position)
                    })
                    print(f"  Found salt candidate: {salt_candidate} (distance: {abs((start + match.start()) - position)})")
            except:
                continue
    
    return findings

def find_complete_vault_structure(filepath: str, data_positions: list):
    """Try to find complete vault structures around the data positions"""
    print(f"Looking for complete vault structures around data positions")
    
    try:
        with open(filepath, 'rb') as f:
            file_data = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []
    
    complete_vaults = []
    
    for pos in data_positions:
        print(f"\nSearching around data position {pos}")
        
        # Search in a larger window
        start = max(0, pos - 10000)
        end = min(len(file_data), pos + 10000)
        window = file_data[start:end]
        
        try:
            text = window.decode('utf-8', errors='ignore')
        except:
            continue
        
        # Look for complete vault structures
        vault_patterns = [
            # Standard format
            r'\{\s*"data"\s*:\s*"([A-Za-z0-9+/=]{400,600})"\s*,\s*"iv"\s*:\s*"([A-Za-z0-9+/=]{20,30})"\s*,\s*"salt"\s*:\s*"([A-Za-z0-9+/=]{40,50})"\s*\}',
            
            # Different field orders
            r'\{\s*"iv"\s*:\s*"([A-Za-z0-9+/=]{20,30})"\s*,\s*"data"\s*:\s*"([A-Za-z0-9+/=]{400,600})"\s*,\s*"salt"\s*:\s*"([A-Za-z0-9+/=]{40,50})"\s*\}',
            r'\{\s*"salt"\s*:\s*"([A-Za-z0-9+/=]{40,50})"\s*,\s*"data"\s*:\s*"([A-Za-z0-9+/=]{400,600})"\s*,\s*"iv"\s*:\s*"([A-Za-z0-9+/=]{20,30})"\s*\}',
            
            # With escape characters
            r'\{\\"data\\":\\"([A-Za-z0-9+/=]{400,600})\\",\\"iv\\":\\"([A-Za-z0-9+/=]{20,30})\\",\\"salt\\":\\"([A-Za-z0-9+/=]{40,50})\\"\}',
        ]
        
        field_orders = [
            ['data', 'iv', 'salt'],
            ['iv', 'data', 'salt'],
            ['salt', 'data', 'iv'],
            ['data', 'iv', 'salt']  # escaped version
        ]
        
        for i, pattern in enumerate(vault_patterns):
            matches = re.finditer(pattern, text, re.DOTALL)
            for match in matches:
                groups = match.groups()
                if len(groups) == 3:
                    order = field_orders[i]
                    vault_data = {}
                    for j, field in enumerate(order):
                        vault_data[field] = groups[j]
                    
                    complete_vaults.append({
                        'data_position': pos,
                        'vault_data': vault_data,
                        'match_position': start + match.start(),
                        'pattern_used': i + 1,
                        'raw_match': match.group()
                    })
                    print(f"  ✅ Found complete vault structure!")
                    print(f"     Data: {vault_data['data'][:50]}...")
                    print(f"     IV: {vault_data['iv']}")
                    print(f"     Salt: {vault_data['salt']}")
    
    return complete_vaults

def main():
    """Main function to find IV and salt"""
    print("Finding IV and Salt for Vault Data")
    print("=" * 50)
    
    target_file = "000005 1.ldb"
    
    # These are the positions where we found the promising data
    data_positions = [664909, 1724346]  # Positions of the 532-char base64 data
    
    if not os.path.exists(target_file):
        print(f"Target file {target_file} not found!")
        return
    
    # First, try to find complete vault structures
    complete_vaults = find_complete_vault_structure(target_file, data_positions)
    
    if complete_vaults:
        print(f"\n🎉 FOUND {len(complete_vaults)} COMPLETE VAULT STRUCTURE(S)!")
        
        for i, vault in enumerate(complete_vaults):
            print(f"\nComplete Vault {i+1}:")
            print(f"  Data position: {vault['data_position']}")
            print(f"  Match position: {vault['match_position']}")
            print(f"  Pattern used: {vault['pattern_used']}")
            
            vault_data = vault['vault_data']
            
            # Save this vault
            vault_filename = f"complete_vault_{i+1}.json"
            with open(vault_filename, 'w') as f:
                json.dump(vault_data, f, indent=2)
            
            vault_text_filename = f"complete_vault_{i+1}.txt"
            with open(vault_text_filename, 'w') as f:
                f.write(json.dumps(vault_data))
            
            print(f"  ✅ Saved to: {vault_filename}")
            print(f"  📄 Text format: {vault_text_filename}")
            
            # Show the vault for copy-paste
            print(f"\n📋 VAULT DATA FOR METAMASK DECRYPTOR:")
            print(f"{'='*60}")
            print(json.dumps(vault_data))
            print(f"{'='*60}")
        
        print(f"\n🎯 INSTRUCTIONS:")
        print(f"1. Go to: https://metamask.github.io/vault-decryptor/")
        print(f"2. Copy the vault data above")
        print(f"3. Paste it into the 'Vault Data' field")
        print(f"4. Enter your MetaMask password")
        print(f"5. Click 'Decrypt' to recover your seed phrase")
        
        return
    
    # If no complete structures found, search for individual components
    print(f"\nNo complete vault structures found. Searching for individual IV and salt...")
    
    all_findings = []
    
    for pos in data_positions:
        findings = search_around_position(target_file, pos)
        all_findings.extend(findings)
    
    # Group findings by type
    iv_candidates = [f for f in all_findings if f['type'] == 'IV_CANDIDATE']
    salt_candidates = [f for f in all_findings if f['type'] == 'SALT_CANDIDATE']
    
    print(f"\n{'='*60}")
    print("COMPONENT SEARCH RESULTS")
    print(f"{'='*60}")
    
    print(f"IV candidates found: {len(iv_candidates)}")
    print(f"Salt candidates found: {len(salt_candidates)}")
    
    if iv_candidates:
        print(f"\n🔑 IV CANDIDATES:")
        # Sort by distance from data positions
        iv_candidates.sort(key=lambda x: x['distance_from_target'])
        for i, iv in enumerate(iv_candidates[:5]):  # Show top 5
            print(f"  {i+1}. {iv['value']} (distance: {iv['distance_from_target']}, decoded: {iv['decoded_length']} bytes)")
    
    if salt_candidates:
        print(f"\n🧂 SALT CANDIDATES:")
        # Sort by distance from data positions
        salt_candidates.sort(key=lambda x: x['distance_from_target'])
        for i, salt in enumerate(salt_candidates[:5]):  # Show top 5
            print(f"  {i+1}. {salt['value']} (distance: {salt['distance_from_target']}, decoded: {salt['decoded_length']} bytes)")
    
    # Try to construct vault data with closest candidates
    if iv_candidates and salt_candidates:
        print(f"\n🔧 ATTEMPTING TO CONSTRUCT VAULT:")
        
        # Use the closest IV and salt to our data
        best_iv = iv_candidates[0]['value']
        best_salt = salt_candidates[0]['value']
        
        # Use our known good data
        vault_data = {
            "data": "2849236c3fab4d27c7026c1d4dcb2602646dec9751e763dba37bdf8ff9406ad9e530ee5db382f413001aeb06a53ed9027d831179727b0865a8918da3edbebcf9b14ed44ce6cbaced4bb1bdb7f1447e6cc254b332051512bd7af426fb8f401378cd2bf5983ca01c64b92ecf032ea15d1721d03f482d7ce6e74fef6d55e702f46980c82b5a84031900b1c9e59e7c97fbec7e8f323a97a7e36cc88be0f1d45b7ff585ac54bd407b22b4154aacc8f6d7ebf48e1d814cc5ed20f8037e0a79715eef29be32806a1d58bb7c5da76f550aa3d8a1fbff0eb19ccb1a313d55cda56c9ec2ef29632387fe8d76e3c0468043e8f663f4860ee12bf2d5b0b7474d6e694f91e6dcc4024fffffffffffffff",
            "iv": best_iv,
            "salt": best_salt
        }
        
        constructed_filename = "constructed_vault.json"
        with open(constructed_filename, 'w') as f:
            json.dump(vault_data, f, indent=2)
        
        constructed_text_filename = "constructed_vault.txt"
        with open(constructed_text_filename, 'w') as f:
            f.write(json.dumps(vault_data))
        
        print(f"  ✅ Saved to: {constructed_filename}")
        print(f"  📄 Text format: {constructed_text_filename}")
        
        print(f"\n📋 CONSTRUCTED VAULT DATA:")
        print(f"{'='*60}")
        print(json.dumps(vault_data))
        print(f"{'='*60}")
        
        print(f"\n⚠️  NOTE: This is a best guess construction.")
        print(f"Try this in the MetaMask vault decryptor, but if it doesn't work,")
        print(f"you may need to try different IV/salt combinations.")
    
    # Save all findings
    with open('iv_salt_search_results.json', 'w') as f:
        json.dump({
            'iv_candidates': iv_candidates,
            'salt_candidates': salt_candidates,
            'data_positions': data_positions
        }, f, indent=2, default=str)
    
    print(f"\nAll findings saved to: iv_salt_search_results.json")

if __name__ == "__main__":
    main()
